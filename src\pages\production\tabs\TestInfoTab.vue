<template>
  <view class="tab-pane">
    <view class="info-card">
      <view class="card-body">
        <view v-if="testInfo.length === 0" class="empty-tip">暂无测试信息</view>
        <view v-else class="test-list">
          <view v-for="(item, index) in testInfo" :key="index" class="test-item">
            <view class="test-header">
              <text class="test-title">{{ item.testNo || '未知测试' }}</text>
            </view>
            <view class="test-body">
              <view class="test-info">
                <text class="test-label">测试项目</text>
                <text class="test-value">{{ item.testItem || '-' }}</text>
              </view>
              <view class="test-info">
                <text class="test-label">测试结果</text>
                <text class="test-value">{{ item.result || '-' }}</text>
              </view>
              <view class="test-info">
                <text class="test-label">测试时间</text>
                <text class="test-value">{{ item.testTime || '-' }}</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
// 定义props接收父组件传递的数据
const props = defineProps({
  testInfo: {
    type: Array,
    default: () => []
  }
});
</script>

<style scoped>
/* 卡片样式 */
.info-card {
  background-color: #ffffff;
  border-radius: 10px;
  margin-bottom: 10px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.card-body {
  padding: 15px;
}

/* 空数据提示 */
.empty-tip {
  text-align: center;
  padding: 30px 0;
  color: #9ca3af;
  font-size: 14px;
}

/* 测试信息样式 */
.test-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.test-item {
  background-color: #f8fafc;
  border-radius: 8px;
  padding: 12px;
  border: 1px solid #e2e8f0;
}

.test-header {
  margin-bottom: 8px;
  padding-bottom: 6px;
  border-bottom: 1px solid #e2e8f0;
}

.test-title {
  font-size: 14px;
  font-weight: 600;
  color: #0f172a;
}

.test-body {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
}

.test-info {
  display: flex;
  flex-direction: column;
}

.test-label {
  font-size: 11px;
  color: #64748b;
  margin-bottom: 2px;
}

.test-value {
  font-size: 13px;
  color: #334155;
}

.tab-pane {
  height: 100%;
}
</style> 
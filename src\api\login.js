import http from '@/utils/request'

//登录
export const login = (params) => {
	const Data = JSON.stringify(params);
  return http.post("Service/LoginAjax.ashx?OP=SDLogin", {
		Data
	});
};

// 获取个人信息 
export const getUserInfo = () => {
  return http.get("Service/LoginAjax.ashx?OP=JustRight&sFlag=10&CorpID=we");
};

// 获取用户详细信息
export const getUserDetailInfo = (params) => {
	const Data = JSON.stringify(params);
  return http.post("Service/BaseModuleAjax.ashx?OP=GetUserInfoByNo&CFlag=10-99", {
		Data
	});
};

// 清空缓存
export const clearSession = () => {
  return http.post("Service/LoginAjax.ashx?OP=ClearSession&CFlag=1&CorpID=we");
};

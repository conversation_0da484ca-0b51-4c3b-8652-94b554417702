/**
 * 音频播放辅助工具
 * 用于在操作成功或失败时播放相应的音效
 */

// 音频文件路径
const AUDIO_PATHS = {
  SUCCESS: '/static/imges/success.mp3',
  ERROR: '/static/imges/error.mp3'
};

// 音频对象缓存
let audioCache = {};

/**
 * 预加载音频文件
 * @returns {Promise} 返回加载完成的Promise
 */
export const preloadAudios = () => {
  return new Promise((resolve) => {
    // 创建音频对象
    const successAudio = uni.createInnerAudioContext();
    successAudio.src = AUDIO_PATHS.SUCCESS;
    successAudio.autoplay = false;
    
    const errorAudio = uni.createInnerAudioContext();
    errorAudio.src = AUDIO_PATHS.ERROR;
    errorAudio.autoplay = false;
    
    // 缓存音频对象
    audioCache.success = successAudio;
    audioCache.error = errorAudio;
    
    // 简单延迟确保加载完成
    setTimeout(() => {
      resolve();
    }, 500);
  });
};

/**
 * 播放成功音效
 */
export const playSuccessAudio = () => {
  if (!audioCache.success) {
    const audio = uni.createInnerAudioContext();
    audio.src = AUDIO_PATHS.SUCCESS;
    audio.autoplay = true;
    audioCache.success = audio;
  } else {
    audioCache.success.stop();
    audioCache.success.play();
  }
};

/**
 * 播放错误音效
 */
export const playErrorAudio = () => {
  if (!audioCache.error) {
    const audio = uni.createInnerAudioContext();
    audio.src = AUDIO_PATHS.ERROR;
    audio.autoplay = true;
    audioCache.error = audio;
  } else {
    audioCache.error.stop();
    audioCache.error.play();
  }
};

/**
 * 释放音频资源
 */
export const destroyAudios = () => {
  if (audioCache.success) {
    audioCache.success.destroy();
  }
  
  if (audioCache.error) {
    audioCache.error.destroy();
  }
  
  audioCache = {};
};

export default {
  preloadAudios,
  playSuccessAudio,
  playErrorAudio,
  destroyAudios
}; 
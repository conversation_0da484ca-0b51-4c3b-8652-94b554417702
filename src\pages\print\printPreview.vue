<template>
  <view class="print-preview-page">
    <!-- 状态栏占位 -->
    <!-- #ifdef APP-PLUS -->
    <view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>
    <!-- #endif -->
    
    <!-- 顶部标题栏 -->
    <view class="header">
      <view class="back-btn" @click="goBack">
        <image class="back-icon" src="/static/icons/return.png"></image>
      </view>
      <text class="header-title">打印预览</text>
      <view class="placeholder"></view>
    </view>
    
    <view class="main-content">
      <!-- 打印联次选择器 -->
      <view class="print-copies-selector" style="display:none">
        <view class="copies-buttons">
          <view 
            class="copy-button" 
            :class="{ active: selectedCopy === 1 }"
            @click="selectCopy(1)"
          >
            <text>客户留存</text>
          </view>
          <view 
            class="copy-button" 
            :class="{ active: selectedCopy === 2 }"
            @click="selectCopy(2)"
          >
            <text>公司留存</text>
          </view>
        </view>
      </view>
      
      <!-- 打印模板预览区 -->
      <view class="print-preview-wrapper">
        <view class="preview-page-indicator" v-if="totalPages > 1">第{{ currentPage }}/{{ totalPages }}页</view>
        <view class="print-preview-container">
          <view class="print-preview">
            <PrintTemplate :orderData="currentPageData" :pageNumber="currentPage" :totalPages="totalPages" ref="printTemplateRef" />
          </view>
        </view>
      </view>

      <!-- 隐藏的Canvas，用于绘制打印内容 -->
      <canvas canvas-id="printCanvas" class="hidden-canvas" id="printCanvas" type="2d"></canvas>
      
      <!-- 固定在底部按钮上方的分页选择器 -->
      <view class="fixed-page-selector" v-if="totalPages > 1">
        <scroll-view
          class="page-scroll-view"
          scroll-x="true"
          :scroll-left="scrollLeft"
          show-scrollbar="false"
        >
          <view class="page-scroll-content">
            <view
              v-for="page in totalPages"
              :key="page"
              :class="['fixed-page-button', currentPage === page ? 'active' : '']"
              @click="selectPage(page)"
            >
              <text>{{ page }}</text>
            </view>
          </view>
        </scroll-view>
      </view>
      
      <!-- 底部操作按钮 -->
      <view class="bottom-actions">
        <view class="bottom-btn cancel-btn" hover-class="btn-hover" @click="goBack">
          <text>返回</text>
        </view>
        <view class="bottom-btn print-all-btn" hover-class="btn-hover" @click="printAllPages">
          <text>全部打印</text>
        </view>
        <view class="bottom-btn print-btn" hover-class="btn-hover" @click="printDocument">
          <text>{{ printButtonText }}</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted, computed, onBeforeUnmount, defineExpose, nextTick } from 'vue';
import PrintTemplate from './printTemplate.vue';
// 导入印立方SDK
import { LPAPIFactory } from 'lpapi-ble-uni';

// 订单数据
const orderData = ref({
  orderNo: '',
  clientName: '',
  contact: '',
  contactPhone: '',
  clientAddress: '',
  createTime: '',
  createdBy: '',
  orderGroupList: []
});

// 当前选择的页码
const currentPage = ref(1);

// 分页滚动控制
const scrollLeft = ref(0);

// 初始化滚动位置
const initScrollPosition = () => {
  // 页面加载时，确保当前页在可视区域内
  selectPage(currentPage.value);
};

// 计算总页数 - 确保每页8条数据
const totalPages = computed(() => {
  if (!orderData.value.deliveryDetail || orderData.value.deliveryDetail.length === 0) {
    return 1;
  }
  return Math.ceil(orderData.value.deliveryDetail.length / 8);
});

// 计算当前页的数据
const currentPageData = computed(() => {
  if (!orderData.value.deliveryDetail || orderData.value.deliveryDetail.length === 0) {
    return orderData.value;
  }
  
  // 复制原始数据
  const pageData = JSON.parse(JSON.stringify(orderData.value));
  
  // 分页处理后的数据
  let pagedDetails = [];
  
  // 分页处理 - 每页固定显示8条数据
  const startIndex = (currentPage.value - 1) * 8;
  pagedDetails = pageData.deliveryDetail.slice(startIndex, startIndex + 8);
  
  // 确保保留原始的ID值，不要根据新索引重新编号
  pagedDetails = pagedDetails.map(item => ({
    ...item,
    // 确保ID字段值保持不变，如果没有则使用索引+1
    ID: item.ID !== null && item.ID !== undefined ? item.ID : null
  }));
  
  // 如果当前页数据为空但总页数大于当前页，可能是页码超出范围
  if (pagedDetails.length === 0 && currentPage.value > 1) {
    currentPage.value = Math.min(currentPage.value - 1, totalPages.value);
    return currentPageData.value; // 递归调用
  }
  
  // 替换明细列表为当前页的数据
  pageData.deliveryDetail = pagedDetails;
  
  return pageData;
});

// 获取按产品名称和规格型号排序的商品列表 - 与printTemplate.vue完全一致的排序逻辑
const getSortedOrderGroups = (groups) => {
  if (!groups || groups.length === 0) {
    return [];
  }
  
  // 复制数组避免直接修改原数据
  const items = [...groups];
  
  // 1. 首先按产品名称分组
  const groupsByProduct = {};
  items.forEach(item => {
    // 获取产品名称的关键部分作为分组依据
    const productKey = getProductKey(item);
    if (!groupsByProduct[productKey]) {
      groupsByProduct[productKey] = [];
    }
    groupsByProduct[productKey].push(item);
  });
  
  // 2. 对每个产品组内，按规格型号进一步分组
  const groupsByProductAndSpec = {};
  Object.keys(groupsByProduct).forEach(productKey => {
    const productItems = groupsByProduct[productKey];
    
    // 按规格型号分组
    productItems.forEach(item => {
    const spec = getProductSpec(item) || '';
      const combinedKey = `${productKey}|${spec}`;
      
      if (!groupsByProductAndSpec[combinedKey]) {
        groupsByProductAndSpec[combinedKey] = [];
      }
      groupsByProductAndSpec[combinedKey].push(item);
    });
  });
  
  // 3. 对每个产品+规格型号组内，按编码排序，然后按数量排序
  Object.keys(groupsByProductAndSpec).forEach(combinedKey => {
    groupsByProductAndSpec[combinedKey].sort((a, b) => {
      // 首先按编码排序（如果有的话）
      const codeA = a.groupCode || '';
      const codeB = b.groupCode || '';
      
      // 如果编码相同，则按数量排序
      if (codeA === codeB) {
      // 获取组数量
      const countA = parseInt(a.groupNum || a.groupCount || 0, 10);
      const countB = parseInt(b.groupNum || b.groupCount || 0, 10);
      
      // 降序排列，数量多的在前面
      return countB - countA;
      }
      
      // 编码不同，按编码升序排列（数字小的在前面）
      // 尝试提取编码中的数字部分进行比较
      const numA = parseInt(codeA.replace(/^\D+/, '') || '0', 10);
      const numB = parseInt(codeB.replace(/^\D+/, '') || '0', 10);
      
      if (!isNaN(numA) && !isNaN(numB)) {
        return numA - numB; // 编码数字小的在前面
      }
      
      // 如果无法提取数字或比较，则按原始编码字符串比较
      return codeA.localeCompare(codeB);
    });
  });
  
  // 4. 统计产品组的总数量和最小编码
  const productTotalCounts = {};
  const productMinCodes = {};
  
  Object.keys(groupsByProductAndSpec).forEach(combinedKey => {
    const [productKey, spec] = combinedKey.split('|');
    
    // 初始化产品键的数据（如果不存在）
    if (!productTotalCounts[productKey]) {
      productTotalCounts[productKey] = 0;
      productMinCodes[productKey] = Number.MAX_SAFE_INTEGER;
    }
    
    // 计算该产品+规格组的总数量
    const groupCount = groupsByProductAndSpec[combinedKey].reduce((sum, item) => {
      return sum + parseInt(item.groupNum || item.groupCount || 0, 10);
    }, 0);
    
    // 累加到产品总数量
    productTotalCounts[productKey] += groupCount;
    
    // 找出该组内的最小编码
    const codes = groupsByProductAndSpec[combinedKey]
      .map(item => item.groupCode || '')
      .filter(code => code);
    
    if (codes.length > 0) {
      // 尝试提取数字部分进行比较
      const numericCodes = codes.map(code => {
        const match = code.match(/(\d+)/);
        return match ? parseInt(match[1], 10) : Number.MAX_SAFE_INTEGER;
      });
      const minNumericCode = Math.min(...numericCodes);
      
      // 更新产品的最小编码（如果当前找到的编码更小）
      if (minNumericCode < productMinCodes[productKey]) {
        productMinCodes[productKey] = minNumericCode;
      }
    }
  });
  
  // 5. 按产品进行排序：先按最小编码，然后按总数量
  const sortedProducts = Object.keys(productTotalCounts).sort((a, b) => {
    // 首先比较最小编码
    const codeCompare = productMinCodes[a] - productMinCodes[b];
    
    // 如果编码比较结果不为0（即不相等），则返回编码比较结果
    if (codeCompare !== 0) {
      return codeCompare;
    }
    
    // 编码相同或不存在，则按总数量降序排序
    return productTotalCounts[b] - productTotalCounts[a];
  });
  
  // 6. 组合排序后的产品和规格型号组，按顺序合并为最终结果
  const result = [];
  
  sortedProducts.forEach(productKey => {
    // 找出所有属于该产品的产品+规格组
    const relatedGroups = Object.keys(groupsByProductAndSpec)
      .filter(key => key.startsWith(`${productKey}|`));
    
    // 对产品内的规格型号组进行排序
    relatedGroups.sort((a, b) => {
      // 提取规格型号部分
      const specA = a.split('|')[1];
      const specB = b.split('|')[1];
      
      // 找出组内的最小编码
      const minCodeA = Math.min(...groupsByProductAndSpec[a]
        .map(item => {
          const code = item.groupCode || '';
          const match = code.match(/(\d+)/);
          return match ? parseInt(match[1], 10) : Number.MAX_SAFE_INTEGER;
        }));
      
      const minCodeB = Math.min(...groupsByProductAndSpec[b]
        .map(item => {
          const code = item.groupCode || '';
          const match = code.match(/(\d+)/);
          return match ? parseInt(match[1], 10) : Number.MAX_SAFE_INTEGER;
        }));
      
      // 首先按最小编码排序
      if (minCodeA !== minCodeB) {
        return minCodeA - minCodeB;
      }
      
      // 编码相同，按规格型号组内总数量排序
      const countA = groupsByProductAndSpec[a].reduce((sum, item) => {
        return sum + parseInt(item.groupNum || item.groupCount || 0, 10);
      }, 0);
      
      const countB = groupsByProductAndSpec[b].reduce((sum, item) => {
        return sum + parseInt(item.groupNum || item.groupCount || 0, 10);
      }, 0);
      
      return countB - countA; // 数量多的优先
    });
    
    // 将排序后的组内数据合并到结果中
    relatedGroups.forEach(groupKey => {
      result.push(...groupsByProductAndSpec[groupKey]);
    });
  });
  
  return result;
};

// 获取产品名称的关键部分用于分组
const getProductKey = (item) => {
  if (!item) return '';
  
  // 获取产品名称
  const fullName = item.productName || '';
  
  // 提取产品名称中的品牌/类型部分，比如"真黑金"、"爱玛迅航版"等
  // 通常这些是名称开头的非数字部分
  const brandMatch = fullName.match(/^([^\d]+)/);
  const productKey = brandMatch ? brandMatch[1].trim() : fullName;
  
  // 返回大写形式以便不区分大小写比较
  return productKey.toUpperCase();
};

// 提取产品规格型号
const getProductSpec = (item) => {
  if (!item) return '';
  
  // 获取完整产品名称
  const fullName = item.productName || '';
  
  // 优先使用productSpec字段
  let spec = item.productSpec || '';
  
  // 如果规格为空但有产品名称，尝试从产品名称中提取规格部分
  if (!spec && fullName) {
    // 提取产品名称中的型号部分，如"72V32An"
    const specMatch = fullName.match(/\s*(\d+V\d+A[^\s]*)/i);
    if (specMatch) {
      return specMatch[1].trim().toUpperCase();
    }
  }
  
  // 如果有规格信息，直接使用
  if (spec) {
    return spec.trim().toUpperCase();
  }
  
  // 如果没有匹配到规格信息，返回空字符串
  return '';
};

// 选择页面
const selectPage = (page) => {
  currentPage.value = page;

  // 计算滚动位置，确保当前页面在可视区域内
  const buttonWidth = 34; // 按钮宽度 + 间距
  const visibleButtons = 5; // 可见按钮数量

  // 计算当前页面应该在的位置
  let targetScrollLeft = 0;

  if (page > Math.floor(visibleButtons / 2) + 1) {
    // 如果页码大于可见区域的中间位置，需要滚动
    const centerPosition = Math.floor(visibleButtons / 2);
    targetScrollLeft = (page - centerPosition - 1) * buttonWidth;

    // 确保不超过最大滚动距离
    const maxScrollLeft = Math.max(0, (totalPages.value - visibleButtons) * buttonWidth);
    targetScrollLeft = Math.min(targetScrollLeft, maxScrollLeft);
  }

  scrollLeft.value = targetScrollLeft;
};

// 来源页面参数
const pageParams = ref({
  from: 'index', // 默认来源页面为index
  id: '',
  orderNo: ''
});

// 状态栏高度
const statusBarHeight = ref(20);

// 判断是否为竖屏
const isPortrait = computed(() => {
  return window.innerHeight > window.innerWidth;
});

// LP API实例
const lpapi = ref(null);
// 打印机列表
const printers = ref([]);
// 是否正在搜索设备
const isDiscovering = ref(false);
// 是否有搜索到的打印机
const hasPrinters = ref(false);
// 绘图上下文
const drawContext = ref(null);
// Canvas ID
const canvasId = 'printCanvas';
// 是否正在打印中
const printingInProgress = ref(false);
// 打印状态文本
const printStatus = ref('');
// 打印状态计时器
let printStatusTimer = null;
// 缓存的打印机设备
const cachedPrinters = ref([]);
// 最后连接的打印机ID
const lastConnectedPrinterId = ref('');
// 连接尝试次数
const connectionAttempts = ref(0);
// 最大连接尝试次数
const MAX_CONNECTION_ATTEMPTS = 3;
// 连接超时计时器
let connectionTimeoutTimer = null;

// 选中的打印联次
const selectedCopy = ref(1);

// 选择打印联次
const selectCopy = (copy) => {
  selectedCopy.value = copy;
};

// 修改打印按钮文本，移除联次信息
const printButtonText = computed(() => {
  return `打印第${currentPage.value}页`;
});

// 监听窗口尺寸变化
const handleResize = () => {
  // 仅在浏览器环境中调用 (H5)
  // #ifdef H5
  isPortrait.value = window.innerHeight > window.innerWidth;
  // #endif
};

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return '';
  
  const date = new Date(dateString);
  if (isNaN(date.getTime())) return dateString;
  
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  
  return `${year}-${month}-${day}`;
};

// 获取当前日期时间字符串
const getCurrentDateTime = () => {
  const now = new Date();
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, '0');
  const day = String(now.getDate()).padStart(2, '0');
  const hours = String(now.getHours()).padStart(2, '0');
  const minutes = String(now.getMinutes()).padStart(2, '0');
  const seconds = String(now.getSeconds()).padStart(2, '0');
  
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
};

// 初始化数据
const initData = () => {
  console.log("开始初始化订单数据...");
  
  // 从本地存储中获取打印数据
  uni.getStorage({
    key: 'printData',  // 更改为executeDeliver.vue中存储的键名
    success: function(res) {
      console.log("成功获取打印数据:", res.data);
      if (res.data) {
        try {
          // 解析数据，如果是字符串则进行parse
          const printData = typeof res.data === 'string' ? JSON.parse(res.data) : res.data;
          
          // 转换数据格式为模板所需格式
          orderData.value = {
            orderNo: printData.deliverNo || '',
            delivery: printData.shipNo ? [{
              DNo: printData.deliverNo || '',
              TrackingNo: printData.shipNo || '',
              BoxNo: printData.boxNo || ''
            }] : [],
                              deliveryDetail: Array.isArray(printData.items) ? printData.items.map(item => ({
                    ID: item.ID || null, // 使用大写ID，与后端数据结构保持一致
                    PName: item.name || '',
                    BatchSN: item.batchNo || '',
                    Qty: item.quantity || 0,
                    PrdBatch: item.productBatch || '', // 添加生产批号
                    PrdDate: item.productionDate || '', // 添加生产日期
                    EfftDate: item.expiryDate || '', // 添加使用期限
                    partSpec: item.partSpec || '', // 新增：部件规格型号
                    partName: item.partName || ''  // 新增：部件名称
                  })) : [],
            createdBy: printData.createdBy || ''
          };
          
          // 记录订单数据内容
          console.log("订单基本信息:", {
            orderNo: orderData.value.orderNo,
            delivery: orderData.value.delivery,
            deliveryDetail: orderData.value.deliveryDetail
          });
        } catch (error) {
          console.error("解析打印数据失败:", error);
          showError('解析打印数据失败');
        }
      } else {
        console.error("本地存储中没有找到打印数据");
        showError('没有找到打印数据');
      }
    },
    fail: function(err) {
      console.error("获取打印数据失败:", err);
      
      // 尝试重新获取数据
      setTimeout(() => {
        console.log("1秒后重试获取打印数据...");
        
        uni.getStorage({
          key: 'printData',  // 更改为executeDeliver.vue中存储的键名
          success: function(res) {
            console.log("重试成功获取打印数据");
            if (res.data) {
              try {
                // 解析数据，如果是字符串则进行parse
                const printData = typeof res.data === 'string' ? JSON.parse(res.data) : res.data;
                
                // 转换数据格式为模板所需格式
                orderData.value = {
                  orderNo: printData.deliverNo || '',
                  delivery: printData.shipNo ? [{
                    DNo: printData.deliverNo || '',
                    TrackingNo: printData.shipNo || '',
                    BoxNo: printData.boxNo || ''
                  }] : [],
                  deliveryDetail: Array.isArray(printData.items) ? printData.items.map(item => ({
                    ID: item.ID || null, // 使用大写ID，与后端数据结构保持一致
                    PName: item.name || '',
                    BatchSN: item.batchNo || '',
                    Qty: item.quantity || 0,
                    PrdBatch: item.productBatch || '', // 添加生产批号
                    PrdDate: item.productionDate || '', // 添加生产日期
                    EfftDate: item.expiryDate || '', // 添加使用期限
                    partSpec: item.partSpec || '', // 新增：部件规格型号
                    partName: item.partName || ''  // 新增：部件名称
                  })) : [],
                  createdBy: printData.createdBy || ''
                };
              } catch (error) {
                console.error("解析打印数据失败:", error);
                showError('解析打印数据失败');
              }
            } else {
              showError('重试获取打印数据失败');
            }
          },
          fail: function() {
            showError('获取打印数据失败');
          }
        });
      }, 1000);
    }
  });
};

// 显示错误提示
const showError = (message) => {
  uni.showToast({
    title: message,
    icon: 'none',
    duration: 2000
  });
  
  // 2秒后返回
  setTimeout(() => {
    goBack();
  }, 2000);
};

// 返回上一页
const goBack = () => {
  // 停止蓝牙搜索
  if (isDiscovering.value && lpapi.value) {
    lpapi.value.stopBleDiscovery();
    isDiscovering.value = false;
  }
  
  // 根据来源页面决定返回的页面
  switch (pageParams.value.from) {
    case 'addAfterSale':
      uni.navigateBack({
        delta: 1
      });
      break;
    case 'afterSaleItem':
      uni.navigateBack({
        delta: 1
      });
      break;
    default:
      uni.navigateBack({
        delta: 1
      });
  }
};

// onLoad生命周期函数，用于接收页面参数
function onLoad(options) {
  if (options) {
    // 如果URL参数中有from，则使用
    if (options.from) {
      pageParams.value.from = options.from;
    }
    
    // 如果URL参数中有id或orderNo，则保存
    if (options.id) {
      pageParams.value.id = options.id;
    }
    if (options.orderNo) {
      pageParams.value.orderNo = options.orderNo;
    }
  }
}

// 暴露onLoad方法，让uni-app能够调用
defineExpose({
  onLoad
});

// 初始化印立方SDK
const initLPAPI = () => {
  console.log("开始初始化LPAPI...");
  
  // 先创建LPAPI实例
  try {
    // 创建LPAPI实例
    console.log("尝试创建LPAPI实例...");
    lpapi.value = LPAPIFactory.getInstance({
      // 日志级别，0-4，0表示不显示调试信息，4表示显示所有调试信息
      showLog: 4,
      // 指定Canvas ID
      canvasId: canvasId,
      platformType: 3, // 明确指定为uni-app
      auto: true // 自动处理画布
    });
    
    if (!lpapi.value) {
      throw new Error("LPAPI实例创建返回空值");
    }
    
    console.log("LPAPI基础实例创建成功");
    
    // 立即尝试设置支持的打印机前缀
    try {
      lpapi.value.setSupportPrefixes("DP-30;DP30;DP-40;DP40;DP-50;DP50;Q3;D3");
      console.log("设置支持的打印机前缀成功");
    } catch (prefixErr) {
      console.warn("设置支持的打印机前缀失败:", prefixErr);
    }
    
    // 确保Canvas已准备好
    initCanvas();
  } catch (e) {
    console.error("初始化LPAPI失败:", e);
    uni.showToast({
      title: '初始化打印模块失败: ' + (e.message || '未知错误'),
      icon: 'none',
      duration: 2000
    });
    
    // 延迟1秒后重试
    setTimeout(() => {
      console.log("重新尝试初始化LPAPI...");
      initLPAPIRetry();
    }, 1000);
  }
};

// 备用初始化方法
const initLPAPIRetry = () => {
  try {
    // 尝试使用不同参数初始化
    console.log("使用备用方法初始化LPAPI...");
    
    // 先尝试清理之前可能的实例
    try {
      if (lpapi.value) {
        if (lpapi.value.isPrinterOpened()) {
          lpapi.value.closePrinter();
        }
        lpapi.value = null;
      }
    } catch (err) {
      console.warn("清理之前实例失败，但继续尝试:", err);
    }
    
    // 使用简单参数创建
    lpapi.value = LPAPIFactory.getInstance();
    
    if (!lpapi.value) {
      throw new Error("备用方法创建实例返回空值");
    }
    
    console.log("LPAPI实例创建成功(备用方法)");
    
    // 尝试重新初始化Canvas
    setTimeout(() => {
      try {
        // 检查Canvas元素
        const query = uni.createSelectorQuery();
        query.select('#' + canvasId).boundingClientRect(data => {
          if (!data) {
            console.error('备用初始化方法: Canvas元素不存在');
            requestBluetoothPermission(); // 无论Canvas问题，继续流程
            return;
          }
          
          console.log('备用初始化方法: Canvas元素存在');
          
          try {
            // 创建绘图上下文
            drawContext.value = lpapi.value.createDrawContext({
              canvasId: canvasId
            });
            
            // 设置绘图上下文
            lpapi.value.setDrawContext(drawContext.value);
            
            console.log("备用方法: Canvas上下文设置成功");
          } catch (err) {
            console.error("备用方法: 创建绘图上下文失败:", err);
          }
          
          // 继续蓝牙搜索流程
          requestBluetoothPermission();
        }).exec();
      } catch (canvasErr) {
        console.error("备用方法: Canvas初始化失败，但继续流程:", canvasErr);
        requestBluetoothPermission();
      }
    }, 500);
  } catch (e) {
    console.error("备用初始化LPAPI失败:", e);
    
    // 显示错误，但继续流程
    uni.showModal({
      title: '初始化失败',
      content: '打印模块初始化失败: ' + (e.message || '未知错误') + '。\n您仍可以继续使用其他功能。',
      showCancel: false,
      success: () => {
        // 即使失败也开始蓝牙流程
        setTimeout(() => {
          requestBluetoothPermission();
        }, 1000);
      }
    });
  }
};

// 初始化Canvas
const initCanvas = () => {
  console.log("初始化Canvas...");
  
  // 等待Canvas组件渲染完成
  nextTick(() => {
    setTimeout(() => {
      try {
        // 创建绘图上下文
        console.log("创建绘图上下文...");
        
        // 确保Canvas元素存在
        const query = uni.createSelectorQuery();
        query.select('#' + canvasId).boundingClientRect(data => {
          if (!data) {
            console.error('Canvas元素不存在!');
            requestBluetoothPermission(); // 即使Canvas有问题也要尝试继续流程
            return;
          }
          
          console.log('Canvas元素存在:', data);
          
          try {
            // 创建绘图上下文
            drawContext.value = lpapi.value.createDrawContext({
              canvasId: canvasId
            });
            
            // 设置绘图上下文
            lpapi.value.setDrawContext(drawContext.value);
            
            console.log("Canvas上下文设置成功");
            
            // 增加延迟，确保Canvas上下文完全准备好
            setTimeout(() => {
              requestBluetoothPermission();
            }, 1000); // 从500ms改为1000ms
          } catch (err) {
            console.error("创建绘图上下文失败:", err);
          }
          
          // 不再立即调用requestBluetoothPermission，而是等待延迟后调用
        }).exec();
        
      } catch (e) {
        console.error("初始化Canvas失败:", e);
        uni.showToast({
          title: '准备打印画布失败',
          icon: 'none',
          duration: 2000
        });
        
        // 尽管Canvas初始化失败，仍然继续流程
        requestBluetoothPermission();
      }
    }, 800); // 增加延迟时间，从500ms改为800ms，确保Canvas已渲染
  });
};

// 请求蓝牙权限
const requestBluetoothPermission = () => {
  // #ifdef APP-PLUS
  console.log("请求蓝牙权限...");
  
  // 请求蓝牙相关权限
  uni.authorize({
    scope: 'scope.bluetooth',
    success: () => {
      console.log("蓝牙权限授权成功");
      startBleDiscovery();
    },
    fail: (err) => {
      console.log("蓝牙权限授权失败:", err);
      // 尝试直接打开蓝牙
      uni.openBluetoothAdapter({
        success: () => {
          console.log("蓝牙适配器打开成功");
          startBleDiscovery();
        },
        fail: (error) => {
          console.error("蓝牙适配器打开失败:", error);
          uni.showModal({
            title: '蓝牙未启用',
            content: '请开启蓝牙和位置权限后重试',
            confirmText: '去设置',
            success: (res) => {
              if (res.confirm) {
                // 打开设置页面
                uni.openSetting({
                  success: (settingRes) => {
                    console.log('设置页面返回:', settingRes);
                    if (settingRes.authSetting && settingRes.authSetting['scope.bluetooth']) {
                      // 用户授权了，重新开始搜索
                      setTimeout(() => {
                        startBleDiscovery();
                      }, 1000);
                    }
                  }
                });
              }
            }
          });
        }
      });
    }
  });
  // #endif
  
  // #ifndef APP-PLUS
  // 非APP环境下直接开始搜索
  startBleDiscovery();
  // #endif
};

// 开始搜索蓝牙设备
const startBleDiscovery = () => {
  console.log("开始蓝牙搜索流程...");
  
  // 先检查是否有缓存的打印机设备
  const cachedDevice = loadCachedPrinter();
  if (cachedDevice) {
    console.log("发现缓存的打印机设备:", cachedDevice);
    // 将缓存设备添加到打印机列表
    if (!printers.value.some(p => p.deviceId === cachedDevice.deviceId)) {
      printers.value.push(cachedDevice);
    }
    
    // 尝试直接连接缓存的设备
    uni.showLoading({
      title: '连接上次使用的打印机...'
    });
    
    // 延迟一小段时间后尝试连接
    setTimeout(() => {
      connectToPrinter(cachedDevice);
      
      // 同时在后台继续搜索其他设备
      startBackgroundDiscovery();
    }, 300);
    
    return;
  }
  
  // 没有缓存设备，正常搜索流程
  startNormalDiscovery();
};

// 加载缓存的打印机
const loadCachedPrinter = () => {
  try {
    const cachedPrinterData = uni.getStorageSync('cachedPrinter');
    if (cachedPrinterData) {
      console.log("从缓存加载打印机信息:", cachedPrinterData);
      lastConnectedPrinterId.value = cachedPrinterData.deviceId;
      return cachedPrinterData;
    }
  } catch (e) {
    console.error("读取缓存打印机失败:", e);
  }
  return null;
};

// 保存打印机到缓存
const savePrinterToCache = (printer) => {
  try {
    if (printer && printer.deviceId) {
      uni.setStorageSync('cachedPrinter', printer);
      lastConnectedPrinterId.value = printer.deviceId;
      console.log("打印机信息已缓存:", printer);
    }
  } catch (e) {
    console.error("缓存打印机信息失败:", e);
  }
};

// 在后台继续搜索其他设备
const startBackgroundDiscovery = () => {
  // 先清空之前的搜索结果，但保留缓存设备
  const cachedDevice = printers.value.find(p => p.deviceId === lastConnectedPrinterId.value);
  printers.value = cachedDevice ? [cachedDevice] : [];
  
  // 停止之前可能的搜索
  try {
    if (isDiscovering.value && lpapi.value) {
      lpapi.value.stopBleDiscovery();
      isDiscovering.value = false;
    }
  } catch (e) {
    console.log("停止之前搜索出错:", e);
  }
  
  // 开始新的搜索，但不显示加载提示
  setTimeout(() => {
    isDiscovering.value = true;
    
    // 设置支持的打印机型号前缀
    try {
      lpapi.value.setSupportPrefixes("DP-30;DP30;DP-40;DP40;DP-50;DP50;Q3;D3");
    } catch (e) {
      console.log("设置打印机型号失败:", e);
    }
    
    // 清理蓝牙缓存并重新初始化
    uni.closeBluetoothAdapter({
      complete: () => {
        uni.openBluetoothAdapter({
          success: () => {
            console.log("蓝牙已重新初始化，开始后台搜索");
            
            // 开始搜索，超时时间更长
            lpapi.value.startBleDiscovery({
              models: "DP-30;DP30;DP-40;DP40;DP-50;DP50;Q3;D3",
              timeout: 10000, // 10秒超时
              deviceFound: onDeviceFound,
              discoveryStop: () => {
                isDiscovering.value = false;
                console.log("后台搜索完成");
              },
              fail: (err) => {
                console.error("后台蓝牙搜索失败:", err);
                isDiscovering.value = false;
              }
            });
          },
          fail: (err) => {
            console.error("重新打开蓝牙适配器失败:", err);
            isDiscovering.value = false;
          }
        });
      }
    });
  }, 500);
};

// 正常搜索流程
const startNormalDiscovery = () => {
  // 先清空之前的搜索结果
  printers.value = [];
  
  // 停止之前可能的搜索
  try {
    if (isDiscovering.value && lpapi.value) {
      console.log("停止之前的搜索...");
      lpapi.value.stopBleDiscovery();
      isDiscovering.value = false;
    }
  } catch (e) {
    console.log("停止搜索出错，可能是首次搜索:", e);
  }
  
  // 重新初始化打印机设备列表
  setTimeout(() => {
    uni.showLoading({
      title: '搜索打印机中...'
    });
    
    isDiscovering.value = true;
    
    // 设置支持的打印机型号前缀
    try {
      lpapi.value.setSupportPrefixes("DP-30;DP30;DP-40;DP40;DP-50;DP50;Q3;D3");
    } catch (e) {
      console.log("设置打印机型号失败:", e);
    }
    
    // 开始搜索前清理蓝牙缓存
    uni.closeBluetoothAdapter({
      complete: () => {
        // 重新打开蓝牙适配器
        uni.openBluetoothAdapter({
          success: () => {
            console.log("蓝牙已重新初始化");
            
            // 延迟200ms再开始搜索，避免蓝牙设备刚初始化完成的问题
            setTimeout(() => {
              console.log("开始搜索蓝牙设备...");
              
              // 开始搜索
              lpapi.value.startBleDiscovery({
                models: "DP-30;DP30;DP-40;DP40;DP-50;DP50;Q3;D3",
                timeout: 5000, // 5秒超时
                deviceFound: onDeviceFound,
                adapterStateChange: (state) => {
                  console.log("蓝牙适配器状态变化:", state);
                  if (state && state.available === false) {
                    uni.hideLoading();
                    uni.showModal({
                      title: '提示',
                      content: '蓝牙已关闭，请打开蓝牙后重试',
                      showCancel: false
                    });
                  }
                },
                discoveryStop: () => {
                  isDiscovering.value = false;
                  uni.hideLoading();
                  
                  if (printers.value.length === 0) {
                    console.log("未找到打印机，尝试切换搜索模式重试...");
                    // 尝试使用第二种搜索方式
                    trySecondSearchMethod();
                  } else {
                    hasPrinters.value = true;
                    // 如果已经找到打印机，尝试自动连接
                    if (printers.value.length > 0 && !lpapi.value.isPrinterOpened()) {
                      // 优先连接上次使用的打印机
                      const lastUsedPrinter = printers.value.find(p => p.deviceId === lastConnectedPrinterId.value);
                      connectToPrinter(lastUsedPrinter || printers.value[0]);
                    }
                  }
                },
                success: (res) => {
                  console.log("蓝牙搜索启动成功:", res);
                },
                fail: (err) => {
                  console.error("蓝牙搜索启动失败:", err);
                  isDiscovering.value = false;
                  uni.hideLoading();
                  
                  // 尝试使用第二种搜索方式
                  trySecondSearchMethod();
                }
              });
            }, 200);
          },
          fail: (err) => {
            console.error("重新打开蓝牙适配器失败:", err);
            isDiscovering.value = false;
            uni.hideLoading();
            
            // 显示更友好的错误提示
            let errorMsg = '无法打开蓝牙，请检查蓝牙是否可用';
            if (err && err.errCode) {
              switch (err.errCode) {
                case 10001:
                  errorMsg = '蓝牙未打开，请在系统设置中打开蓝牙';
                  break;
                case 10002:
                  errorMsg = '没有找到蓝牙设备，请确保蓝牙已开启';
                  break;
                case 10003:
                  errorMsg = '连接超时，请确保打印机已开机并在有效距离内';
                  break;
                case 10004:
                  errorMsg = '蓝牙连接已断开，请重新连接';
                  break;
                case 10005:
                  errorMsg = '未找到指定设备，请确保打印机已开机';
                  break;
                case 10008:
                  errorMsg = '蓝牙操作正在进行中，请稍后再试';
                  break;
                case 10009:
                  errorMsg = '系统蓝牙服务不可用，请重启手机';
                  break;
                case 10012:
                  errorMsg = '蓝牙连接超时，请确保打印机在有效距离内';
                  break;
                case 10013:
                  errorMsg = '蓝牙设备不支持此操作';
                  break;
              }
            }
            
            uni.showModal({
              title: '蓝牙异常',
              content: errorMsg,
              showCancel: false,
              success: () => {
                // 尝试使用第二种搜索方式
                setTimeout(() => {
                  trySecondSearchMethod();
                }, 1000);
              }
            });
          }
        });
      }
    });
  }, 100);
};

// 尝试第二种搜索方式
const trySecondSearchMethod = () => {
  if (!lpapi.value) return;
  
  uni.showLoading({
    title: '尝试第二种搜索方式...'
  });
  
  console.log("尝试使用通用搜索...");
  
  isDiscovering.value = true;
  
  // 使用系统API直接搜索蓝牙设备
  uni.startBluetoothDevicesDiscovery({
    allowDuplicatesKey: false,
    success: (res) => {
      console.log("系统蓝牙搜索启动成功:", res);
      
      // 监听蓝牙设备发现事件
      uni.onBluetoothDeviceFound((deviceRes) => {
        if (deviceRes && deviceRes.devices && deviceRes.devices.length > 0) {
          const devices = deviceRes.devices;
          
          // 过滤出可能是打印机的设备
          const possiblePrinters = devices.filter(device => {
            // 检查设备名称是否包含打印机型号的关键字
            const name = device.name || device.localName || '';
            return name && (
              name.includes('DP') || 
              name.includes('Q3') || 
              name.includes('D3') ||
              name.includes('Printer')
            );
          });
          
          if (possiblePrinters.length > 0) {
            console.log("找到可能的打印机设备:", possiblePrinters);
            
            // 将找到的设备添加到打印机列表
            possiblePrinters.forEach(device => {
              // 检查是否已经存在于列表中
              const exists = printers.value.some(p => p.deviceId === device.deviceId);
              if (!exists) {
                printers.value.push({
                  name: device.name || device.localName || '未命名打印机',
                  deviceId: device.deviceId,
                  RSSI: device.RSSI
                });
              }
            });
            
            // 停止搜索，连接找到的设备
            uni.stopBluetoothDevicesDiscovery({
              success: () => {
                isDiscovering.value = false;
                uni.hideLoading();
                
                // 如果已经找到打印机，尝试自动连接
                if (printers.value.length > 0 && !lpapi.value.isPrinterOpened()) {
                  connectToPrinter(printers.value[0]);
                }
              }
            });
          }
        }
      });
      
      // 5秒后停止搜索
      setTimeout(() => {
        if (isDiscovering.value) {
          uni.stopBluetoothDevicesDiscovery({
            complete: () => {
              isDiscovering.value = false;
              uni.hideLoading();
              
              if (printers.value.length === 0) {
                uni.showModal({
                  title: '未找到打印机',
                  content: '请确保打印机已开机并在有效距离内',
                  showCancel: false
                });
              } else {
                // 如果已经找到打印机，尝试自动连接
                if (!lpapi.value.isPrinterOpened()) {
                  connectToPrinter(printers.value[0]);
                }
              }
            }
          });
        }
      }, 5000);
    },
    fail: (err) => {
      console.error("系统蓝牙搜索启动失败:", err);
      isDiscovering.value = false;
      uni.hideLoading();
      uni.showModal({
        title: '蓝牙搜索失败',
        content: '无法搜索蓝牙设备，请检查权限设置',
        showCancel: false
      });
    }
  });
};

// 处理发现设备回调
const onDeviceFound = (devices) => {
  if (!devices || devices.length === 0) return;
  
  console.log("发现蓝牙设备:", devices);
  
  // 过滤设备，保留有效打印机设备
  const validDevices = devices.filter(device => {
    if (!device.name || !device.deviceId) return false;
    
    // 检查信号强度，过滤掉信号弱的设备
    // RSSI 通常是负数，数值越大(越接近0)表示信号越强
    const hasGoodSignal = !device.RSSI || device.RSSI > -85;
    
    // 检查设备名称是否为德佟打印机
    const isDotPrinter = device.name && (
      device.name.includes('DP') || 
      device.name.includes('Q3') || 
      device.name.includes('D3')
    );
    
    return hasGoodSignal && isDotPrinter;
  });
  
  if (validDevices.length === 0) return;
  
  // 按信号强度排序
  validDevices.sort((a, b) => (b.RSSI || -100) - (a.RSSI || -100));
  
  // 查找是否有新设备
  let hasNewDevices = false;
  
  // 更新打印机列表，避免重复添加
  validDevices.forEach(device => {
    // 检查设备是否已存在于列表中
    const existingIndex = printers.value.findIndex(p => p.deviceId === device.deviceId);
    
    if (existingIndex === -1) {
      // 新设备，添加到列表
      printers.value.push(device);
      hasNewDevices = true;
    } else {
      // 更新已有设备信息(例如信号强度)
      printers.value[existingIndex] = device;
    }
  });
  
  // 如果是新发现的设备，并且未连接打印机，尝试自动连接
  if (hasNewDevices && printers.value.length > 0 && !lpapi.value.isPrinterOpened()) {
    // 按照型号优先级自动连接
    // 优先级: DP-30S/DP30S > DP-40S/DP40S > 其他
    let priorityDevice = null;
    
    // 先查找DP-30S型号
    priorityDevice = printers.value.find(device => 
      device.name && (device.name.includes('DP-30S') || device.name.includes('DP30S'))
    );
    
    // 如果没找到DP-30S，尝试查找DP-40S
    if (!priorityDevice) {
      priorityDevice = printers.value.find(device => 
        device.name && (device.name.includes('DP-40S') || device.name.includes('DP40S'))
      );
    }
    
    // 如果没找到特定型号，使用信号最强的设备
    if (!priorityDevice && printers.value.length > 0) {
      priorityDevice = printers.value[0];
    }
    
    // 连接到选中的设备
    if (priorityDevice) {
      connectToPrinter(priorityDevice);
    }
  }
};

// 连接到打印机
const connectToPrinter = (printer) => {
  if (!lpapi.value || !printer) return;
  
  console.log("尝试连接打印机:", printer.name, printer.deviceId);
  
  // 记录连接尝试次数
  connectionAttempts.value++;
  
  // 停止搜索
  if (isDiscovering.value) {
    try {
      uni.stopBluetoothDevicesDiscovery();
      lpapi.value.stopBleDiscovery();
    } catch (e) {
      console.log("停止搜索出错:", e);
    }
    isDiscovering.value = false;
  }
  
  uni.showLoading({
    title: '连接打印机中...'
  });
  
  // 如果已经连接了打印机，先断开
  if (lpapi.value.isPrinterOpened()) {
    try {
      lpapi.value.closePrinter();
      console.log("已断开之前的打印机连接");
      // 短暂延迟后再连接新设备
      setTimeout(() => {
        connectPrinterDevice(printer);
      }, 300);
    } catch (e) {
      console.log("关闭之前的打印机连接出错:", e);
      // 继续尝试连接新设备
      connectPrinterDevice(printer);
    }
  } else {
    // 直接连接设备
    connectPrinterDevice(printer);
  }
};

// 实际连接打印机设备的函数
const connectPrinterDevice = (printer) => {
  // 清除之前的超时计时器
  if (connectionTimeoutTimer) {
    clearTimeout(connectionTimeoutTimer);
  }
  
  // 设置连接超时 - 根据尝试次数动态调整超时时间
  const timeoutDuration = 5000 + (connectionAttempts.value * 1000); // 5-8秒不等
  connectionTimeoutTimer = setTimeout(() => {
    uni.hideLoading();
    uni.showToast({
      title: '连接打印机超时',
      icon: 'none',
      duration: 2000
    });
    
    // 如果超过最大尝试次数，显示更详细的错误信息
    if (connectionAttempts.value >= MAX_CONNECTION_ATTEMPTS) {
    uni.showModal({
      title: '连接失败',
        content: '多次尝试连接打印机失败，请检查:\n1. 打印机是否开机\n2. 打印机是否在有效距离内\n3. 手机蓝牙是否正常',
        confirmText: '重新搜索',
        cancelText: '取消',
        success: (res) => {
          if (res.confirm) {
            // 重置连接尝试次数
            connectionAttempts.value = 0;
            // 重新开始搜索
            startNormalDiscovery();
          }
        }
      });
    } else {
      // 未超过最大尝试次数，显示重试按钮
      uni.showModal({
        title: '连接超时',
        content: `连接打印机超时(尝试${connectionAttempts.value}/${MAX_CONNECTION_ATTEMPTS})，是否重试？`,
      confirmText: '重试',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          // 用户点击重试
          connectToPrinter(printer);
        } else {
            // 用户点击取消，重置尝试次数并重新搜索
            connectionAttempts.value = 0;
            startNormalDiscovery();
        }
      }
    });
    }
  }, timeoutDuration);
  
  // 连接打印机
  lpapi.value.openPrinter({
    deviceId: printer.deviceId,
    name: printer.name,
    connectionStateChange: (state) => {
      console.log("打印机连接状态变化:", state);
      
      // 检查连接是否真的断开了 - 添加多重检查
      const checkRealDisconnection = () => {
        // 如果lpapi不存在，则确认断开
        if (!lpapi.value) return true;
        
        // 检查API报告的连接状态
        try {
          const isOpen = lpapi.value.isPrinterOpened();
          console.log("当前打印机连接状态检查:", isOpen);
          return !isOpen;
        } catch (e) {
          console.warn("检查打印机连接状态时出错:", e);
          // 出错视为已断开
          return true;
        }
      };
      
      // 如果打印机断开连接，尝试重新连接
      if (state && state.connected === false) {
        // 增加额外检查，确认连接确实已断开
        setTimeout(() => {
          // 再次检查连接状态，避免假断开
          if (checkRealDisconnection()) {
            // 在连接断开时尝试紧急停止走纸
            try {
              emergencyStopPrinter();
            } catch (e) {
              console.warn("连接断开时尝试停止走纸失败:", e);
            }
            
            // 只有在非打印流程中才提示重连，如果是打印过程中断开则静默处理
            if (!printingInProgress.value) {
        uni.showToast({
          title: '打印机连接断开',
          icon: 'none',
          duration: 1500
        });
        
              // 延迟一段时间后再次确认是否需要重连
        setTimeout(() => {
                // 再次检查连接状态，避免打印机已经重连成功但仍然提示重连
                if (checkRealDisconnection()) {
            // 显示重连对话框
            uni.showModal({
              title: '连接断开',
              content: '打印机连接已断开，是否重新连接？',
              confirmText: '重连',
              cancelText: '取消',
              success: (res) => {
                      // 在用户操作前再次检查连接状态
                      if (checkRealDisconnection()) {
                if (res.confirm) {
                          // 用户点击重连，重置连接尝试次数
                          connectionAttempts.value = 0;
                          // 重新连接
                  connectToPrinter(printer);
                        }
                      } else {
                        // 连接已恢复，取消弹窗
                        uni.showToast({
                          title: '打印机已连接',
                          icon: 'success',
                          duration: 1500
                        });
                }
              }
            });
                } else {
                  console.log("打印机已重新连接，不需要提示重连");
          }
        }, 1500);
            } else {
              // 如果是打印过程中断开，则只显示一个简短的提示
              // 清除状态计时器
              if (printStatusTimer) {
                clearTimeout(printStatusTimer);
                printStatusTimer = null;
              }
              
              uni.hideLoading();
              updatePrintStatus('打印完成');
            }
          } else {
            console.log("连接状态误报，打印机仍然连接中");
          }
        }, 300); // 等待300ms，确保状态已稳定
      }
    },
    success: (res) => {
      // 清除连接超时计时器
      if (connectionTimeoutTimer) {
        clearTimeout(connectionTimeoutTimer);
        connectionTimeoutTimer = null;
      }
      
      uni.hideLoading();
      console.log("打印机连接成功:", res);
      
      // 重置连接尝试次数
      connectionAttempts.value = 0;
      
      // 获取打印机信息
      try {
      const printerInfo = lpapi.value.getPrinterInfo();
      console.log("已连接打印机信息:", printerInfo);
      } catch (e) {
        console.warn("获取打印机信息失败:", e);
      }
      
      // 保存打印机到缓存
      savePrinterToCache(printer);
      
      uni.showToast({
        title: '打印机连接成功',
        icon: 'success',
        duration: 1500
      });
    },
    fail: (error) => {
      // 清除连接超时计时器
      if (connectionTimeoutTimer) {
        clearTimeout(connectionTimeoutTimer);
        connectionTimeoutTimer = null;
      }
      
      uni.hideLoading();
      console.error("打印机连接失败:", error);
      
      // 显示详细错误信息
      let errorMsg = '连接打印机失败';
      if (error) {
        if (error.errCode === 10003) {
          errorMsg = '找不到该设备，请确保打印机已开机';
        } else if (error.errCode === 10012) {
          errorMsg = '连接超时，请确保打印机在有效范围内';
        } else if (error.errCode === 10009) {
          errorMsg = '系统蓝牙服务不可用，请重启蓝牙';
        } else if (error.message) {
          errorMsg = error.message;
        }
      }
      
      // 根据尝试次数决定显示内容
      if (connectionAttempts.value >= MAX_CONNECTION_ATTEMPTS) {
      uni.showModal({
        title: '连接失败',
          content: `多次尝试连接失败(${connectionAttempts.value}/${MAX_CONNECTION_ATTEMPTS})。\n${errorMsg}\n\n建议检查打印机电源或重启蓝牙。`,
          confirmText: '重新搜索',
        cancelText: '取消',
          success: (res) => {
            if (res.confirm) {
              // 重置连接尝试次数
              connectionAttempts.value = 0;
              // 重新开始搜索
              startNormalDiscovery();
            }
          }
        });
      } else {
        uni.showModal({
          title: '连接失败',
          content: `${errorMsg} (尝试${connectionAttempts.value}/${MAX_CONNECTION_ATTEMPTS})`,
          confirmText: '重试',
          cancelText: '更换设备',
        success: (res) => {
          if (res.confirm) {
            // 用户点击重试
            setTimeout(() => {
              connectToPrinter(printer);
            }, 1000);
          } else {
              // 用户点击更换设备，显示设备列表
              if (printers.value.length > 1) {
                showPrinterSelector();
              } else {
                // 如果没有其他设备，重新搜索
                connectionAttempts.value = 0;
                startNormalDiscovery();
              }
          }
        }
      });
      }
    }
  });
};

// 显示打印机选择器
const showPrinterSelector = () => {
  if (printers.value.length === 0) {
    uni.showToast({
      title: '没有可用打印机',
      icon: 'none',
      duration: 2000
    });
    return;
  }
  
  // 按信号强度排序
  const sortedPrinters = [...printers.value].sort((a, b) => {
    return (b.RSSI || -100) - (a.RSSI || -100);
  });
  
  uni.showActionSheet({
    itemList: sortedPrinters.map(p => {
      const name = p.name || '未命名打印机';
      const signal = p.RSSI ? `(信号:${p.RSSI})` : '';
      return `${name} ${signal}`;
    }),
    success: (res) => {
      // 重置连接尝试次数
      connectionAttempts.value = 0;
      // 连接选中的打印机
      connectToPrinter(sortedPrinters[res.tapIndex]);
    },
    fail: () => {
      console.log('用户取消选择打印机');
    }
  });
};

// 执行打印操作
const printDocument = async () => {
  // 显示加载提示
  uni.showLoading({
    title: '准备打印...'
  });
  
  // #ifdef APP-PLUS
  // 检查LPAPI是否已初始化
  if (!lpapi.value) {
    console.log("打印模块未初始化，正在重新初始化...");
    
    // 重新初始化LPAPI
    initLPAPI();
    
    // 显示提示
    uni.hideLoading();
    uni.showToast({
      title: '正在初始化打印模块，请稍后再试',
      icon: 'none',
      duration: 2000
    });
    return Promise.reject(new Error('打印模块未初始化'));
  }
  
  // 检查是否有打印机连接
  if (!lpapi.value.isPrinterOpened()) {
    uni.hideLoading();
    
    // 判断是否有可用打印机
    if (printers.value.length > 0) {
      // 显示打印机选择对话框
      showPrinterSelector();
    } else {
      // 先检查是否有缓存的打印机
      const cachedDevice = loadCachedPrinter();
      if (cachedDevice) {
        uni.showModal({
          title: '连接打印机',
          content: '是否连接上次使用的打印机？',
          confirmText: '连接',
          cancelText: '重新搜索',
        success: (res) => {
            if (res.confirm) {
              // 重置连接尝试次数
              connectionAttempts.value = 0;
              // 连接缓存的打印机
              connectToPrinter(cachedDevice);
            } else {
              // 重新搜索
              startNormalDiscovery();
            }
        }
      });
    } else {
      // 没有可用打印机，重新搜索
      uni.showToast({
        title: '未连接打印机，正在重新搜索',
        icon: 'none',
        duration: 1500
      });
        startNormalDiscovery();
      }
    }
    return Promise.reject(new Error('未连接打印机'));
  }
  
  try {
    // 显示正在打印当前页的提示
    uni.showLoading({
      title: `正在打印第${currentPage.value}页...`
    });
    
    // 检查绘图上下文是否准备就绪
    if (!drawContext.value) {
      console.log("绘图上下文未准备好，正在重新创建...");
      
      try {
        // 重新创建绘图上下文
        drawContext.value = lpapi.value.createDrawContext({
          canvasId: canvasId
        });
        
        // 设置绘图上下文
        lpapi.value.setDrawContext(drawContext.value);
        
        console.log("绘图上下文创建成功");
        
        // 等待Canvas准备就绪
        await new Promise(resolve => setTimeout(resolve, 500)); // 从200ms改为500ms
      } catch (e) {
        console.error("创建绘图上下文失败:", e);
        throw new Error("创建打印画布失败: " + (e.message || "未知错误"));
      }
    }
    
    // 检查打印机状态
    try {
      const printerInfo = lpapi.value.getPrinterInfo();
      console.log("打印机状态:", printerInfo);
      
      if (!printerInfo) {
        console.warn("无法获取打印机信息，但继续尝试打印");
      }
    } catch (e) {
      console.warn("获取打印机信息出错，但继续尝试打印:", e);
    }
    
    console.log("开始创建打印任务...");
    
    // 开始一个新任务，设置标签宽度为80mm打印机
    let jobInfo = null;
    try {
      jobInfo = lpapi.value.startJob({
        width: 75, // 设置为80mm宽度的打印机(75像素宽度)
        height: 480, // 从520减少到480，减少单据高度
        orientation: 0, // 竖向打印
        jobName: "装箱清单" // 设置打印任务名称为装箱清单
      });
      
      if (!jobInfo) {
        throw new Error("创建打印任务返回空结果");
      }
      
      // 等待画布更新，加大延时确保画布准备好
      await new Promise(resolve => setTimeout(resolve, 3000)); // 从2000增加到3000ms
      
      // 手动更新画布尺寸
      try {
        lpapi.value.updateCanvas({
          width: 75,
          height: 480 // 从520减少到480，减少单据高度
        });
        // 等待画布更新
        await new Promise(resolve => setTimeout(resolve, 1000));
      } catch (e) {
        console.warn("更新画布尺寸失败:", e);
      }
    } catch (e) {
      console.error("创建打印任务时出错:", e);
      throw e; // 抛出错误以便上层捕获
    }
    
    console.log("打印任务创建成功, 画布尺寸:", jobInfo.canvas.width, "x", jobInfo.canvas.height);
    
    // 确保Canvas已经准备好
    await new Promise(resolve => setTimeout(resolve, 500)); // 增加等待时间从100到500
    
    // 绘制打印内容 - 完全按照图片样式，但适配75像素宽度
    // 设置文本水平居中
    lpapi.value.setItemHorizontalAlignment(1); // 居中
    
    // 标题 - 第一行：公司名称
    lpapi.value.drawText({
      text: "深圳市资福医疗技术有限公司",
      x: 16, // 标题位置
      y: 2, // 进一步减少上边距
      fontName: "黑体",
      fontHeight: 3.5, // 调整大小
      fontStyle: 1 // 1=加粗
    });
    
    // 标题 - 第二行：装箱清单
    lpapi.value.drawText({
      text: "装箱清单",
      x: 30, // 调整为画布中心位置
      y: 6, // 进一步减少间距，往上移
      fontName: "黑体",
      fontHeight: 3.0, // 减小字号
      fontStyle: 0 // 0=不加粗
    });
    
    // 重置对齐方式为左对齐
    lpapi.value.setItemHorizontalAlignment(0);
    
    // 第一条分隔线
    lpapi.value.drawLine({
      x1: 0.5,               // 左边距
      y1: 10,                // 进一步减少位置，往上移
      x2: 72.3,              // 右边距调整
      y2: 10,
      lineWidth: 0.3         // 细线条
    });
    
    // 发货单号、运单号、箱号放在同一行
    let yPos = 11; // 再次减少位置，往上移
    lpapi.value.setItemHorizontalAlignment(0); // 左对齐
    
    // 获取数据
    const deliverNo = currentPageData.value.orderNo || (currentPageData.value.delivery && currentPageData.value.delivery.length > 0 ? currentPageData.value.delivery[0].DNo : '');
    const trackingNo = currentPageData.value.delivery && currentPageData.value.delivery.length > 0 ? currentPageData.value.delivery[0].TrackingNo : '';
    const boxNo = currentPageData.value.delivery && currentPageData.value.delivery.length > 0 ? currentPageData.value.delivery[0].BoxNo : '';
    
    // 在同一行显示所有信息，增加间距
    lpapi.value.drawText({
      text: `发货单号: ${deliverNo}   运单号: ${trackingNo}   箱号: ${boxNo}`,
      x: 0.5, // 左对齐位置更靠左，从2改为0.5
      y: yPos,
      fontHeight: 2.5,  // 减小字体大小
    });
    
    // === 表格部分 ===
    yPos += 4; // 进一步减少间距
    
    // 调整列宽比例 - 与模板一致，改为2列布局
    const colWidth = {
      code: 7,     // 序号列宽度10%
      name: 65     // 产品信息列宽度90%
    };
    
    // 列的起始位置
    const colStart = {
      code: 0,
      name: colWidth.code
    };
    
    // 列的中心位置
    const colCenter = {
      code: colStart.code + colWidth.code/2,
      name: colStart.name + colWidth.name/2
    };
    
    // 表头文字 - 序号列
    lpapi.value.setItemHorizontalAlignment(1); // 居中
    lpapi.value.drawText({
      text: "序号",
      x: colCenter.code - 1.5, // 再次向左调整
      y: yPos + 2.0, // 向上调整位置
      fontHeight: 2.4, // 放大字体
      fontStyle: 1 // 加粗
    });
    
    // 产品信息列
    lpapi.value.drawText({
      text: "产品信息",
      x: colCenter.name - 2.5, // 再次向左调整
      y: yPos + 2.0, // 向上调整位置
      fontHeight: 2.4, // 放大字体
      fontStyle: 1 // 加粗
    });
    
    // 绘制表头区域的外边框
    lpapi.value.drawRect({
      x: 0.3,
      y: yPos,
      width: 72.0, // 略微减小宽度，确保右边线能正确打印
      height: 5, // 减小标题行高度
      lineWidth: 0.3 // 细线条
    });
    
    // 绘制表头区域的内部垂直分隔线
    // 序号和产品信息分隔线
    lpapi.value.drawLine({
      x1: colStart.name,
      y1: yPos,
      x2: colStart.name,
      y2: yPos + 6,
      lineWidth: 0.3 // 细线条
    });
    
    // 绘制表头底部横线
    yPos += 5; // 减少表头高度
    lpapi.value.drawLine({
      x1: 0.3,
      y1: yPos,
      x2: 72.3, // 调整与其他线条一致
      y2: yPos,
      lineWidth: 0.3 // 细线条
    });
    
    // 绘制数据行
    const dataRows = currentPageData.value.deliveryDetail && currentPageData.value.deliveryDetail.length > 0 
      ? currentPageData.value.deliveryDetail 
      : [];
    
    // 只绘制实际数据行
    const rowsToDisplay = dataRows.length; // 实际数据的行数
    const rowHeight = 12; // 调整每行高度以适应每页8行显示，从10增加到12
    
    // 设置绘图属性来解决线条不显示问题
    try {
      // 修改绘图属性，进一步减小线条宽度，并确保线条显示
      lpapi.value.setItemAttribute({
        strokeStyle: '#000000', // 线条颜色为黑色
        lineJoin: 'miter',      // 线条连接样式
        lineWidth: 0.3,         // 使用细线条
        lineCap: 'square'       // 添加线条端点样式，使线条边缘清晰
      });
    } catch (e) {
      console.error("设置绘图属性失败:", e);
    }
    
    // 先绘制有数据的行
    for (let i = 0; i < rowsToDisplay; i++) {
      const item = dataRows[i];
      const rowStartY = yPos;

      // 左侧垂直线
      lpapi.value.drawLine({
        x1: 0.3,
        y1: rowStartY,
        x2: 0.3,
        y2: rowStartY + rowHeight,
        lineWidth: 0.3 // 细线条
      });
      
      // 右侧垂直线
      lpapi.value.drawLine({
        x1: 72.3, // 略微向左调整，确保能正确打印
        y1: rowStartY,
        x2: 72.3, // 略微向左调整，确保能正确打印
        y2: rowStartY + rowHeight,
        lineWidth: 0.3 // 细线条
      });
      
      // 列分隔线
      // 序号和产品信息分隔线
      lpapi.value.drawLine({
        x1: colStart.name,
        y1: rowStartY,
        x2: colStart.name,
        y2: rowStartY + rowHeight,
        lineWidth: 0.3 // 细线条   
      });
      

      
      // 显示序号，优先使用ID，如果没有则使用原始索引+1
      const originalIndex = ((currentPage.value - 1) * 8) + i;
      const displayId = (item.ID !== null && item.ID !== undefined) ? 
                        item.ID : 
                        (originalIndex + 1);
      
      lpapi.value.setItemHorizontalAlignment(1); // 居中对齐
      lpapi.value.drawText({
        text: String(displayId), // 转换为字符串确保安全显示
        x: colCenter.code,
        y: rowStartY + 2.5, // 进一步向上调整垂直位置
        fontHeight: 2.6, // 进一步放大字体
        fontStyle: 0.5 // 加粗但不过于粗重
      });
      
      // 产品信息 - 包含产品名称和数量
      const productName = item.PName || '-';
      const quantity = item.Qty >= 1 ? `(${item.Qty})` : '';
      const productSerialNo = item.BatchSN || '-';
      
      // 产品名称、数量和生产批号
      lpapi.value.setItemHorizontalAlignment(0); // 左对齐
      
      // 获取生产批号
      const prodBatch = item.PrdBatch || '-';
      const batchText = (prodBatch && prodBatch !== '-') ? ` 生产批号: ${prodBatch}` : '';
      
      // 处理产品名称过长的情况，超过12个字符就换行
      let displayName = productName;
      if (displayName && displayName.length > 12) {
        // 在第12个字符后添加换行
        const firstLine = displayName.substring(0, 12);
        const secondLine = displayName.substring(12);
        
        // 先绘制第一行
        lpapi.value.drawText({
          text: `产品名称: ${firstLine}`,
          x: colStart.name + 1, // 减小左边距
          y: rowStartY + 1.5, // 进一步向上调整垂直位置
          fontHeight: 2.4, // 放大字体
          width: colWidth.name - 2, // 限制宽度确保不会超出
          wordWrap: false // 禁用换行
        });
        
        // 再绘制第二行
        lpapi.value.drawText({
          text: `${secondLine}${quantity}${batchText}`,
          x: colStart.name + 1, // 减小左边距
          y: rowStartY + 4.5, // 第二行位置上移
          fontHeight: 2.4, // 放大字体
          width: colWidth.name - 2, // 限制宽度确保不会超出
          wordWrap: false // 禁用换行
        });
      } else {
        // 如果产品名称不长，直接显示
        lpapi.value.drawText({
          text: `产品名称: ${displayName}${quantity}${batchText}`,
          x: colStart.name + 1, // 减小左边距
          y: rowStartY + 1.5, // 进一步向上调整垂直位置
          fontHeight: 2.4, // 放大字体
          width: colWidth.name - 2, // 限制宽度确保不会超出
          wordWrap: false // 禁用换行
        });
      }
      
      // 序列号和规格型号在同一行
      const partSpec = item.partSpec || '';
      const partName = item.partName || '';
      
      if((productSerialNo && productSerialNo !== '-') || (partSpec && partSpec !== '-')) {
        // 根据产品名称长度调整位置
        const serialSpecY = productName && productName.length > 12 ? 
                            rowStartY + 7.0 : // 如果产品名称换行，位置上移
                            rowStartY + 5.0;  // 如果产品名称单行，位置上移
        
        // 绘制序列号（左侧）
        if(productSerialNo && productSerialNo !== '-') {
          lpapi.value.drawText({
            text: `序列号: ${productSerialNo}`,
            x: colStart.name + 1, // 左侧
            y: serialSpecY,
            fontHeight: 2.2,
            width: (colWidth.name - 2) / 2, // 占用一半宽度
            wordWrap: false
          });
        }
        
        // 绘制规格型号（右侧）
        if(partSpec && partSpec !== '-') {
          lpapi.value.drawText({
            text: `规格型号: ${partSpec}`,
            x: colStart.name + (colWidth.name - 2) / 2 + 1, // 右侧
            y: serialSpecY,
            fontHeight: 2.0,
            width: (colWidth.name - 2) / 2, // 占用一半宽度
            wordWrap: false
          });
        }
      }
      
      // 部件名称
      if(partName && partName !== '-') {
        const nameY = productName && productName.length > 12 ? 
                      rowStartY + 9.5 : // 如果产品名称换行，部件名称位置上移
                      rowStartY + 7.5;   // 如果产品名称单行，部件名称位置上移
        
        lpapi.value.drawText({
          text: `部件名称: ${partName}`,
          x: colStart.name + 1,
          y: nameY,
          fontHeight: 2.0,
          width: colWidth.name - 2,
          wordWrap: false
        });
      }
      
      // 生产信息 - 包含生产日期和使用期限
      const prodDate = item.PrdDate || '-';
      const effDate = item.EfftDate || '-';
      
      // 生产日期和有效期限 - 在同一行
      if(prodDate && prodDate !== '-') {
        const dateY = productName && productName.length > 12 ? 
                      rowStartY + 11.5 : // 如果产品名称换行，日期位置上移
                      rowStartY + 9.5;   // 如果产品名称单行，日期位置上移
        
        lpapi.value.drawText({
          text: `生产日期: ${prodDate}`,
          x: colStart.name + 1,
          y: dateY,
          fontHeight: 2.0,
          width: colWidth.name - 2,
          wordWrap: false
        });
      }
      
      // 有效期限 - 与生产日期在同一行
      if(effDate && effDate !== '-') {
        const effY = productName && productName.length > 12 ? 
                     rowStartY + 11.5 : // 如果产品名称换行，有效期限位置上移
                     rowStartY + 9.5;   // 如果产品名称单行，有效期限位置上移
        
        lpapi.value.drawText({
          text: `有效期限: ${effDate}`,
          x: colStart.name + 35, // 向右偏移，与生产日期在同一行
          y: effY,
          fontHeight: 2.0,
          width: colWidth.name - 36,
          wordWrap: false
        });
      }
      
      // 底部分隔线
      yPos += rowHeight;
      lpapi.value.drawLine({
        x1: 0.3,             
        y1: yPos,
        x2: 72.3, // 略微向左调整，确保能正确打印
        y2: yPos,
        lineWidth: 0.3 // 细线条
      });
    }
    
    // 底部信息 - 紧接着表格，进一步减少间距
    yPos += 2; // 小间距
    
    // 添加页码显示
    yPos += 0; // 不增加间距，页码紧贴表格底部
    lpapi.value.setItemHorizontalAlignment(2); // 右对齐(2)，0=左对齐，1=居中，2=右对齐
    lpapi.value.drawText({
      text: `第 ${currentPage.value} / ${totalPages.value} 页`,
      x: 60, // 靠右放置，但不要太贴近边缘
      y: yPos,
      fontHeight: 2.0, // 页码字体大小
      fontStyle: 0 // 不加粗
    });
    
    console.log("绘制完成，准备提交打印任务...");
    
    // 更新加载提示
    uni.hideLoading();
    uni.showLoading({
      title: '发送打印数据...'
    });
    
    // 提交打印任务，增加打印浓度和对比度设置
    const printJobAttempt = async (retryCount = 0, maxRetries = 3) => {
      try {
        const result = await lpapi.value.commitJob({
          printDarkness: 20, // 增加打印浓度
          printSpeed: 1,     // 打印速度，更慢更清晰
          threshold: 140,    // 调整阈值
          antiColor: false,  // 不进行反色
          autoPause: true,   // 打印完成后自动暂停
          autoFeed: false,   // 禁止自动进纸
          autoHalt: true,    // 加入自动停止标志
          complete: (resp) => {
            console.log("打印结果:", resp);
            
            // 清除动画状态并显示成功状态
            if (printStatusTimer) {
              clearTimeout(printStatusTimer);
              printStatusTimer = null;
            }
            
            // 停止打印状态动画，立即更新为完成状态
            printingInProgress.value = false;
            
            // 隐藏加载提示并显示打印成功
            uni.hideLoading();
            updatePrintStatus('打印完成');
            
            // 打印完成后立即停止打印机出纸
            if (lpapi.value && lpapi.value.isPrinterOpened()) {
              console.log("打印完成，执行停止出纸操作");
              
              // 立即执行紧急停止
              emergencyStopPrinter();
            }
          }
        });
        
        // 检查打印任务结果
        if (!result) {
          throw new Error("创建打印任务返回空结果");
        }
        
        console.log("打印任务提交结果:", result);
        return result;
      } catch (e) {
        console.error(`打印任务提交失败(尝试${retryCount + 1}/${maxRetries}):`, e);
        
        // 如果还有重试次数，则重试
        if (retryCount < maxRetries) {
          console.log(`${retryCount + 1}秒后重试...`);
          await new Promise(resolve => setTimeout(resolve, (retryCount + 1) * 1000));
          return printJobAttempt(retryCount + 1, maxRetries);
        } else {
          // 重试次数用完，抛出错误
          throw e;
        }
      }
    };
    
    try {
      const result = await printJobAttempt();
      // 打印提交成功的逻辑继续执行...
      
      // 隐藏加载等待状态
      uni.hideLoading();
      
      // 显示打印中状态,并启动动态状态显示
      updatePrintStatus('打印中...');
      
      // 记录打印状态，用于判断是主动打印还是连接断开
      printingInProgress.value = true;
      
      // 启动打印状态动画
      startPrintingAnimation();
      
      // 打印后等待较长时间，确保打印完成后再返回
      // 注意：这里只是一个备用的超时保障，正常情况下上方commitJob的complete回调应该会被触发
      return new Promise((resolve, reject) => {
        setTimeout(() => {
          // 如果回调已经处理了打印完成，则不再执行下面的代码
          if (!printingInProgress.value) {
            console.log("打印已通过回调完成，跳过超时逻辑");
            return resolve();
          }
          
          // 清除动画状态
          if (printStatusTimer) {
            clearTimeout(printStatusTimer);
            printStatusTimer = null;
          }
          
          // 标记打印已完成
          printingInProgress.value = false;
          
          // 更新状态为打印完成
          updatePrintStatus('打印完成');
          
          // 打印成功后的提示
          uni.showToast({
            title: `第${currentPage.value}页打印完成`,
            icon: 'success',
            duration: 800
          });
          
          // 再次尝试暂停打印机，确保不会继续打印
          if (lpapi.value && lpapi.value.isPrinterOpened()) {
            // 最后一次紧急停止
            emergencyStopPrinter();
          }
          
          resolve();
        }, 800); // 延长等待时间至6秒，确保有足够时间等待回调完成
      });
    } catch (e) {
      console.error("打印错误:", e);
      uni.hideLoading();
      
      // 更新打印状态为失败
      updatePrintStatus(`第${currentPage.value}页打印失败: ` + (e.message || '未知错误'));
      printingInProgress.value = false;
      
      return Promise.reject(e);
    }
  } catch (e) {
    console.error("打印错误:", e);
    uni.hideLoading();
    
    // 更新打印状态为失败
    updatePrintStatus(`第${currentPage.value}页打印失败: ` + (e.message || '未知错误'));
    printingInProgress.value = false;
    
    return Promise.reject(e);
  }
  // #endif
  
  // #ifdef H5
  // H5环境下模拟打印
  return new Promise((resolve) => {
    setTimeout(() => {
      uni.hideLoading();
      
      // 通知用户打印已发送
      uni.showToast({
        title: `第${currentPage.value}页打印指令已发送`,
        icon: 'success',
        duration: 1500
      });
      
      // 打印后等待片刻，然后返回并销毁当前页面
      setTimeout(() => {
        resolve();
      }, 1500);
    }, 1000);
  });
  // #endif
};

// 初始化状态栏高度
const initStatusBar = () => {
  try {
    const systemInfo = uni.getSystemInfoSync();
    
    // #ifdef APP-PLUS
    statusBarHeight.value = systemInfo.statusBarHeight || 20;
    console.log('APP状态栏高度:', statusBarHeight.value);
    // #endif
    
    // #ifdef H5
    statusBarHeight.value = 0;
    // #endif
    
    // #ifndef APP-PLUS || H5
    statusBarHeight.value = systemInfo.statusBarHeight || 20;
    // #endif
  } catch (error) {
    console.error('获取系统信息失败', error);
    statusBarHeight.value = 20; // 默认高度
  }
};

// 生命周期钩子
onMounted(() => {
  console.log("组件已挂载，开始初始化...");

  // 初始化状态栏高度
  initStatusBar();

  // 初始化订单数据
  initData();

  // 初始化分页滚动位置
  nextTick(() => {
    initScrollPosition();
  });

  // 添加延迟，确保Canvas组件已渲染
  setTimeout(() => {
    // 创建Canvas元素
    createCanvasElement();
    
    // #ifdef APP-PLUS
    // 初始化印立方SDK
    initLPAPI();
    // #endif
    
    // #ifndef APP-PLUS
    console.log("非APP环境，跳过印立方SDK初始化");
    // #endif
  }, 500);
  
  // 尝试从页面参数获取来源信息
  const pages = getCurrentPages();
  if (pages.length > 0) {
    const currentPage = pages[pages.length - 1];
    if (currentPage.$page && currentPage.$page.options) {
      if (currentPage.$page.options.from) {
        pageParams.value.from = currentPage.$page.options.from;
      }
      if (currentPage.$page.options.id) {
        pageParams.value.id = currentPage.$page.options.id;
      }
      if (currentPage.$page.options.orderNo) {
        pageParams.value.orderNo = currentPage.$page.options.orderNo;
      }
    }
  }
  
  // 监听窗口尺寸变化
  // #ifdef H5
  window.addEventListener('resize', handleResize);
  // #endif
  
  // 页面加载完成后自动连接打印机
  // #ifdef APP-PLUS
  setTimeout(() => {
    // 检查是否有缓存的打印机设备
    const cachedDevice = loadCachedPrinter();
    if (cachedDevice) {
      console.log("发现缓存的打印机设备，自动连接:", cachedDevice);
      // 将缓存设备添加到打印机列表
      if (!printers.value.some(p => p.deviceId === cachedDevice.deviceId)) {
        printers.value.push(cachedDevice);
      }
      
      // 尝试直接连接缓存的设备
      uni.showLoading({
        title: '连接打印机中...'
      });
      
      // 延迟一小段时间后尝试连接
      setTimeout(() => {
        connectToPrinter(cachedDevice);
      }, 1000);
    } else {
      // 没有缓存设备，开始搜索打印机
      console.log("未发现缓存的打印机设备，开始搜索打印机");
      startNormalDiscovery();
    }
  }, 2000); // 延迟2秒，确保页面和SDK都已初始化完成
  // #endif
});

// 确保Canvas元素已创建
const createCanvasElement = () => {
  // 检查Canvas元素是否存在
  const query = uni.createSelectorQuery();
  query.select('#' + canvasId).boundingClientRect(data => {
    if (!data) {
      console.error('Canvas元素不存在，尝试动态创建');
      
      // 动态创建Canvas元素
      try {
        // #ifdef APP-PLUS
        const currentWebview = plus.webview.currentWebview();
        const canvasElement = new plus.nativeObj.View(canvasId, {
          top: '0px',
          left: '0px',
          width: '580px', 
          height: '1050px', // 从1150px增加到1250px，更大的打印高度
          backgroundColor: '#FFFFFF'
        });
        currentWebview.append(canvasElement);
        console.log('动态创建Canvas元素成功');
        // #endif
      } catch (e) {
        console.error('动态创建Canvas元素失败:', e);
      }
    } else {
      console.log('Canvas元素已存在:', data);
    }
  }).exec();
};

// 组件卸载前清理监听器
onBeforeUnmount(() => {
  // #ifdef H5
  window.removeEventListener('resize', handleResize);
  // #endif
  
  // 清除打印状态计时器
  if (printStatusTimer) {
    clearTimeout(printStatusTimer);
    printStatusTimer = null;
  }
  
  // 清除连接超时计时器
  if (connectionTimeoutTimer) {
    clearTimeout(connectionTimeoutTimer);
    connectionTimeoutTimer = null;
  }
  
  // #ifdef APP-PLUS
  // 停止蓝牙搜索
  if (isDiscovering.value && lpapi.value) {
    lpapi.value.stopBleDiscovery();
  }
  // #endif
});

// 添加更新打印状态的函数
// 更新打印状态并显示相应提示
const updatePrintStatus = (status, showToast = true) => {
  printStatus.value = status;
  console.log("打印状态更新:", status);
  
  // 清除之前的计时器
  if (printStatusTimer) {
    clearTimeout(printStatusTimer);
    printStatusTimer = null;
  }
  
  // 隐藏任何已显示的loading
  uni.hideLoading();
  
  if (showToast) {
    uni.showToast({
      title: status,
      icon: status.includes('失败') ? 'none' : 'success',
      duration: 1500
    });
  }
};

// 启动打印状态动画显示
const startPrintingAnimation = () => {
  const states = ['打印中.', '打印中..', '打印中...'];
  let index = 0;
  
  // 清除之前的计时器
  if (printStatusTimer) {
    clearTimeout(printStatusTimer);
  }
  
  // 更新状态函数
  const updateStatus = () => {
    // 如果打印已完成，则不再显示打印中动画
    if (!printingInProgress.value) {
      if (printStatusTimer) {
        clearTimeout(printStatusTimer);
        printStatusTimer = null;
      }
      
      // 确保隐藏loading
      uni.hideLoading();
      return;
    }
    
    updatePrintStatus(states[index], false);
    index = (index + 1) % states.length;
    
    uni.showLoading({
      title: states[index],
      mask: false
    });
    
    printStatusTimer = setTimeout(updateStatus, 800);
  };
  
  // 启动动画
  updateStatus();
};

// 紧急停止打印机函数
const emergencyStopPrinter = () => {
  if (!lpapi.value || !lpapi.value.isPrinterOpened()) return false;
  
  try {
    console.log("执行紧急停止打印机操作");
    
    // 1. 立即多次发送暂停指令
    for (let i = 0; i < 3; i++) {
      lpapi.value.pausePrinter();
    }
    
    // 2. 中止并取消当前打印任务
    try {
      lpapi.value.abortJob();
      lpapi.value.cancelJob();
    } catch (e) {
      console.warn("取消打印任务失败:", e);
    }
    
    // 3. 发送打印机控制命令
    try {
      // 实时停止打印命令 - 最优先
      lpapi.value.sendRawData([0x10, 0x14, 0x01, 0x00, 0x01]);
      
      // 重置打印机
      lpapi.value.sendRawData([0x1B, 0x40]);
      
      // 取消缓冲区数据
      lpapi.value.sendRawData([0x18]);
      
      // 设备控制3 - 暂停命令
      lpapi.value.sendRawData([0x13]);
      
      // 走纸一定距离然后切纸 - 处理已在打印头的纸
      lpapi.value.sendRawData([0x1D, 0x56, 0x00]);
      
      // 打印头归位
      lpapi.value.sendRawData([0x1B, 0x3C]);
      
      // 再次发送重置命令
      lpapi.value.sendRawData([0x1B, 0x40]);
    } catch (e) {
      console.warn("发送打印机控制命令失败:", e);
    }
    
    // 4. 再次发送暂停指令保证执行
    lpapi.value.pausePrinter();
    
    // 5. 如果打印机支持setConfig接口，尝试设置自动进纸为关闭
    try {
      if (typeof lpapi.value.setConfig === 'function') {
        lpapi.value.setConfig({
          autoFeed: false,
          continuousFeed: false
        });
      }
    } catch (e) {
      console.warn("设置打印机配置失败:", e);
    }
    
    return true;
  } catch (e) {
    console.error("紧急停止打印机失败:", e);
    return false;
  }
};

// 获取第一个序列号
const getFirstSerialNumber = (item) => {
  if (!item) return '';
  
  // 如果有serialNumbers数组
  if (item.serialNumbers && item.serialNumbers.length > 0) {
    const firstSerial = item.serialNumbers[0];
    return firstSerial.serialNumber || '';
  }
  
  // 如果有serialNumbers字符串属性
  if (item.serialNumbers && typeof item.serialNumbers === 'string') {
    // 可能是JSON字符串，尝试解析
    try {
      const parsedSerials = JSON.parse(item.serialNumbers);
      if (Array.isArray(parsedSerials) && parsedSerials.length > 0) {
        return parsedSerials[0].serialNumber || parsedSerials[0] || '';
      }
    } catch (e) {
      // 不是JSON，可能是逗号分隔的字符串
      const serials = item.serialNumbers.split(',');
      if (serials.length > 0) {
        return serials[0] || '';
      }
    }
  }
  
  // 直接使用serialNumber属性
  if (item.serialNumber) {
    return item.serialNumber;
  }
  
  return '';
};

// 添加全部打印方法
const printAllPages = async () => {
  // 显示确认对话框
  uni.showModal({
    title: '全部打印',
    content: `确定要打印全部${totalPages.value}页吗？`,
    success: (res) => {
      if (res.confirm) {
        // 用户确认，执行全部打印
        printAllPagesConfirm();
      }
    }
  });
};

// 执行全部打印
const printAllPagesConfirm = async () => {
  // 保存当前页码
  const originalPage = currentPage.value;
  
  try {
    // 显示加载提示
    uni.showLoading({
      title: '准备打印全部页面...',
      mask: true
    });
    
    // 依次打印每一页
    for (let i = 1; i <= totalPages.value; i++) {
      // 切换到对应页面
      currentPage.value = i;
      
      // 等待页面数据更新
      await new Promise(resolve => setTimeout(resolve, 500));
      
      // 更新加载提示
      uni.showLoading({
        title: `正在打印第${i}/${totalPages.value}页...`,
        mask: true
      });
      
      // 打印当前页
      await printDocument();
      
      // 打印完成后等待一段时间，确保打印完成
      await new Promise(resolve => setTimeout(resolve, 2000));
    }
    
    // 全部打印完成
    uni.showToast({
      title: '全部页面打印完成',
      icon: 'success',
      duration: 2000
    });
  } catch (error) {
    console.error('全部打印失败:', error);
    uni.showToast({
      title: '打印过程中出错',
      icon: 'none',
      duration: 2000
    });
  } finally {
    // 恢复原始页码
    currentPage.value = originalPage;
    uni.hideLoading();
  }
};
</script>

<style scoped>
.print-preview-page {
  min-height: 100vh;
  background-color: #f7f9fc;
  padding-bottom: 30px;
  display: flex;
  flex-direction: column;
}

/* 隐藏的Canvas，用于绘制打印内容 */
.hidden-canvas {
  position: absolute;
  width: 580px;
  height: 1050px; /* 从1150px增加到1250px，以匹配更大的打印高度 */
  top: 0px;
  left: 0px;
  visibility: hidden;
  background: #fff;
}

/* 状态栏占位 */
.status-bar {
  width: 100%;
  background-color: white;
}

.header {
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
  background-color: white;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  position: relative;
  top: 0;
  z-index: 100;
  box-sizing: border-box;
  width: 100%;
}

.back-btn {
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  -webkit-tap-highlight-color: transparent;
}

.back-icon {
  width: 20px;
  height: 20px;
}

.header-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  flex: 1;
  text-align: center;
}

.placeholder {
  width: 30px;
}

/* 主体内容区 */
.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding-top: 15px;
  background-color: #f7f9fc;
  height: auto;
  padding-bottom: 140px; /* 增加底部内边距 */
}

/* 打印提示 */
.print-tips {
  margin: 5px 16px 15px;
  padding: 12px;
  background-color: #fff7e6;
  border-radius: 8px;
  border-left: 4px solid #ffab00;
  box-shadow: 0 2px 6px rgba(255, 171, 0, 0.1);
}

.print-tips text {
  color: #875a00;
  font-size: 14px;
  line-height: 1.5;
}

/* 打印联次选择器 */
.print-copies-selector {
  margin: 0 16px 10px;
  background-color: transparent;
  border: none;
  box-shadow: none;
  display: flex;
  justify-content: center;
}

.copies-buttons {
  display: flex;
  gap: 8px;
}

.copy-button {
  min-width: 75px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f5f5;
  border: 1px solid #ddd;
  border-radius: 14px;
  transition: all 0.2s ease;
}

.copy-button.active {
  background-color: #4CAF50;
  border-color: #4CAF50;
  box-shadow: 0 1px 2px rgba(76, 175, 80, 0.2);
}

.copy-button text {
  font-size: 12px;
  color: #333;
  font-weight: normal;
}

.copy-button.active text {
  color: white;
}

/* 打印预览区域 */
.print-preview-wrapper {
  flex: 1;
  margin: 0 16px 140px; /* 增加底部边距，为分页器和底部按钮留出更多空间 */
  padding: 15px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  overflow: auto;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  position: relative;
  min-height: 600px;
  max-height: calc(100vh - 240px); /* 调整最大高度，确保底部元素可见 */
}

.print-preview-container {
  width: 100%;
  max-width: 500px;
  margin: 0 auto;
  display: flex;
  align-items: flex-start; /* 改为顶部对齐 */
  justify-content: center;
  min-height: 700px; /* 增加最小高度，从500px改为700px */
  height: auto; /* 允许自动高度 */
  overflow: visible; /* 允许内容溢出，避免截断 */
}

.print-preview {
  width: 95%;
  height: auto;
  max-height: none; /* 移除最大高度限制 */
  border: 1px dashed #ddd;
  padding: 8px; /* 增加内边距 */
  background-color: #fff;
  box-sizing: border-box;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transform: scale(1.1); /* 增加缩放比例 */
  transform-origin: center top;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: visible; /* 允许内容溢出可见 */
  font-size: 110%; /* 增大字体 */
  margin-bottom: 30px; /* 底部增加边距，从20px改为30px */
}

/* 仅标题居中，其他内容按原始对齐方式 */
.print-title {
  text-align: center;
}

/* 确保客户信息区域左对齐 */
.info-section, .info-row, .info-item, .info-label, .info-value {
  text-align: left !important;
}

/* 移除横向提示样式 */
.orientation-hint {
  display: none;
}

/* 底部按钮区域 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 12px 5%; /* 增加上下内边距 */
  display: flex;
  justify-content: space-between;
  background-color: #ffffff;
  box-shadow: 0 -3px 10px rgba(0, 0, 0, 0.08); /* 增强阴影效果 */
  z-index: 10;
  border-top: 1px solid #f0f0f0;
  padding-bottom: calc(12px + env(safe-area-inset-bottom)); /* 适配iPhone底部安全区域 */
}

.bottom-btn {
  height: 52px; /* 增加按钮高度 */
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.cancel-btn {
  width: 28%; /* 调整宽度为28% */
  background-color: rgba(100, 116, 139, 0.1);
  color: #64748b;
  border: 1px solid rgba(100, 116, 139, 0.2);
}

.print-all-btn {
  width: 28%; /* 调整宽度为28% */
  background-color: #f0f9ff;
  color: #0369a1;
  border: 1px solid #bae6fd;
}

.print-btn {
  width: 40%; /* 调整宽度为40% */
  background-color: #0c873d;
  color: white;
  border: none;
  box-shadow: 0 3px 6px rgba(12, 135, 61, 0.2); /* 添加按钮阴影 */
}

.btn-hover {
  opacity: 0.9;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

/* 打印媒体查询 */
@media print {
  .print-preview-page {
    background-color: #fff;
  }
  
  .header, .back-btn, .print-tips, .bottom-actions, .orientation-hint {
    display: none;
  }
  
  .main-content {
    padding: 0;
  }
  
  .print-preview-wrapper {
    margin: 0;
    padding: 0;
    box-shadow: none;
    overflow: visible;
  }
  
  .print-preview-container {
    perspective: none;
    transform-style: flat;
    margin: 0;
    max-width: none;
    height: auto;
    position: static;
  }
  
  .print-preview {
    border: none;
    padding: 0;
    box-shadow: none;
    transform: none; /* 竖向显示，不需要旋转 */
    margin: 0;
    width: 100%;
    height: auto;
    position: static;
    font-size: 100%; /* 恢复正常字体大小 */
  }
  
  /* 打印尺寸设置 */
  @page {
    size: portrait; /* 设置为纵向打印 */
    margin: 2mm; /* 减小边距 */
  }
}

/* 横屏样式优化 */
@media screen and (orientation: landscape) {
  .print-preview-wrapper {
    padding: 15px;
    overflow: auto; /* 允许滚动 */
  }
  
  .print-preview-container {
    height: auto;
    min-height: 500px;
  }
  
  .print-preview {
    transform: scale(1.05);
    width: 92%;
    height: auto;
    max-height: none;
    margin: 0 auto 30px;
    font-size: 110%;
  }
}

/* 为大屏幕设备优化 */
@media screen and (min-width: 1024px) {
  .print-preview {
    width: 92%;
    height: auto;
    max-height: none;
    font-size: 120%;
    transform: scale(1.15);
  }
}

/* 为小屏幕设备优化 */
@media screen and (max-width: 600px) {
  .print-preview-wrapper {
    margin: 0 8px 80px;
    padding: 10px;
    overflow: auto;
  }
  
  .print-preview-container {
    max-width: 100%;
    height: auto;
  }
  
  .print-preview {
    transform: scale(1.0);
    margin: 0 auto 20px;
    width: 98%;
    height: auto;
    max-height: none;
    padding: 5px;
    font-size: 105%;
  }
}

/* 打印分页选择器 */
.page-selector {
  margin: 5px 16px 10px;
  padding: 12px 15px; /* 增加内边距 */
  background-color: #f0f8ff;
  border-radius: 8px;
  border-left: 4px solid #1e90ff;
  display: flex;
  flex-direction: column;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05); /* 添加轻微阴影 */
}

.page-title {
  font-size: 15px; /* 增大字号 */
  font-weight: 600; /* 加粗 */
  color: #333;
  margin-bottom: 12px; /* 增加下边距 */
}

.page-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 10px; /* 增加按钮之间的间距 */
  margin-bottom: 8px;
}

.page-button {
  min-width: 70px; /* 增加按钮宽度 */
  height: 36px; /* 增加按钮高度 */
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f5f5;
  border: 1px solid #ddd;
  border-radius: 6px; /* 增加圆角 */
  padding: 0 12px;
  cursor: pointer; /* 添加鼠标手型 */
  transition: all 0.2s ease; /* 添加过渡效果 */
}

.page-button.active {
  background-color: #1e90ff;
  border-color: #1e90ff;
  box-shadow: 0 2px 4px rgba(30, 144, 255, 0.3); /* 为激活按钮添加阴影 */
}

.page-button.active text {
  color: white;
  font-weight: 600; /* 加粗激活状态的文字 */
}

.page-button text {
  font-size: 14px; /* 增大字号 */
}

.page-desc {
  font-size: 13px; /* 增大字号 */
  color: #666;
  margin-top: 6px;
}

/* 底部按钮文本调整 */
.print-btn text {
  font-size: 16px;
  font-weight: 600; /* 加粗打印按钮文字 */
}

/* 底部固定的分页选择器 */
.fixed-page-selector {
  position: fixed;
  bottom: 80px; /* 继续提高位置，避免被遮挡 */
  left: 50%;
  transform: translateX(-50%);
  background-color: rgba(255, 255, 255, 0.98); /* 增加不透明度 */
  padding: 5px 10px; /* 增加内边距使按钮更容易点击 */
  border-radius: 18px; /* 增大圆角 */
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15); /* 增强阴影效果 */
  z-index: 100; /* 提高层级，确保在最上层 */
  width: 200px; /* 固定宽度，只显示5个按钮 */
  overflow: hidden;
}

/* 分页滚动视图 */
.page-scroll-view {
  width: 100%;
  height: 38px;
}

/* 分页滚动内容 */
.page-scroll-content {
  display: flex;
  align-items: center;
  gap: 6px; /* 按钮间距 */
  padding: 0 4px;
}

.fixed-page-button {
  width: 28px; /* 增大按钮尺寸 */
  height: 28px; /* 增大按钮尺寸 */
  min-width: 28px; /* 确保最小宽度，防止压缩 */
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f5f5;
  border: 1px solid #ddd;
  border-radius: 50%;
  transition: all 0.2s ease;
  cursor: pointer;
  flex-shrink: 0; /* 防止按钮被压缩 */
}

.fixed-page-button.active {
  background-color: #1e90ff;
  border-color: #1e90ff;
  box-shadow: 0 2px 4px rgba(30, 144, 255, 0.3);
}

.fixed-page-button.active text {
  color: white;
  font-weight: 600;
}

.fixed-page-button text {
  font-size: 12px; /* 减小字体大小 */
  color: #333;
}

.preview-page-indicator {
  position: absolute;
  top: 10px;
  right: 10px;
  background-color: rgba(30, 144, 255, 0.9);
  color: white;
  padding: 4px 10px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  z-index: 2;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* 适配小屏幕设备 */
@media screen and (max-width: 375px) {
  .print-preview-wrapper {
    margin-bottom: 160px; /* 在小屏设备上增加更多底部边距 */
  }
  
  .main-content {
    padding-bottom: 160px;
  }
  
  .bottom-actions {
    padding: 12px 3% calc(12px + env(safe-area-inset-bottom));
  }
  
  .bottom-btn {
    height: 44px; /* 减小按钮高度 */
  }
}
</style> 
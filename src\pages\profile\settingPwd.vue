<template>
  <view class="pwd-settings">
    <!-- 状态栏占位 -->
    <!-- #ifdef MP-WEIXIN -->
    <view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>
    <!-- #endif -->

    <!-- #ifdef APP-PLUS -->
    <view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>
    <!-- #endif -->

    <!-- 顶部标题栏 -->
    <view class="header">
      <view class="back-btn" @click="navigateBack">
        <image class="back-icon" src="/static/icons/return.png"></image>
      </view>
      <text class="header-title">修改密码</text>
      <view class="placeholder"></view>
    </view>

    <!-- 密码修改表单 -->
    <view class="form-container">
      <!-- 原密码 -->
      <view class="form-group">
        <view class="form-label">原密码</view>
        <view class="password-input-wrapper">
          <input :type="passwordVisible.old ? 'text' : 'password'" v-model="formData.oldPassword" class="form-input"
            placeholder="请输入当前密码" id="oldPasswordInput" @focus="focusState.old = true"
            @blur="handleBlur('old', $event)" />
          <view class="password-toggle" v-if="formData.oldPassword && focusState.old" @mousedown.prevent
            @click.stop="togglePasswordVisibility('old')">
            <image :src="passwordVisible.old ? '/static/icons/eye-open.png' : '/static/icons/eye-close.png'"
              class="eye-icon"></image>
          </view>
        </view>
      </view>

      <!-- 新密码 -->
      <view class="form-group">
        <view class="form-label">新密码</view>
        <view class="password-input-wrapper">
          <input :type="passwordVisible.new ? 'text' : 'password'" v-model="formData.newPassword" class="form-input"
            placeholder="请输入新密码" id="newPasswordInput" @focus="focusState.new = true"
            @blur="handleBlur('new', $event)" />
          <view class="password-toggle" v-if="formData.newPassword && focusState.new" @mousedown.prevent
            @click.stop="togglePasswordVisibility('new')">
            <image :src="passwordVisible.new ? '/static/icons/eye-open.png' : '/static/icons/eye-close.png'"
              class="eye-icon"></image>
          </view>
        </view>
      </view>

      <!-- 确认新密码 -->
      <view class="form-group">
        <view class="form-label">确认新密码</view>
        <view class="password-input-wrapper">
          <input :type="passwordVisible.confirm ? 'text' : 'password'" v-model="formData.confirmPassword"
            class="form-input" placeholder="请再次输入新密码" id="confirmPasswordInput" @focus="focusState.confirm = true"
            @blur="handleBlur('confirm', $event)" />
          <view class="password-toggle" v-if="formData.confirmPassword && focusState.confirm" @mousedown.prevent
            @click.stop="togglePasswordVisibility('confirm')">
            <image :src="passwordVisible.confirm ? '/static/icons/eye-open.png' : '/static/icons/eye-close.png'"
              class="eye-icon"></image>
          </view>
        </view>
      </view>

      <!-- 密码规则提示 -->
      <view class="password-tips">
        <text class="tips-title">密码要求：</text>
        <view class="tips-item" :class="{ 'validated': passwordValidation.length }">
          <text class="dot">•</text>
          <text>密码长度至少8位</text>
        </view>
        <view class="tips-item" :class="{ 'validated': passwordValidation.hasNumber }">
          <text class="dot">•</text>
          <text>包含数字</text>
        </view>
        <view class="tips-item" :class="{ 'validated': passwordValidation.hasLetter }">
          <text class="dot">•</text>
          <text>包含字母</text>
        </view>
      </view>
    </view>

    <!-- 保存按钮 -->
    <view class="button-container">
      <button class="save-button" @click="changePassword">确认修改</button>
    </view>
  </view>
</template>

<script setup>
import { ref, reactive, computed, watch, nextTick, onMounted } from 'vue';

import { updateUserPassword } from '@/api/user'

// 状态栏高度
const statusBarHeight = ref(20);

// 表单数据
const formData = reactive({
  oldPassword: '',
  newPassword: '',
  confirmPassword: ''
});

// 密码显示控制
const passwordVisible = reactive({
  old: false,
  new: false,
  confirm: false
});

// 输入框焦点状态
const focusState = reactive({
  old: false,
  new: false,
  confirm: false
});

// 页面初始化
onMounted(() => {
  // 获取状态栏高度
  try {
    const systemInfo = uni.getSystemInfoSync();

    // #ifdef MP-WEIXIN
    statusBarHeight.value = systemInfo.statusBarHeight || 20;
    // #endif

    // #ifdef APP-PLUS
    statusBarHeight.value = systemInfo.statusBarHeight || 20;
    console.log('APP状态栏高度:', statusBarHeight.value);
    // #endif
  } catch (error) {
    console.error('获取系统信息失败', error);
  }
});

// 切换密码显示状态
const togglePasswordVisibility = (field) => {
  // 切换状态
  passwordVisible[field] = !passwordVisible[field];

  // 在下一个tick中尝试重新聚焦
  nextTick(() => {
    // 根据字段类型获取对应的输入框ID
    const inputId = field === 'old'
      ? '#oldPasswordInput'
      : field === 'new'
        ? '#newPasswordInput'
        : '#confirmPasswordInput';

    // 使用uni-app的方式聚焦
    uni.createSelectorQuery()
      .select(inputId)
      .fields({
        context: true,
        size: true,
      })
      .exec(res => {
        if (res && res[0]) {
          // 有些平台可能不支持focus方法，所以要做检查
          try {
            res[0].node && res[0].node.focus && res[0].node.focus();
          } catch (err) {
            console.log('focus error:', err);
          }
        }
      });
  });

  return false;
};

// 表单错误信息
const formErrors = reactive({
  oldPassword: '',
  newPassword: '',
  confirmPassword: ''
});

// 密码验证
const passwordValidation = computed(() => {
  const hasNumber = /\d/.test(formData.newPassword);
  const hasLetter = /[a-zA-Z]/.test(formData.newPassword);
  const length = formData.newPassword.length >= 8;

  return {
    hasNumber,
    hasLetter,
    length,
    isValid: hasNumber && hasLetter && length
  };
});

// 监听密码变化
watch(() => formData.newPassword, () => {
  // 如果确认密码已经输入，则检查是否匹配
  if (formData.confirmPassword) {
    validatePasswordMatch();
  }
});

// 验证两次密码输入是否一致
const validatePasswordMatch = () => {
  if (formData.newPassword !== formData.confirmPassword) {
    formErrors.confirmPassword = '两次输入的密码不一致';
    return false;
  } else {
    formErrors.confirmPassword = '';
    return true;
  }
};

// 返回上一页
const navigateBack = () => {
  try {
    // 获取当前页面栈
    const pages = getCurrentPages();

    // 如果当前页面栈只有1个页面，那么无法返回上一页，需要重定向到个人中心
    if (pages.length <= 1) {
      uni.switchTab({
        url: '/pages/profile/index'
      });
    } else {
      // 有上一页，正常返回
      uni.navigateBack();
    }
  } catch (error) {
    console.error('导航错误', error);
    // 出错时也重定向到个人中心
    uni.switchTab({
      url: '/pages/profile/index'
    });
  }
};

// 验证表单
const validateForm = () => {
  let isValid = true;

  // 清空错误信息
  Object.keys(formErrors).forEach(key => {
    formErrors[key] = '';
  });

  // 验证原密码
  if (!formData.oldPassword) {
    formErrors.oldPassword = '请输入原密码';
    isValid = false;
  }

  // 验证新密码
  if (!formData.newPassword) {
    formErrors.newPassword = '请输入新密码';
    isValid = false;
  } else if (!passwordValidation.value.isValid) {
    formErrors.newPassword = '密码不符合要求';
    isValid = false;
  }

  // 验证确认密码
  if (!formData.confirmPassword) {
    formErrors.confirmPassword = '请确认新密码';
    isValid = false;
  } else if (!validatePasswordMatch()) {
    isValid = false;
  }

  return isValid;
};

// 修改密码
const changePassword = async () => {
  
  if (!validateForm()) {
    // 显示第一个错误
    const firstError = Object.values(formErrors).find(error => error);
    if (firstError) {
      uni.showToast({
        title: firstError,
        icon: 'none'
      });
    }
    return;
  }

  uni.showLoading({ title: '提交中...' });

  const reqData = {
    oldPassword: formData.oldPassword,
    newPassword: formData.newPassword,
    confirmPassword: formData.confirmPassword
  }

  // 模拟API调用
  //await new Promise(resolve => setTimeout(resolve, 1500));
  await updateUserPassword(reqData)

  uni.hideLoading();
  uni.showToast({
    title: '密码修改成功',
    icon: 'success'
  });

  // 返回上一页或导航到个人中心
  setTimeout(() => {
    navigateBack();
  }, 1500);
};

// 处理输入框失焦
const handleBlur = (field, event) => {
  // 检查是否点击了小眼睛图标
  const relatedTarget = event.relatedTarget;
  if (relatedTarget && relatedTarget.closest && relatedTarget.closest('.password-toggle')) {
    // 如果点击了小眼睛，不执行失焦操作
    return;
  }
  focusState[field] = false;
};
</script>

<style scoped>
.pwd-settings {
  min-height: 100vh;
  background-color: #f8f9fa;
  padding-bottom: 30px;
  display: flex;
  flex-direction: column;
}

/* 状态栏占位 */
.status-bar {
  width: 100%;
  background-color: white;
}

.header {
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
  background-color: white;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  position: relative;
  top: 0;
  z-index: 100;
  box-sizing: border-box;
  width: 100%;
}

.back-btn {
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  -webkit-tap-highlight-color: transparent;
}

.back-icon {
  width: 20px;
  height: 20px;
}

.header-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  flex: 1;
  text-align: center;
}

.placeholder {
  width: 30px;
}

.form-container {
  padding: 20px 16px;
  background-color: white;
  border-radius: 8px;
  margin: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.form-group {
  margin-bottom: 20px;
}

.form-label {
  font-size: 14px;
  color: #666;
  margin-bottom: 6px;
  font-weight: 500;
}

.password-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  background-color: #f9f9f9;
  transition: border-color 0.3s, box-shadow 0.3s;
}

.password-input-wrapper:focus-within {
  border-color: #0c873d;
  background-color: #fff;
  box-shadow: 0 0 0 2px rgba(12, 135, 61, 0.1);
}

.form-input {
  width: 100%;
  height: 44px;
  padding: 0 12px;
  font-size: 15px;
  background-color: transparent;
  border: none;
  color: #333;
  outline: none;
}

.password-toggle {
  padding: 6px;
  cursor: pointer;
  z-index: 2;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  user-select: none;
  -webkit-user-select: none;
  transition: all 0.2s;
  opacity: 0.8;
  margin-right: 4px;
}

.password-toggle:hover {
  opacity: 1;
}

.eye-icon {
  width: 20px;
  height: 20px;
  opacity: 0.6;
}

.password-tips {
  background-color: #f9f9f9;
  border-radius: 8px;
  padding: 12px 16px;
  margin-top: 5px;
}

.tips-title {
  font-size: 13px;
  color: #555;
  margin-bottom: 8px;
  display: block;
}

.tips-item {
  display: flex;
  align-items: center;
  margin-bottom: 6px;
  font-size: 12px;
  color: #888;
}

.dot {
  margin-right: 5px;
  font-size: 16px;
}

.tips-item.validated {
  color: #0c873d;
}

.button-container {
  padding: 20px 16px;
}

.save-button {
  width: 100%;
  height: 44px;
  background-color: #0c873d;
  color: white;
  border-radius: 22px;
  font-size: 16px;
  font-weight: 500;
  border: none;
  box-shadow: 0 2px 8px rgba(12, 135, 61, 0.25);
  transition: background-color 0.3s, transform 0.2s;
}

.save-button:active {
  background-color: #097033;
  transform: scale(0.98);
}

/* 响应式调整 */
@media screen and (min-width: 768px) {
  .form-container {
    max-width: 500px;
    margin: 20px auto;
    padding: 24px;
  }

  .button-container {
    max-width: 500px;
    margin: 0 auto;
  }
}
</style>

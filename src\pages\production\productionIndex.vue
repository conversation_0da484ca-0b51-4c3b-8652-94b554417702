<template>
  <view class="container">
    <!-- 使用uni-nav-bar组件 -->
    <view class="nav-bar-container">
      <uni-nav-bar
        :fixed="true"
        :status-bar="true"
        title="作业单元"
        left-icon="left"
        @clickLeft="goBack"
        :border="false"
      />
    </view>
    
    <!-- 主体内容区 -->
    <view class="main-content">
      <!-- 扫码和输入区域 -->
      <view class="scan-container">
        <view class="scan-icon-btn" @click="handleScan">
          <image class="scan-icon-left" src="/static/icons/scan.png" mode="aspectFit"></image>
        </view>
        <view class="input-wrapper" :class="{ 'input-focus': isInputFocused }">
          <input 
            class="code-input" 
            type="text" 
            v-model="codeInput" 
            placeholder="扫描或输入作业单元" 
            @confirm="handleCodeSubmit"
            @focus="handleInputFocus"
            @blur="handleInputBlur"
          />
          <view v-if="codeInput" class="clear-icon" @click.stop="clearCodeInput">×</view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { useUserStore } from '@/store/user';
import scanCodeUtil from '@/utils/scanCode';

// 导入uni-nav-bar组件
import uniNavBar from '@dcloudio/uni-ui/lib/uni-nav-bar/uni-nav-bar.vue'

// 用户状态管理
const userStore = useUserStore();

// 输入框内容
const codeInput = ref('');

// 输入框焦点状态
const isInputFocused = ref(false);

// 处理输入框获得焦点
const handleInputFocus = () => {
  isInputFocused.value = true;
};

// 处理输入框失去焦点
const handleInputBlur = () => {
  isInputFocused.value = false;
};

// 处理输入提交
const handleCodeSubmit = () => {
  if (!codeInput.value.trim()) {
    uni.showToast({
      title: '请输入条码',
      icon: 'none'
    });
    return;
  }
  
  // 执行工单查询
  const code = codeInput.value.trim();
  processProductionCode(code);
  
  // 清空输入框
  codeInput.value = '';
};

// 清除输入框
const clearCodeInput = () => {
  codeInput.value = '';
};

// 扫码功能
const handleScan = async () => {
  try {
    // 使用scanCode.js中的扫码功能
    const result = await scanCodeUtil.start({
      preserveGS1Separators: true
    });
    
    // 直接处理扫描结果，不显示在输入框
    processProductionCode(result);
    
    // 确保输入框为空
    codeInput.value = '';
  } catch (error) {
    uni.showToast({
      title: '扫码失败',
      icon: 'none'
    });
    console.error('扫码失败:', error);
  }
};

// 处理生产工单号
const processProductionCode = (code) => {
  // 跳转到工单执行页面
  navigateToExecutive(code);
};

// 跳转到执行页面
const navigateToExecutive = (code) => {
  uni.navigateTo({
    url: './productionExecutive?code=' + encodeURIComponent(code),
    fail: (error) => {
      uni.showToast({
        title: '页面跳转失败',
        icon: 'none'
      });
    }
  });
};

// 返回上一页
const goBack = () => {
  // 使用switchTab跳转到首页
  uni.switchTab({
    url: '/pages/index/index'
  });
};

// 生命周期
onMounted(() => {
  // 检查是否已登录
  checkLogin();
  
  // APP端设置状态栏样式
  // #ifdef APP-PLUS
  plus.navigator.setStatusBarStyle('dark');
  // #endif
});

// 检查登录状态
const checkLogin = () => {
  // 使用userStore的isLoggedIn getter
  const isLoggedIn = userStore.isLoggedIn;
  
  if (!isLoggedIn) {
    setTimeout(() => {
      // 再次检查，防止状态更新延迟
      if (!userStore.isLoggedIn) {
        uni.navigateTo({
          url: '/pages/login/login'
        });
      }
    }, 100);
  }
};
</script>

<style scoped>
/* 容器样式 */
.container {
  height: 100vh;
  width: 100%;
  display: flex;
  flex-direction: column;
  background-color: #f7f9fc;
  position: relative;
  overflow: hidden;
  margin: 0;
  border: none;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, sans-serif;
}

/* 导航栏容器 */
.nav-bar-container {
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 100;
}

/* 主体内容区域 */
.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  width: 100%;
  overflow: hidden;
  position: relative;
  padding: 0 20px;
  margin-top: 10px; /* 减小与标题栏的距离 */
}

/* 扫码和输入区域 */
.scan-container {
  display: flex;
  align-items: center;
  margin: 20px 0;
  width: 100%;
}

.scan-icon-btn {
  width: 56px;
  height: 56px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 10px;
}

.scan-icon-left {
  width: 33px;
  height: 33px;
}

.input-wrapper {
  flex: 1;
  display: flex;
  align-items: center;
  height: 36px;
  border-radius: 12px;
  border: 1px solid #d1d5db;
  background-color: #fff;
  padding: 0 15px;
  position: relative;
  transition: all 0.3s ease;
}

.input-focus {
  border-color: #4CAF50;
  box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.1);
}

.code-input {
  flex: 1;
  height: 100%;
  border: none;
  outline: none;
  font-size: 15px;
  color: #333;
}

.clear-icon {
  font-size: 16px;
  color: #999;
  padding: 4px;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
}
</style> 
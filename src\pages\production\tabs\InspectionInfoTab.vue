<template>
  <view class="tab-pane">
    <view class="info-card">
      <view class="card-body">
        <view v-if="inspectionInfo.length === 0" class="empty-tip">暂无抽检信息</view>
        <view v-else class="inspection-list">
          <view v-for="(item, index) in inspectionInfo" :key="index" class="inspection-item">
            <view class="inspection-header">
              <text class="inspection-title">{{ item.inspectionNo || '未知抽检' }}</text>
            </view>
            <view class="inspection-body">
              <view class="inspection-info">
                <text class="inspection-label">检验项目</text>
                <text class="inspection-value">{{ item.inspectionItem || '-' }}</text>
              </view>
              <view class="inspection-info">
                <text class="inspection-label">检验结果</text>
                <text class="inspection-value">{{ item.result || '-' }}</text>
              </view>
              <view class="inspection-info">
                <text class="inspection-label">检验时间</text>
                <text class="inspection-value">{{ item.inspectionTime || '-' }}</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
// 定义props接收父组件传递的数据
const props = defineProps({
  inspectionInfo: {
    type: Array,
    default: () => []
  }
});
</script>

<style scoped>
/* 卡片样式 */
.info-card {
  background-color: #ffffff;
  border-radius: 10px;
  margin-bottom: 10px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.card-body {
  padding: 15px;
}

/* 空数据提示 */
.empty-tip {
  text-align: center;
  padding: 30px 0;
  color: #9ca3af;
  font-size: 14px;
}

/* 抽检信息样式 */
.inspection-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.inspection-item {
  background-color: #f8fafc;
  border-radius: 8px;
  padding: 12px;
  border: 1px solid #e2e8f0;
}

.inspection-header {
  margin-bottom: 8px;
  padding-bottom: 6px;
  border-bottom: 1px solid #e2e8f0;
}

.inspection-title {
  font-size: 14px;
  font-weight: 600;
  color: #0f172a;
}

.inspection-body {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
}

.inspection-info {
  display: flex;
  flex-direction: column;
}

.inspection-label {
  font-size: 11px;
  color: #64748b;
  margin-bottom: 2px;
}

.inspection-value {
  font-size: 13px;
  color: #334155;
}

.tab-pane {
  height: 100%;
}
</style> 
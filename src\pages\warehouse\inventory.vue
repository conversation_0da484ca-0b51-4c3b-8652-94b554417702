<template>
  <view class="container">
    <!-- 使用uni-nav-bar组件 -->
    <view class="nav-bar-container">
      <uni-nav-bar
        :fixed="true"
        :status-bar="true"
        title="库存查询"
        left-icon="left"
        @clickLeft="goBack"
        :border="false"
      />
    </view>
    
    <!-- 主体内容区 -->
    <view class="main-content">
      <!-- 搜索区域 -->
      <view class="search-container">
        <view class="search-wrapper">
          <uni-icons type="search" size="16" color="#999"></uni-icons>
          <input 
            class="search-input" 
            type="text" 
            v-model="searchKeyword" 
            placeholder="输入产品编号、名称或条码" 
            @confirm="handleSearch"
          />
          <view v-if="searchKeyword" class="clear-icon" @click.stop="clearSearch">×</view>
        </view>
        <view class="scan-btn" @click="handleScan">
          <image class="scan-icon" src="/static/icons/scan.png" mode="aspectFit"></image>
        </view>
      </view>
      
      <!-- 筛选区域 -->
      <view class="filter-container">
        <view class="filter-item" @click="showLocationPicker = true">
          <text class="filter-text">{{ selectedLocation || '仓库位置' }}</text>
          <uni-icons type="down" size="12" color="#666"></uni-icons>
        </view>
        <view class="filter-item" @click="showCategoryPicker = true">
          <text class="filter-text">{{ selectedCategory || '产品类别' }}</text>
          <uni-icons type="down" size="12" color="#666"></uni-icons>
        </view>
        <view class="filter-item" @click="toggleSortOrder">
          <text class="filter-text">{{ sortText }}</text>
          <uni-icons :type="sortOrder === 'asc' ? 'up' : 'down'" size="12" color="#666"></uni-icons>
        </view>
      </view>
      
      <!-- 库存列表 -->
      <view class="inventory-list">
        <view v-if="loading" class="loading-container">
          <uni-icons type="spinner-cycle" size="24" color="#4CAF50"></uni-icons>
          <text class="loading-text">加载中...</text>
        </view>
        <view v-else-if="inventoryList.length === 0" class="empty-container">
          <image class="empty-icon" src="/static/icons/empty.png" mode="aspectFit"></image>
          <text class="empty-text">暂无库存数据</text>
        </view>
        <view v-else class="inventory-items">
          <view 
            v-for="(item, index) in inventoryList" 
            :key="index" 
            class="inventory-item"
            @click="showInventoryDetail(item)"
          >
            <view class="inventory-main">
              <view class="inventory-header">
                <text class="inventory-name">{{ item.productName }}</text>
                <text class="inventory-quantity">{{ item.quantity }}{{ item.unit }}</text>
              </view>
              <view class="inventory-info">
                <text class="inventory-code">{{ item.productCode }}</text>
                <text class="inventory-location">{{ item.location }}</text>
              </view>
              <view class="inventory-specs">
                <text class="inventory-spec">{{ item.specification }}</text>
                <text class="inventory-category">{{ item.category }}</text>
              </view>
            </view>
            <view class="inventory-action">
              <uni-icons type="right" size="16" color="#999"></uni-icons>
            </view>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 仓库位置选择器 -->
    <uni-popup ref="locationPopup" type="bottom" @change="handlePopupChange">
      <view class="picker-container">
        <view class="picker-header">
          <text class="picker-title">选择仓库位置</text>
          <view class="picker-close" @click="closeLocationPicker">×</view>
        </view>
        <view class="picker-body">
          <view 
            class="picker-item" 
            :class="{ 'picker-item-active': selectedLocation === '' }"
            @click="selectLocation('')"
          >
            <text>全部</text>
          </view>
          <view 
            v-for="(location, index) in locationOptions" 
            :key="index"
            class="picker-item"
            :class="{ 'picker-item-active': selectedLocation === location }"
            @click="selectLocation(location)"
          >
            <text>{{ location }}</text>
          </view>
        </view>
      </view>
    </uni-popup>
    
    <!-- 产品类别选择器 -->
    <uni-popup ref="categoryPopup" type="bottom" @change="handlePopupChange">
      <view class="picker-container">
        <view class="picker-header">
          <text class="picker-title">选择产品类别</text>
          <view class="picker-close" @click="closeCategoryPicker">×</view>
        </view>
        <view class="picker-body">
          <view 
            class="picker-item" 
            :class="{ 'picker-item-active': selectedCategory === '' }"
            @click="selectCategory('')"
          >
            <text>全部</text>
          </view>
          <view 
            v-for="(category, index) in categoryOptions" 
            :key="index"
            class="picker-item"
            :class="{ 'picker-item-active': selectedCategory === category }"
            @click="selectCategory(category)"
          >
            <text>{{ category }}</text>
          </view>
        </view>
      </view>
    </uni-popup>
    
    <!-- 库存详情弹窗 -->
    <uni-popup ref="detailPopup" type="center">
      <view class="detail-container">
        <view class="detail-header">
          <text class="detail-title">库存详情</text>
          <view class="detail-close" @click="closeDetailPopup">×</view>
        </view>
        <view class="detail-body">
          <view class="detail-item">
            <text class="detail-label">产品编号</text>
            <text class="detail-value">{{ currentItem.productCode || '-' }}</text>
          </view>
          <view class="detail-item">
            <text class="detail-label">产品名称</text>
            <text class="detail-value">{{ currentItem.productName || '-' }}</text>
          </view>
          <view class="detail-item">
            <text class="detail-label">规格型号</text>
            <text class="detail-value">{{ currentItem.specification || '-' }}</text>
          </view>
          <view class="detail-item">
            <text class="detail-label">库存数量</text>
            <text class="detail-value">{{ currentItem.quantity || '0' }}{{ currentItem.unit || '个' }}</text>
          </view>
          <view class="detail-item">
            <text class="detail-label">仓库位置</text>
            <text class="detail-value">{{ currentItem.location || '-' }}</text>
          </view>
          <view class="detail-item">
            <text class="detail-label">产品类别</text>
            <text class="detail-value">{{ currentItem.category || '-' }}</text>
          </view>
          <view class="detail-item">
            <text class="detail-label">最后入库</text>
            <text class="detail-value">{{ currentItem.lastInboundTime || '-' }}</text>
          </view>
          <view class="detail-item">
            <text class="detail-label">最后出库</text>
            <text class="detail-value">{{ currentItem.lastOutboundTime || '-' }}</text>
          </view>
        </view>
        <view class="detail-footer">
          <view class="detail-btn detail-btn-inbound" @click="navigateToInbound">入库</view>
          <view class="detail-btn detail-btn-outbound" @click="navigateToOutbound">出库</view>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script setup>
import { ref, reactive, onMounted, computed, watch } from 'vue';
import { onLoad } from '@dcloudio/uni-app';
import scanCodeUtil from '@/utils/scanCode';

// 导入uni组件
import uniNavBar from '@dcloudio/uni-ui/lib/uni-nav-bar/uni-nav-bar.vue'
import uniIcons from '@dcloudio/uni-ui/lib/uni-icons/uni-icons.vue'
import uniPopup from '@dcloudio/uni-ui/lib/uni-popup/uni-popup.vue'

// 搜索关键词
const searchKeyword = ref('');

// 加载状态
const loading = ref(false);

// 排序方式
const sortOrder = ref('desc');
const sortText = computed(() => sortOrder.value === 'asc' ? '数量升序' : '数量降序');

// 筛选选项
const selectedLocation = ref('');
const selectedCategory = ref('');

// 选择器显示状态
const showLocationPicker = ref(false);
const showCategoryPicker = ref(false);

// 仓库位置选项
const locationOptions = ref(['A区-01', 'A区-02', 'B区-01', 'B区-02', 'C区-01', 'C区-02']);

// 产品类别选项
const categoryOptions = ref(['原材料', '半成品', '成品', '包装材料', '辅助材料']);

// 库存列表
const inventoryList = ref([]);

// 当前选中的库存项
const currentItem = reactive({});

// 弹窗引用
const locationPopup = ref(null);
const categoryPopup = ref(null);
const detailPopup = ref(null);

// 处理页面加载
onLoad((options) => {
  // 如果有传入的条码参数，直接搜索
  if (options.code) {
    searchKeyword.value = decodeURIComponent(options.code);
    handleSearch();
  } else {
    // 否则加载全部库存
    loadInventoryData();
  }
});

// 加载库存数据
const loadInventoryData = () => {
  loading.value = true;
  
  // 模拟API请求
  setTimeout(() => {
    // 模拟数据
    const mockData = [
      {
        productCode: 'P001',
        productName: '产品A',
        specification: '规格A-123',
        quantity: 100,
        unit: '个',
        location: 'A区-01',
        category: '成品',
        lastInboundTime: '2023-05-15 10:30',
        lastOutboundTime: '2023-05-20 14:20'
      },
      {
        productCode: 'P002',
        productName: '产品B',
        specification: '规格B-456',
        quantity: 50,
        unit: '箱',
        location: 'B区-01',
        category: '半成品',
        lastInboundTime: '2023-05-16 09:15',
        lastOutboundTime: '2023-05-19 11:45'
      },
      {
        productCode: 'P003',
        productName: '产品C',
        specification: '规格C-789',
        quantity: 200,
        unit: '个',
        location: 'A区-02',
        category: '成品',
        lastInboundTime: '2023-05-14 16:20',
        lastOutboundTime: '2023-05-18 10:30'
      },
      {
        productCode: 'P004',
        productName: '原料D',
        specification: '规格D-101',
        quantity: 500,
        unit: 'kg',
        location: 'C区-01',
        category: '原材料',
        lastInboundTime: '2023-05-10 08:45',
        lastOutboundTime: '2023-05-17 15:10'
      },
      {
        productCode: 'P005',
        productName: '包装盒E',
        specification: '规格E-202',
        quantity: 1000,
        unit: '个',
        location: 'C区-02',
        category: '包装材料',
        lastInboundTime: '2023-05-12 14:30',
        lastOutboundTime: '2023-05-16 09:20'
      }
    ];
    
    // 应用筛选
    let filteredData = mockData;
    
    // 按仓库位置筛选
    if (selectedLocation.value) {
      filteredData = filteredData.filter(item => item.location === selectedLocation.value);
    }
    
    // 按产品类别筛选
    if (selectedCategory.value) {
      filteredData = filteredData.filter(item => item.category === selectedCategory.value);
    }
    
    // 按关键词搜索
    if (searchKeyword.value) {
      const keyword = searchKeyword.value.toLowerCase();
      filteredData = filteredData.filter(item => 
        item.productCode.toLowerCase().includes(keyword) || 
        item.productName.toLowerCase().includes(keyword)
      );
    }
    
    // 排序
    filteredData.sort((a, b) => {
      if (sortOrder.value === 'asc') {
        return a.quantity - b.quantity;
      } else {
        return b.quantity - a.quantity;
      }
    });
    
    inventoryList.value = filteredData;
    loading.value = false;
  }, 500);
};

// 处理搜索
const handleSearch = () => {
  loadInventoryData();
};

// 清除搜索
const clearSearch = () => {
  searchKeyword.value = '';
  loadInventoryData();
};

// 处理扫码
const handleScan = async () => {
  try {
    const result = await scanCodeUtil.start({
      preserveGS1Separators: true
    });
    
    searchKeyword.value = result;
    handleSearch();
  } catch (error) {
    uni.showToast({
      title: '扫码失败',
      icon: 'none'
    });
    console.error('扫码失败:', error);
  }
};

// 切换排序方式
const toggleSortOrder = () => {
  sortOrder.value = sortOrder.value === 'asc' ? 'desc' : 'asc';
  loadInventoryData();
};

// 选择仓库位置
const selectLocation = (location) => {
  selectedLocation.value = location;
  closeLocationPicker();
  loadInventoryData();
};

// 选择产品类别
const selectCategory = (category) => {
  selectedCategory.value = category;
  closeCategoryPicker();
  loadInventoryData();
};

// 打开仓库位置选择器
const openLocationPicker = () => {
  locationPopup.value.open();
};

// 关闭仓库位置选择器
const closeLocationPicker = () => {
  locationPopup.value.close();
};

// 打开产品类别选择器
const openCategoryPicker = () => {
  categoryPopup.value.open();
};

// 关闭产品类别选择器
const closeCategoryPicker = () => {
  categoryPopup.value.close();
};

// 处理弹窗变化
const handlePopupChange = (e) => {
  if (e.show === false) {
    showLocationPicker.value = false;
    showCategoryPicker.value = false;
  }
};

// 监听位置选择器显示状态
watch(showLocationPicker, (val) => {
  if (val) {
    openLocationPicker();
  }
});

// 监听类别选择器显示状态
watch(showCategoryPicker, (val) => {
  if (val) {
    openCategoryPicker();
  }
});

// 显示库存详情
const showInventoryDetail = (item) => {
  // 复制数据到当前选中项
  Object.assign(currentItem, item);
  detailPopup.value.open();
};

// 关闭详情弹窗
const closeDetailPopup = () => {
  detailPopup.value.close();
};

// 导航到入库页面
const navigateToInbound = () => {
  closeDetailPopup();
  uni.navigateTo({
    url: `/pages/warehouse/inbound?code=${currentItem.productCode}`
  });
};

// 导航到出库页面
const navigateToOutbound = () => {
  closeDetailPopup();
  uni.navigateTo({
    url: `/pages/warehouse/outbound?code=${currentItem.productCode}`
  });
};

// 返回上一页
const goBack = () => {
  uni.navigateBack({
    delta: 1
  });
};
</script>

<style scoped>
/* 容器样式 */
.container {
  height: 100vh;
  width: 100%;
  display: flex;
  flex-direction: column;
  background-color: #f7f9fc;
  position: relative;
  overflow: hidden;
  margin: 0;
  border: none;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, sans-serif;
}

/* 导航栏容器 */
.nav-bar-container {
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 100;
}

/* 主体内容区域 */
.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  width: 100%;
  overflow-y: auto;
  position: relative;
  padding: 0 15px;
  margin-top: 10px;
}

/* 搜索区域样式 */
.search-container {
  display: flex;
  align-items: center;
  margin: 10px 0;
  width: 100%;
}

.search-wrapper {
  flex: 1;
  display: flex;
  align-items: center;
  height: 36px;
  border-radius: 8px;
  border: 1px solid #d1d5db;
  background-color: #fff;
  padding: 0 10px;
  position: relative;
}

.search-input {
  flex: 1;
  height: 100%;
  border: none;
  outline: none;
  font-size: 14px;
  color: #333;
  margin-left: 5px;
}

.clear-icon {
  font-size: 16px;
  color: #999;
  padding: 4px;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
}

.scan-btn {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 10px;
}

.scan-icon {
  width: 24px;
  height: 24px;
}

/* 筛选区域样式 */
.filter-container {
  display: flex;
  align-items: center;
  margin: 10px 0;
  width: 100%;
  background-color: #fff;
  border-radius: 8px;
  padding: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.filter-item {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 30px;
  border-right: 1px solid #f0f0f0;
  padding: 0 10px;
}

.filter-item:last-child {
  border-right: none;
}

.filter-text {
  font-size: 13px;
  color: #666;
  margin-right: 5px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 库存列表样式 */
.inventory-list {
  flex: 1;
  width: 100%;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 0;
}

.loading-text {
  font-size: 14px;
  color: #999;
  margin-top: 10px;
}

.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 0;
}

.empty-icon {
  width: 60px;
  height: 60px;
  margin-bottom: 10px;
}

.empty-text {
  font-size: 14px;
  color: #999;
}

.inventory-items {
  width: 100%;
}

.inventory-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #fff;
  border-radius: 8px;
  padding: 12px 15px;
  margin-bottom: 10px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.inventory-main {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.inventory-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 5px;
}

.inventory-name {
  font-size: 15px;
  font-weight: 500;
  color: #333;
}

.inventory-quantity {
  font-size: 15px;
  font-weight: 500;
  color: #4CAF50;
}

.inventory-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 5px;
}

.inventory-code {
  font-size: 13px;
  color: #666;
}

.inventory-location {
  font-size: 13px;
  color: #666;
}

.inventory-specs {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.inventory-spec {
  font-size: 12px;
  color: #999;
}

.inventory-category {
  font-size: 12px;
  color: #999;
  background-color: #f5f5f5;
  padding: 2px 6px;
  border-radius: 4px;
}

.inventory-action {
  display: flex;
  align-items: center;
  margin-left: 10px;
}

/* 选择器样式 */
.picker-container {
  background-color: #fff;
  border-top-left-radius: 16px;
  border-top-right-radius: 16px;
  overflow: hidden;
}

.picker-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  border-bottom: 1px solid #f0f0f0;
}

.picker-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.picker-close {
  font-size: 20px;
  color: #999;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.picker-body {
  padding: 10px 15px;
  max-height: 60vh;
  overflow-y: auto;
}

.picker-item {
  padding: 12px 0;
  font-size: 15px;
  color: #333;
  border-bottom: 1px solid #f0f0f0;
}

.picker-item:last-child {
  border-bottom: none;
}

.picker-item-active {
  color: #4CAF50;
}

/* 详情弹窗样式 */
.detail-container {
  width: 80%;
  background-color: #fff;
  border-radius: 12px;
  overflow: hidden;
}

.detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  border-bottom: 1px solid #f0f0f0;
}

.detail-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.detail-close {
  font-size: 20px;
  color: #999;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.detail-body {
  padding: 15px;
  max-height: 60vh;
  overflow-y: auto;
}

.detail-item {
  display: flex;
  margin-bottom: 10px;
}

.detail-item:last-child {
  margin-bottom: 0;
}

.detail-label {
  width: 80px;
  font-size: 14px;
  color: #666;
}

.detail-value {
  flex: 1;
  font-size: 14px;
  color: #333;
}

.detail-footer {
  display: flex;
  border-top: 1px solid #f0f0f0;
}

.detail-btn {
  flex: 1;
  height: 44px;
  line-height: 44px;
  text-align: center;
  font-size: 15px;
}

.detail-btn-inbound {
  color: #4CAF50;
  border-right: 1px solid #f0f0f0;
}

.detail-btn-outbound {
  color: #FF5722;
}
</style> 
<script>
import { queryVersions } from './api/versions'
import baseURL from '@/config/index.js'
import { useUserStore } from '@/store/user'


export default {
  data() {
    return {
      statusBarHeight: 20,
      hasCheckedVersion: false, // 版本检查标记
      appShowCount: 0, // 记录onShow调用次数
      appHideCount: 0,  // 记录onHide调用次数
      downloadTask: null, // 下载任务引用
      downloadWaiting: null // 下载等待提示引用
    }
  },
  
  onLaunch: function () {
    // console.log(baseURL)
    console.log('App Launch')
    
    // 获取用户store
    const userStore = useUserStore()
    
    // 检查本地存储中是否有用户信息
    try {
      const storedUserInfo = uni.getStorageSync('userInfo')
      const tokenExpireTime = uni.getStorageSync('tokenExpireTime')
      
      // 检查token是否过期
      let isTokenExpired = true
      if (tokenExpireTime) {
        const now = Date.now()
        isTokenExpired = now > Number(tokenExpireTime)
        if (isTokenExpired) {
          //console.log('Token已过期，过期时间:', new Date(Number(tokenExpireTime)).toLocaleString())
        } else {
          const remainingTime = Math.round((Number(tokenExpireTime) - now) / (60 * 1000)) // 剩余分钟
          //console.log(`Token有效，剩余约${remainingTime}分钟过期`)
        }
      }
      
      // 如果有用户信息且未过期，恢复登录状态
      if (storedUserInfo && !isTokenExpired) {
        // 解析用户信息
        let userInfo = storedUserInfo
        if (typeof storedUserInfo === 'string') {
          try {
            userInfo = JSON.parse(storedUserInfo)
          } catch (e) {
            console.error('解析用户信息失败:', e)
            userInfo = {}
          }
        }
        
        // 恢复用户状态
        userStore.userInfo = userInfo
        userStore.tokenExpireTime = Number(tokenExpireTime)
        
      } else if (isTokenExpired) {
        // 如果token过期，清除用户信息
        userStore.clearUserInfo()
      }
    } catch (e) {
      console.error('检查本地存储失败:', e)
    }
    
    // #ifdef APP-PLUS
    // 检查网络状态
    uni.getNetworkType({
      success: function (res) {
        if (res.networkType === 'none') {
          uni.showToast({
            title: '网络连接不可用，请检查网络设置',
            icon: 'none',
            duration: 3000
          })
        }
      }
    })
    
    // 监听网络状态变化
    uni.onNetworkStatusChange(function (res) {
      if (!res.isConnected) {
        uni.showToast({
          title: '网络连接已断开',
          icon: 'none',
          duration: 3000
        })
      } else {
        console.log('网络已连接，类型：' + res.networkType)
      }
    })
    
    // APP端设置状态栏
    plus.navigator.setStatusBarStyle('dark')
    
    // 安全区域处理
    this.initSafeArea()
    // #endif
  },
  
  onShow: function () {
    // 增加计数并仅在第一次时输出日志和执行版本检查
    this.appShowCount++
    if (this.appShowCount === 1) {
      console.log('App Show')
      
      // 获取用户store
      const userStore = useUserStore()
      
      // 检查token是否过期
      if (userStore.isLoggedIn) {
        // 调用store的方法检查token是否过期
        userStore.checkTokenExpiration()
        
        // 检查是否登录，如果已登录，检查版本
        // 使用标记防止重复执行版本检查
        if (!this.hasCheckedVersion) {
          this.checkVersionAfterLogin()
          this.hasCheckedVersion = true
        }
      } else {
        // 如果未登录或token已过期，跳转到登录页面
        //console.log('未登录或token已过期，需要重新登录')
        uni.reLaunch({
          url: '/pages/login/login'
        });
      }
    }
  },
  
  onHide: function () {
    // 增加计数并仅在第一次时输出日志
    this.appHideCount++
    if (this.appHideCount === 1) {
      console.log('App Hide')
    }
    
    // #ifdef APP-PLUS
    // 应用进入后台时，如果有正在进行的下载任务，显示通知提醒用户
    /*
    if (this.downloadTask) {
      plus.push.createMessage(
        '应用正在后台下载更新，请勿关闭应用',
        'download',
        {cover: true}
      );
    }
    */
    // #endif
  },
  
  methods: {
    // 检查版本（在登录后调用）
    checkVersionAfterLogin() {
      
      // 获取当前APP版本
      const currentVersion = this.getAppVersion()
      
      // 查询后端版本信息
      this.queryServerVersion(currentVersion)
    },
    
    // 重置计数器（可在需要时调用）
    resetCounters() {
      this.appShowCount = 0
      this.appHideCount = 0
      this.hasCheckedVersion = false
    },
    
    // 获取当前APP版本
    getAppVersion() {
      // #ifdef APP-PLUS
      try {
        // 区分调试基座和正式APP
        if (plus.runtime.isCustomLaunch) {
          return '1.0.0' // 开发环境使用固定版本号
        }
        
        // 正式环境获取真实版本号
        const versionName = plus.runtime.version
        return versionName
      } catch (err) {
        return '1.0.0'
      }
      // #endif
      
      // 非APP环境，返回模拟版本号
      return '1.0.0'
    },
    
    // 查询服务器版本
    async queryServerVersion(currentVersion) {
      try {
        // #ifdef APP-PLUS
        // 在调试基座中跳过版本检查
        if (plus.runtime.isCustomLaunch) {
          console.log('调试基座中运行，跳过版本检查')
          return
        }
        // #endif

        const result = await queryVersions({
          VersionsType: 'App'
        })
        
        if (result && result.data && result.data.length > 0) {
          // 获取最新版本信息（第一条数据）
          const latestVersion = result.data[0]
          
          // 获取版本号，根据返回的数据结构，字段名是Versions
          // 移除可能的'V'前缀
          let serverVersion = latestVersion.Versions || ''
          if (serverVersion.startsWith('V') || serverVersion.startsWith('v')) {
            serverVersion = serverVersion.substring(1)
          }

          // 比较版本号
          const compareResult = this.compareVersions(serverVersion, currentVersion)
          
          if (compareResult > 0) {
            // 先跳转到首页
            uni.switchTab({
              url: '/pages/index/index',
              success: () => {
                // 跳转成功后，通过全局事件触发版本更新检查
                uni.$emit('checkAppUpdate', {
                  version: serverVersion,
                  downloadUrl: baseURL.downloadPath,
                  remark: latestVersion.Remark || '发现新版本，请更新获得更好的体验'
                })
              }
            })
          } else {
            //console.log('当前已是最新版本')
          }
        } else {
          //console.log('未获取到版本信息或数据为空')
        }
      } catch (error) {
        console.error('查询版本信息失败', error)
      }
    },
    
    // 比较版本号，返回1表示v1>v2，返回-1表示v1<v2，返回0表示相等
    compareVersions(v1, v2) {
      // 确保版本号是字符串
      v1 = String(v1)
      v2 = String(v2)
      
      // #ifdef APP-PLUS
      // 在调试基座中运行时跳过版本检查
      if (plus.runtime.isCustomLaunch) {
        return 0 // 在调试基座中返回版本相等，避免提示更新
      }
      // #endif
      
      // 处理数字型版本号 (如14.57)
      if (/^\d+\.\d+$/.test(v2) && parseFloat(v2) > 10) {
        return 0 // 不提示更新
      }
      
      const v1Parts = v1.split('.').map(Number)
      const v2Parts = v2.split('.').map(Number)
      
      for (let i = 0; i < Math.max(v1Parts.length, v2Parts.length); i++) {
        const v1Part = v1Parts[i] || 0
        const v2Part = v2Parts[i] || 0
        
        if (v1Part > v2Part) {
          return 1
        } else if (v1Part < v2Part) {
          return -1
        }
      }
      
      return 0
    },
    
    // #ifdef APP-PLUS
    // 初始化安全区域
    initSafeArea() {
      try {
        // 获取手机系统信息
        const systemInfo = uni.getSystemInfoSync()
        // 状态栏高度
        this.statusBarHeight = systemInfo.statusBarHeight || 20
        
        // 安全区域
        if (systemInfo.safeArea) {
          // 记录安全区域信息
          const safeArea = systemInfo.safeArea
          
          // 设置CSS变量，用于全局样式
          uni.setStorageSync('statusBarHeight', this.statusBarHeight)
          
          // 设置导航栏样式
          plus.navigator.setStatusBarBackground('#FFFFFF')
          plus.navigator.setStatusBarStyle('dark')
        }
      } catch (e) {
        console.error('初始化安全区域失败', e)
      }
    }
    // #endif
  }
}
</script>

<style>
/*每个页面公共css */
@import './static/css/common.css';

/* APP全局样式补充 */
/* #ifdef APP-PLUS */
page {
  background-color: #f7f9fc;
}

.page-container {
  min-height: 100vh;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}

.status-bar {
  height: var(--status-bar-height);
  width: 100%;
  background-color: #FFFFFF;
}
/* #endif */
</style>

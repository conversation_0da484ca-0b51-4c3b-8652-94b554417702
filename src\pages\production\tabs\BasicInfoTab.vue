<template>
  <view class="basic-info-tab">
    <view class="info-card">
      <view class="card-body">
        <view v-if="!productionInfo || Object.keys(productionInfo).length === 0" class="empty-tip">
          暂无基本信息
        </view>
        <view v-else class="info-list">
          <view class="info-item">
            <text class="info-label">工单号</text>
            <text class="info-value">{{ productionInfo.orderNo || '-' }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">产品名称</text>
            <text class="info-value">{{ productionInfo.productName || '-' }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">规格型号</text>
            <text class="info-value">{{ productionInfo.spec || '-' }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">计划数量</text>
            <text class="info-value">{{ productionInfo.planQty || '-' }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">计划开始时间</text>
            <text class="info-value">{{ productionInfo.planStartTime || '-' }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">计划完成时间</text>
            <text class="info-value">{{ productionInfo.planEndTime || '-' }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">状态</text>
            <text class="info-value">{{ productionInfo.status || '-' }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">备注</text>
            <text class="info-value">{{ productionInfo.remark || '-' }}</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { defineProps } from 'vue';

// 定义props
const props = defineProps({
  productionInfo: {
    type: Object,
    default: () => ({})
  }
});
</script>

<style scoped>
/* 卡片样式 */
.basic-info-tab {
  height: 100%;
}

.info-card {
  background-color: #ffffff;
  border-radius: 10px;
  margin-bottom: 10px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.card-body {
  padding: 15px;
}

/* 信息列表样式 */
.info-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.info-item {
  display: flex;
  flex-direction: column;
}

.info-label {
  font-size: 12px;
  color: #64748b;
  margin-bottom: 4px;
}

.info-value {
  font-size: 14px;
  color: #334155;
  font-weight: 500;
}

/* 空数据提示 */
.empty-tip {
  text-align: center;
  padding: 30px 0;
  color: #9ca3af;
  font-size: 14px;
}
</style> 
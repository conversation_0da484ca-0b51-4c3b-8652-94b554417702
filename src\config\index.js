/**
 * 全局配置文件
 * 根据不同的环境（development/production/uat）加载对应的配置
 * 所有服务器通过代理访问
 */

// 服务器地址配置
const SERVER_CONFIG = {
  // APP环境使用的实际服务器地址
 //APP_SERVER: "http://localhost:18127/",
  APP_SERVER: "http://*************:3306/", 
  // 生产环境测试服务器 
  PROD_TEST: "http://***********:81/",
  // 生产环境正式服务器
  PROD_FORMAL: "http://***********:80/"
};

// 下载路径配置
const DOWNLOAD_CONFIG = {
  // APP下载文件名
  APP_FILENAME: "ruixietong.apk",
  // 下载路径后缀
  DOWNLOAD_PATH: "/app/"
};

// 环境配置对象
const CONFIG = {
    // 开发环境配置
    development: {
        serverUrl: SERVER_CONFIG.APP_SERVER, // 开发环境服务器地址
        downloadPath: SERVER_CONFIG.APP_SERVER + DOWNLOAD_CONFIG.DOWNLOAD_PATH + DOWNLOAD_CONFIG.APP_FILENAME, // 开发环境APK下载路径
    },
    // 生产环境配置
    production: {
        serverUrl: SERVER_CONFIG.APP_SERVER, // 生产环境使用实际服务器地址
        downloadPath: SERVER_CONFIG.APP_SERVER + DOWNLOAD_CONFIG.DOWNLOAD_PATH + DOWNLOAD_CONFIG.APP_FILENAME, // 生产环境APK下载路径
    },
    // UAT环境配置
    uat: {
        serverUrl: SERVER_CONFIG.APP_SERVER, // UAT环境服务器地址
        downloadPath: SERVER_CONFIG.APP_SERVER + DOWNLOAD_CONFIG.DOWNLOAD_PATH + DOWNLOAD_CONFIG.APP_FILENAME // UAT环境APK下载路径
    }
};

// 获取当前环境，默认为开发环境
const env = process.env.NODE_ENV || 'development';

// 根据当前环境获取对应的配置
const currentConfig = CONFIG[env];

// H5环境使用代理
// #ifdef H5
// 开发环境走代理
currentConfig.serverUrl = "/Service/";
// #endif

// 导出配置
export default currentConfig;
<template>
  <view class="print-container">
    <!-- 打印模板 -->
    <view class="print-area">
      <!-- 标题 -->
      <view class="print-header">
        <text class="print-title">深圳市资福医疗技术有限公司</text>
        <text class="print-subtitle">装箱清单</text>
      </view>
      
      <!-- 单据信息行 - 将发货单号、运单号、箱号放在同一行 -->
      <view class="order-info">
        <view class="order-info-item full-width">
          <text class="info-label-small">发货单号:</text>
          <text class="info-value-small">{{ orderData.delivery && orderData.delivery.length > 0 ? orderData.delivery[0].DNo : orderData.orderNo }}</text>
          <text class="info-label-small" style="margin-left: 8px;">运单号:</text>
          <text class="info-value-small">{{ orderData.delivery && orderData.delivery.length > 0 ? orderData.delivery[0].TrackingNo : '-' }}</text>
          <text class="info-label-small" style="margin-left: 8px;">箱号:</text>
          <text class="info-value-small">{{ orderData.delivery && orderData.delivery.length > 0 ? orderData.delivery[0].BoxNo : '-' }}</text>
        </view>
      </view>
      
      <!-- 基本信息区域 - 已移除单独的运单号和箱号行 -->
      <view class="info-section">
        <!-- 留空，保留区域以保持布局一致性 -->
      </view>
      
      <!-- 表格区域 -->
      <view class="table-section">
        <!-- 表头 -->
        <view class="table-header">
          <view class="table-cell th-code">序号</view>
          <view class="table-cell th-name">产品信息</view>
        </view>
        
        <!-- 表格内容 -->
        <view class="table-body">
          <!-- 显示实际数据行 -->
          <view class="table-row" v-for="(item, index) in orderData.deliveryDetail" :key="'data-'+index">
            <view class="table-cell td-code">{{ item.ID || (index + 1) }}</view>
            <view class="table-cell td-name">
              <view class="product-info">
                <text class="product-name">产品名称: {{ item.PName || '-' }}{{ item.Qty >= 1 ? `(${item.Qty})` : '' }}{{ item.PrdBatch && item.PrdBatch !== '-' ? ` 生产批号: ${item.PrdBatch}` : '' }}</text>
              </view>
              <view class="serial-spec-row" v-if="(item.BatchSN && item.BatchSN !== '-') || (item.partSpec && item.partSpec !== '-')">
                <text class="product-serial" v-if="item.BatchSN && item.BatchSN !== '-'">序列号: {{ item.BatchSN }}</text>
                <text class="product-part-spec-inline" v-if="item.partSpec && item.partSpec !== '-'">规格型号: {{ item.partSpec }}</text>
              </view>
              <text class="product-part-name" v-if="item.partName && item.partName !== '-'">部件名称: {{ item.partName }}</text>
              <!-- 生产信息 -->
              <view class="prod-date-row" v-if="(item.PrdDate && item.PrdDate !== '-') || (item.EfftDate && item.EfftDate !== '-')">
                <text class="prod-date" v-if="item.PrdDate && item.PrdDate !== '-'">生产日期: {{ item.PrdDate }}</text>
                <text class="prod-exp" v-if="item.EfftDate && item.EfftDate !== '-'">有效期限: {{ item.EfftDate }}</text>
              </view>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 页码显示 -->
      <view class="page-number">
        <text>{{ `第 ${props.pageNumber} / ${props.totalPages} 页` }}</text>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, computed } from 'vue';

// 接收父组件传递的订单数据和联次信息
const props = defineProps({
  orderData: {
    type: Object,
    default: () => ({
      orderNo: '',
      delivery: [],
      deliveryDetail: [],
      createTime: '',
      createdBy: ''
    })
  },
  pageNumber: {
    type: Number,
    default: 1
  },
  totalPages: {
    type: Number,
    default: 1
  }
});

// 获取当前日期时间
const getCurrentDateTime = () => {
  const now = new Date();
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, '0');
  const day = String(now.getDate()).padStart(2, '0');
  const hours = String(now.getHours()).padStart(2, '0');
  const minutes = String(now.getMinutes()).padStart(2, '0');
  const seconds = String(now.getSeconds()).padStart(2, '0');
  
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
};
</script>

<style scoped>
.print-container {
  padding: 0;
  margin: 0;
  width: 100%;
  height: auto;
  min-height: 100vh; /* 确保容器至少占满整个视口高度 */
  background-color: #fff;
  font-family: "PingFang SC", "Helvetica Neue", Arial, sans-serif;
  display: flex;
  justify-content: center;
  align-items: flex-start; /* 改为顶部对齐，避免内容过多时的居中问题 */
}

.print-area {
  padding: 0;
  width: 100%;
  max-width: 100%;
  min-height: 130vh; /* 增加内容区域高度，确保足够容纳更多内容 */
  background-color: #fff;
  /* 添加打印纸张效果 */
  box-shadow: 0 0 6px rgba(0,0,0,0.1);
  border-radius: 2px;
  transform-origin: center center;
  display: flex;
  flex-direction: column;
}

/* 标题区域 */
.print-header {
  padding: 12px 0;
  text-align: center;
  border-bottom: 1px solid #000;
  background-color: #f9f9f9;
  position: relative; /* 添加相对定位 */
  display: flex;
  flex-direction: column;
}

.print-title {
  font-size: 18px;
  font-weight: bold;
  letter-spacing: 1px;
  color: #222;
  text-align: center;
}

.print-subtitle {
  font-size: 16px;
  font-weight: normal;
  letter-spacing: 1px;
  color: #222;
  text-align: center;
  margin-top: 5px;
}

/* 单据信息行 - 与信息区域合并一起 */
.order-info {
  display: flex;
  justify-content: flex-start;
  padding: 6px 6px 3px; /* 进一步减小内边距 */
  background-color: #fff;
  border-bottom: none; /* 移除边框 */
}

.order-info-item {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.order-info-item.full-width {
  width: 100%;
  flex-wrap: wrap; /* 允许在小屏幕上换行 */
}

.order-info-value {
  font-size: 13px; /* 调整为与info-value相同的字体大小 */
  color: #333;
  padding-left: 2px;
}

/* 新增缩小字体的样式类 */
.info-label-small {
  font-weight: 500;
  color: #555;
  font-size: 11px; /* 更小的字体 */
  padding-right: 1px;
  text-align: left;
  white-space: nowrap;
}

.info-value-small {
  color: #333;
  font-size: 11px; /* 更小的字体 */
  word-break: break-all;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  padding-left: 1px;
  text-align: left;
}

/* 基本信息区域 */
.info-section {
  padding: 0 6px 3px; /* 进一步减小内边距 */
  font-size: 13px;
  background-color: #fff;
  border-bottom: 1px dashed #ccc;
  text-align: left; /* 确保左对齐 */
  min-height: 3px; /* 确保即使没有内容也保持一定高度 */
}

.info-row {
  display: flex;
  margin-bottom: 6px; /* 减小行间距 */
  text-align: left; /* 确保左对齐 */
  align-items: flex-start;
}

.info-row:last-child {
  margin-bottom: 0;
}

.info-item {
  display: flex;
  align-items: flex-start;
  text-align: left; /* 确保左对齐 */
}

/* 针对不同字段的宽度调整 */
.client-name.full-width,
.client-address.full-width {
  width: 100%;
  text-align: left; /* 确保左对齐 */
}

.client-contact {
  flex: 1;
  margin-right: 10px;
  text-align: left; /* 确保左对齐 */
}

.client-phone {
  flex: 1;
  text-align: left; /* 确保左对齐 */
}

.info-label {
  font-weight: 500;
  color: #555;
  font-size: 13px; /* 减小字体 */
  min-width: auto; /* 移除固定宽度 */
  padding-right: 3px; /* 添加右边距 */
  text-align: left; /* 确保左对齐 */
  white-space: nowrap; /* 防止换行 */
}

.info-value {
  color: #333;
  font-size: 13px; /* 减小字体 */
  word-break: break-all;
  white-space: normal;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.4; /* 减小行高 */
  padding-left: 1px; /* 减少左边距 */
  text-align: left; /* 确保左对齐 */
  flex: 1; /* 让值占据剩余空间 */
}

/* 地址专用样式 */
.address-text {
  white-space: normal;
  word-break: break-all;
  line-height: 1.4; /* 减小行高 */
  text-align: left;
  display: block;
}

.bold {
  font-weight: bold;
  color: #222;
}

/* 表格区域 */
.table-section {
  margin: 8px 0; /* 减小上下外边距 */
  border: 1px solid #000;
  background-color: #fff;
  display: table; /* 使用table布局替代flex */
  width: 100%;
  border-collapse: collapse; /* 合并边框 */
  table-layout: fixed; /* 固定表格布局 */
}

.table-header {
  display: table-row; /* 表头行 */
  background-color: #f0f0f0;
  font-weight: bold;
  height: 36px; /* 固定表头高度 */
  border-bottom: 1px solid #000; /* 确保表头底部有边框 */
}

/* 确保所有表头文字居中并且有边框 */
.table-header .table-cell {
  text-align: center;
  font-weight: bold;
  vertical-align: middle;
  border: 1px solid #000; /* 确保表头单元格有边框 */
  padding: 4px 2px; /* 表头内边距稍大 */
}

.table-body {
  display: table-row-group; /* 表格主体 */
}

.table-row {
  display: table-row; /* 表格行 */
  min-height: 70px; /* 增加行高以容纳更多内容，从50px增加到70px */
}

.table-cell {
  display: table-cell; /* 表格单元格 */
  border: 1px solid #000;
  padding: 3px 2px; /* 减小内边距 */
  font-size: 10px; /* 进一步缩小表格字体 */
  text-align: center;
  vertical-align: middle; /* 垂直居中 */
  line-height: 1.3; /* 减小行高 */
  box-sizing: border-box; /* 确保边框计算在宽度内 */
  overflow: hidden;
}

.th-code {
  width: 10%; /* 序号列宽度保持10% */
  white-space: nowrap;
  text-align: center;
  padding: 4px; /* 增加表头序号内边距 */
}

.td-code {
  width: 10%; /* 序号列宽度保持10% */
  white-space: nowrap; /* 防止编码数字换行 */
  overflow: hidden;
  text-overflow: ellipsis;
  text-align: center;
  font-weight: 500; /* 加粗序号 */
  padding: 0 4px; /* 增加水平内边距 */
}

.th-name {
  width: 90%; /* 增加产品名称列宽度，从45%增加到90% */
  text-align: center;
}

.td-name {
  width: 90%; /* 增加产品名称列宽度，从45%增加到90% */
  text-align: left;
  padding: 3px 4px; /* 增加内边距，从2px 3px增加到3px 4px */
  vertical-align: middle;
  word-break: break-word; /* 允许在任意位置换行 */
}

.th-prod, .td-prod {
  width: 45%; /* 生产信息列宽度 - 已删除，保留样式以防需要 */
  text-align: center;
}

.td-prod {
  text-align: left;
  padding: 2px 4px;
  vertical-align: middle;
  word-break: break-word; /* 允许在任意位置换行 */
}

/* 生产信息样式 */
.prod-batch {
  display: block;
  font-size: 9px;
  font-weight: 500;
  line-height: 1.3;
  margin-top: 2px; /* 增加与上方内容的间距 */
  color: #333;
}

.prod-date, .prod-exp {
  display: block;
  font-size: 9px;
  color: #333;
  line-height: 1.2;
  margin-top: 1px;
}

/* 新增行布局样式 */
.prod-info-row {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  margin-top: 2px;
  width: 100%;
}

.serial-spec-row {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  margin-top: 1px;
  width: 100%;
}

.prod-date-row {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  margin-top: 1px;
  width: 100%;
}

.prod-batch, .product-part-spec-inline {
  font-size: 9px;
  font-weight: 500;
  color: #333;
  line-height: 1.2;
  flex: 1;
  margin-right: 8px; /* 字段间距 */
}

.product-serial, .product-part-spec-inline {
  font-size: 9px;
  color: #555;
  line-height: 1.2;
  flex: 1;
  margin-right: 8px; /* 字段间距 */
}

.prod-date, .prod-exp {
  font-size: 9px;
  color: #333;
  line-height: 1.2;
  flex: 1;
  margin-right: 8px; /* 字段间距 */
}

.prod-batch:last-child, .product-part-spec-inline:last-child,
.product-serial:last-child, .product-part-spec-inline:last-child,
.prod-date:last-child, .prod-exp:last-child {
  margin-right: 0; /* 最后一个字段不需要右边距 */
}

/* 产品名称和序列号的样式 */
.product-info {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  width: 100%;
  min-width: 0;
  margin-bottom: 2px; /* 添加底部间距 */
}

.product-name {
  font-weight: 500;
  font-size: 10px;
  line-height: 1.2;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: normal;
  word-break: break-word;
  flex: 1;
  min-width: 0;
}



.product-serial {
  display: block;
  font-size: 10px; /* 增大序列号字体从9px到10px */
  color: #555;
  line-height: 1.2;
  margin-top: 1px;
  word-break: break-all;
  white-space: normal;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 100%;
}

/* 新增部件信息样式 */
.product-part-name {
  display: block;
  font-size: 9px;
  color: #555;
  line-height: 1.2;
  margin-top: 1px;
  word-break: break-all;
  white-space: normal;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 100%;
}

/* 页码显示样式 */
.page-number {
  width: 100%;
  text-align: right; /* 修改为右对齐 */
  padding: 8px 0;
  margin-top: 8px;
}

.page-number text {
  font-size: 12px;
  color: #666;
  margin-right: 10px; /* 添加右边距确保不贴边 */
}

/* 适配小尺寸打印纸 */
@media print {
  .print-container {
    width: 100%;
    height: 100%;
    margin: 0;
    padding: 0;
    /* 移除旋转，改为竖向布局 */
    transform: none;
    transform-origin: center center;
  }
  
  .print-area {
    box-shadow: none;
    width: 100%;
    max-width: 100%;
  }
  
  .print-title {
    font-size: 16px; /* 打印时稍微缩小字体 */
  }
  
  .order-info {
    padding: 3px 6px;
  }
  
  .order-info-value {
    font-size: 12px;
  }
  
  .info-label-small, .info-value-small {
    font-size: 10px;
  }
  
  .info-section, .footer-section {
    font-size: 12px;
    padding: 5px 6px;
  }
  
  .info-label, .info-value {
    font-size: 12px;
  }
  
  .td-code {
    font-weight: 500; /* 打印时保持序号加粗 */
    padding: 0 3px; /* 打印时保持序号内边距 */
  }
  
  .table-cell {
    font-size: 9px; /* 打印时进一步缩小字体 */
    padding: 2px;
  }
  
  .th-name, .td-name {
    font-size: 9px; /* 打印时进一步缩小字体 */
  }
  
  .product-name {
    font-size: 9px; /* 打印时进一步缩小产品名称字体 */
  }
  
  .product-serial {
    font-size: 9px; /* 打印时增大序列号字体从8px到9px */
  }
  
  .product-part-spec, .product-part-name {
    font-size: 8px; /* 打印时缩小部件信息字体 */
  }
  
  .product-part-spec-inline {
    font-size: 8px; /* 打印时缩小规格型号字体 */
  }
  
  .prod-batch, .product-part-spec-inline {
    font-size: 8px; /* 打印时缩小生产批号和规格型号字体 */
  }
  
  .prod-date, .prod-exp {
    font-size: 8px; /* 打印时缩小日期字体 */
  }
  
  .signature-value {
    font-size: 12px;
  }
  
  .page-number {
    padding: 5px 0;
    margin-top: 5px;
  }
  
  .page-number text {
    font-size: 10px;
  }
}
</style> 
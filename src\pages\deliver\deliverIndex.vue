<template>
    <view class="container">
      <!-- 使用uni-nav-bar组件 -->
      <view class="nav-bar-container">
        <uni-nav-bar
          :fixed="true"
          :status-bar="true"
          title="发货单据"
          left-icon="left"
          @clickLeft="goBack"
          :border="false"
        />
      </view>
      
      <!-- 主体内容区（包括搜索框和列表） -->
      <view class="main-content">
        <!-- 搜索框 -->
        <view class="search-container">
          <view class="search-box">
            <image class="search-icon" src="/static/icons/search.png" mode="aspectFit"></image>
            <input 
              class="search-input" 
              type="text" 
              v-model="searchText" 
              placeholder="请输入发货单号/销售订单号/产品型号搜索" 
            />
            <view v-if="searchText" class="clear-icon" @click="clearSearch">×</view>
          </view>
          <view class="search-actions">
            <view class="search-btn" @click="handleSearch">
              <text>搜索</text>
            </view>
            <view class="sync-btn" @click="handleSync">
              <text>同步</text>
            </view>
          </view>
        </view>
        
        <!-- 内容区域 -->
        <view class="content">
          <!-- 列表内容 -->
          <scroll-view class="list-container" scroll-y @scrolltolower="loadMore" refresher-enabled
            :refresher-triggered="isRefreshing" @refresherrefresh="onRefresh">
            <!-- 无数据提示 -->
            <view v-if="displayList.length === 0 && !loading" class="loading">暂无数据</view>
            
            <!-- 卡片列表 -->
            <view v-for="(item, index) in displayList" :key="index" class="list-item">
              <view class="list-item-header">
                <view class="list-item-customer">{{ item.customer }}</view>
                <view class="list-item-status">
                  <view class="status-tag" :class="getStatusClass(item.status)">{{ item.status }}</view>
                  <text class="bill-type" :class="item.billType === 'FH' ? 'bill-type-fh' : 'bill-type-th'">{{ item.billType === 'FH' ? '发货单' : '退货单' }}</text>
                </view>
              </view>
              <view class="list-item-row">
                <view class="list-item-id">发货单号: {{ item.orderNo }}</view>
                <view class="list-item-sale-no">销售单号: {{ item.saleNo || '无' }}</view>
              </view>
              <view class="list-item-info">
                <view class="list-item-type">业务类型: {{ item.businessType || '无' }}</view>
                <view class="list-item-create-date">创建日期: {{ item.createDate || '无' }}</view>
              </view>
              <view class="list-item-info list-item-bottom-row">
                <view class="list-item-user">{{ item.receiver || '无收件人' }}</view>
                <view class="list-item-department">{{ item.department }}</view>
                <view class="list-item-shipvia">发运方式: {{ item.shipVia || '无' }}</view>
                <view class="list-item-date">发货日期: {{ item.shipDate || '无' }}</view>
              </view>
              <view class="list-item-actions">
                <view class="list-action-button primary" @click="viewDetail(item)">详情</view>
                <view class="list-action-button warning" @click="executeItem(item)">执行</view>
                <view class="list-action-button danger" @click="showDeleteConfirm(item)">删除</view>
              </view>
            </view>
            
            <!-- 加载中提示 -->
            <view v-if="loading" class="loading">加载中...</view>
          </scroll-view>
        </view>
      </view>
      
      <!-- 删除确认模态框 -->
      <view v-if="showDeleteModalFlag" class="modal" @click.self="hideDeleteModal">
        <view class="modal-content delete-confirm">
          <view class="modal-header">
            <text>删除确认</text>
          </view>
          <view class="modal-body">
            <text>确定要删除 {{ currentItem.customer }} 的发货工单吗？</text>
          </view>
          <view class="modal-footer">
            <button class="modal-button cancel" @click="hideDeleteModal">取消</button>
            <button class="modal-button confirm" @click="deleteItem">确认</button>
          </view>
        </view>
      </view>
    </view>
  </template>
  
  <script setup>
  import { ref, onMounted, nextTick, computed, defineExpose } from 'vue';
  import { useUserStore } from '@/store/user';
  import { getOrderInfo, deleteOrder, syncDispatchInfo, deleteDeliverOrder, printDeliverOrder, submitPRDDeliver } from '@/api/deliver';
  
  // 导入uni-nav-bar组件
  import uniNavBar from '@dcloudio/uni-ui/lib/uni-nav-bar/uni-nav-bar.vue'

  // 用户状态管理
  const userStore = useUserStore();
  
  // 判断是否为H5环境
  const isH5 = computed(() => {
    // #ifdef H5
    return true;
    // #endif
    
    // #ifndef H5
    return false;
    // #endif
  });
  
  // 分页加载参数
  const pageSize = 20;
  const currentPage = ref(1);
  const loading = ref(false);
  const totalPages = ref(1);
  const totalCount = ref(0);
  
  // 搜索相关
  const searchText = ref('');
  
  // 模态框状态
  const showDeleteModalFlag = ref(false);
  const currentItem = ref({});
  
  // 订单列表数据
  const orderList = ref([]);
  
  // 当前显示的列表数据
  const displayList = ref([]);
  
  // 下拉刷新状态
  const isRefreshing = ref(false);
  
  // 获取订单列表
  const fetchOrders = async (page = 1, isLoadMore = false) => {
    if (loading.value) return;
    
    loading.value = true;
    
    // 构建查询参数
    const params = {
      page: page,
      limit: pageSize,
      CFlag: '238'  // 使用CFlag=238获取发货信息
    };
    
    // 创建Data对象，用于传递查询条件
    const dataParams = {
      No: "",
      Item: "",
      Name: "",
      MNo: "",
      MName: "",
      Status: "",
      BDate: "",
      EDate: "",
      A: "",
      B: "",
      C: "",
      D: ""
    };
    
    // 如果有搜索关键词，根据前缀确定查询字段
    const trimmedSearch = searchText.value.trim();
    if (trimmedSearch) {
      // 发货单号以FH开头，放在A字段查询
      if (trimmedSearch.toUpperCase().startsWith('FH')) {
        dataParams.A = trimmedSearch;
      } 
      // 销售单号以SO开头，放在NO字段查询
      else if (trimmedSearch.toUpperCase().startsWith('SO')) {
        dataParams.No = trimmedSearch;
      } 
      // 其他情况视为产品型号，放在Name字段查询
      else {
        dataParams.Name = trimmedSearch;
      }
    }
    
    // 将Data对象添加到params中
    params.Data = JSON.stringify(dataParams);
    
    try {
      const result = await getOrderInfo(params);
      
      // 处理返回数据
      if (result && result.data) {
        // 标准格式返回
        processOrderData(result.data, isLoadMore);
        // 更新总记录数和总页数
        totalCount.value = result.count || 0;
        totalPages.value = Math.ceil(totalCount.value / pageSize);
      } else if (result && Array.isArray(result)) {
        // 直接返回数组
        processOrderData(result, isLoadMore);
        // 假设总数
        totalCount.value = result.length;
        totalPages.value = 1;
      } else if (result && result.NumCount) {
        // 单个对象返回
        processOrderData([result], isLoadMore);
        // 设置总数
        totalCount.value = 1;
        totalPages.value = 1;
      } else {
        if (!isLoadMore) {
          orderList.value = [];
          displayList.value = [];
        }
        
      }
    } catch (error) {
      if (!isLoadMore) {
        orderList.value = [];
        displayList.value = [];
      }
      uni.showToast({
        title: '获取数据失败',
        icon: 'none'
      });
    } finally {
      // 确保无论成功失败都重置加载状态
      loading.value = false;
      // 重置下拉刷新状态
      isRefreshing.value = false;
    }
  };
  
  // 处理订单数据
  const processOrderData = (data, isLoadMore) => {
    // 格式化数据用于显示
    const formattedData = data.map(item => ({
      id: item.DLID,
      orderNo: item.DNo,
      saleNo: item.SaleNo,  // 添加销售单号
      customer: item.CustName,
      receiver: item.Consignee,
      department: item.SaleDept,
      shipDate: item.ShipDate2, // 发货日期改为ShipDate2
      createDate: item.DDate2, // 创建日期
      businessType: item.Dtype, // 业务类型
      shipVia: item.ShipVia, // 发运方式
      status: item.Status,
      hospital: item.Hospital,
      address: item.Addr, // 保留但不显示
      phone: item.Phone,
      trackingNo: item.TrackingNo,
      billType: item.BillType, // 添加单据类型字段
      // 保留原始数据
      originalData: item
    }));
    
    if (isLoadMore) {
      // 加载更多时，追加数据
      orderList.value = [...orderList.value, ...data];
      displayList.value = [...displayList.value, ...formattedData];
    } else {
      // 新查询时，替换数据
      orderList.value = data;
      displayList.value = formattedData;
    }
  };
  
  // 加载更多数据
  const loadMore = () => {
    if (loading.value) return;
    
    if (currentPage.value >= totalPages.value) {
      // 已加载全部数据
      return;
    }
    
    currentPage.value++;
    fetchOrders(currentPage.value, true);
  };
  
  // 清除搜索
  const clearSearch = () => {
    searchText.value = '';
    // 执行搜索
    currentPage.value = 1;
    displayList.value = [];
    fetchOrders(1, false);
  };
  
  // 手动触发搜索
  const handleSearch = () => {
    currentPage.value = 1;
    displayList.value = [];
    fetchOrders(1, false);
  };
  
  // 同步数据
  const handleSync = async () => {
    try {
      // 获取输入框内容并去除空格
      const DNo = searchText.value.trim();
      
      // 检查输入框是否为空
      if (DNo === '') {
        uni.showToast({
          title: '请输入同步的发货单号！',
          icon: 'none',
          duration: 2000
        });
        return;
      }
      
      // 检查是否包含中文字符
      if (/[\u4e00-\u9fa5]/.test(DNo)) {
        uni.showToast({
          title: '发货单号中存在中文，请确认！',
          icon: 'none',
          duration: 2000
        });
        return;
      }
      
      // 检查是否是发货单号格式（以FH开头）
      if (!DNo.toUpperCase().startsWith('FH')) {
        uni.showToast({
          title: '请输入以FH开头的发货单号！',
          icon: 'none',
          duration: 2000
        });
        return;
      }
      
      uni.showLoading({
        title: '同步中...',
        mask: true
      });
      
      // 调用同步API，使用输入框内容作为单号
      const result = await syncDispatchInfo(DNo);
      
      // 隐藏加载提示
      uni.hideLoading();
      
      // 处理同步结果
      if (result && result.Msg === 'Success') {
        // 显示成功消息
        uni.showToast({
          title: result.Message || '同步成功',
          icon: 'success',
          duration: 2000
        });
        // 刷新列表数据
        currentPage.value = 1;
        displayList.value = [];
        fetchOrders(1, false);
      } else if (result && result.Msg === 'Error') {
        // 显示错误消息
        uni.showToast({
          title: result.Message || '同步失败',
          icon: 'none',
          duration: 2000
        });
      } else if (result && result.Msg === 'LoginError') {
        // 显示登录失效消息
        uni.showToast({
          title: result.Message || '登录失效',
          icon: 'none',
          duration: 2000
        });
        
        // 延迟跳转到登录页
        setTimeout(() => {
          uni.reLaunch({
            url: '/pages/login/login'
          });
        }, 1500);
      } else {
        uni.showToast({
          title: '同步失败',
          icon: 'none',
          duration: 2000
        });
      }
    } catch (error) {
      uni.hideLoading();
      uni.showToast({
        title: '同步失败',
        icon: 'none',
        duration: 2000
      });
    }
  };
  
  // 执行订单
  const executeItem = async (item) => {
    try {
      
      // // 构建请求参数，与web端保持一致
      // const params = {
      //   No: "",
      //   Item: "",
      //   Name: "",
      //   MNo: "",
      //   MName: "",
      //   A: "",
      //   B: item.id, // DLID
      //   C: item.orderNo, // DNo
      //   D: "",
      //   E: "",
      //   F: "",
      //   Flag: "5" // 标识，与web端保持一致
      // };
      
      // // 调用PRDDeliver接口
      // const result = await submitPRDDeliver(params);
      
      // 处理响应结果
      // if (result && result.Msg === 'Success') {
      if (true) {
        // 检查通过，继续执行
        // 将订单数据保存到本地缓存，以备事件通道失败时使用
        try {
          // 使用订单号作为缓存的key
          const cacheKey = `orderDetail_${item.orderNo}`;
          uni.setStorageSync(cacheKey, JSON.stringify(item.originalData));
          
          // 同时保存lastOrderNo和lastItemId，用于executeDeliver.vue页面恢复
          uni.setStorageSync('lastOrderNo', item.orderNo);
          uni.setStorageSync('lastItemId', item.id);
          
        } catch (cacheError) {
        }
        
        // #ifdef APP-PLUS || MP-WEIXIN
        // 跳转到执行页面
        uni.navigateTo({
          url: './executeDeliver?id=' + item.id + '&orderNo=' + item.orderNo + '&timestamp=' + new Date().getTime(),
          success: (res) => {
            try {
              // 传递完整数据给执行页面
              res.eventChannel.emit('deliverExecute', { 
                id: item.id,
                orderNo: item.orderNo,
                item: item.originalData
              });
            } catch (emitError) {
            }
          },
          fail: (error) => {
            uni.showToast({
              title: '页面跳转失败',
              icon: 'none'
            });
          }
        });
        // #endif
        
        // #ifdef H5
        // H5环境通过URL参数传递完整数据
        try {
          const orderData = encodeURIComponent(JSON.stringify(item.originalData));
          uni.navigateTo({
            url: './executeDeliver?orderData=' + orderData + '&orderNo=' + item.orderNo + '&id=' + item.id + '&timestamp=' + new Date().getTime(),
            fail: (error) => {
              uni.showToast({
                title: '页面跳转失败',
                icon: 'none'
              });
            }
          });
        } catch (encodeError) {
          // 如果编码失败，使用基本参数跳转
          uni.navigateTo({
            url: './executeDeliver?orderNo=' + item.orderNo + '&id=' + item.id
          });
        }
        // #endif
      } else if (result && result.Msg === 'Error') {
        // 错误处理
        uni.showToast({
          title: result.Message || '检查失败',
          icon: 'none',
          duration: 2000
        });
        
        // 刷新列表
        currentPage.value = 1;
        displayList.value = [];
        fetchOrders(1, false);
      } else if (result && result.Msg === 'LoginError') {
        // 登录失效处理
        uni.showToast({
          title: result.Message || '登录失效',
          icon: 'none',
          duration: 2000
        });
        
        // 跳转到登录页
        setTimeout(() => {
          uni.reLaunch({
            url: '/pages/login/login'
          });
        }, 1500);
      } else {
        // 其他错误
        uni.showToast({
          title: '检查失败，请重试',
          icon: 'none',
          duration: 2000
        });
      }
    } catch (error) {
      uni.hideLoading();
      uni.showToast({
        title: '系统出错，请重试！',
        icon: 'none',
        duration: 2000
      });
    }
  };
  
  // 查看详情
  const viewDetail = (item) => {
    // 将订单数据保存到本地缓存，以备事件通道失败时使用
    try {
      // 使用订单号作为缓存的key
      const cacheKey = `orderDetail_${item.orderNo}`;
      uni.setStorageSync(cacheKey, JSON.stringify(item.originalData));
    } catch (cacheError) {
    }
    
    // #ifdef APP-PLUS || MP-WEIXIN
    // 跳转到详情页面
    uni.navigateTo({
      url: './deliverItem?id=' + item.id + '&orderNo=' + item.orderNo + '&timestamp=' + new Date().getTime(),
      success: (res) => {
        try {
          // 传递完整数据给详情页
          res.eventChannel.emit('deliverDetail', { 
            id: item.id,
            orderNo: item.orderNo, 
            from: 'index',
            item: item.originalData
          });
        } catch (emitError) {
        }
      },
      fail: (error) => {
        uni.showToast({
          title: '页面跳转失败',
          icon: 'none'
        });
      }
    });
    // #endif
    
    // #ifdef H5
    // H5环境通过URL参数传递完整数据
    try {
      const orderData = encodeURIComponent(JSON.stringify(item.originalData));
      uni.navigateTo({
        url: './deliverItem?orderData=' + orderData + '&orderNo=' + item.orderNo + '&id=' + item.id + '&timestamp=' + new Date().getTime(),
        fail: (error) => {
          uni.showToast({
            title: '页面跳转失败',
            icon: 'none'
          });
        }
      });
    } catch (encodeError) {
      // 如果编码失败，使用基本参数跳转
      uni.navigateTo({
        url: './deliverItem?orderNo=' + item.orderNo + '&id=' + item.id
      });
    }
    // #endif
  };
  
  // 显示删除确认
  const showDeleteConfirm = (item) => {
    currentItem.value = item;
    showDeleteModalFlag.value = true;
  };
  
  // 删除项目
  const deleteItem = async () => {
    try {
      if (!currentItem.value || !currentItem.value.id) {
        uni.showToast({
          title: '无法获取单据ID',
          icon: 'none',
          duration: 2000
        });
        return;
      }
      
      // 获取DLID和DNo
      const dlid = currentItem.value.id;
      const dno = currentItem.value.orderNo;
      
      // 显示加载提示
      uni.showLoading({
        title: '删除中...',
        mask: true
      });
      
      // 调用删除单据API，使用与web端相同的接口
      const result = await deleteDeliverOrder(dlid, dno);
      
      // 隐藏加载提示
      uni.hideLoading();
      
      // 处理响应结果，与web端保持一致
      if (result && result.Msg === 'Success') {
        // 从显示列表中移除
        const displayIndex = displayList.value.findIndex(item => item.id === currentItem.value.id);
        if (displayIndex !== -1) {
          displayList.value.splice(displayIndex, 1);
        }
        
        // 从订单列表中移除
        const index = orderList.value.findIndex(item => item.DLID === currentItem.value.id);
        if (index !== -1) {
          orderList.value.splice(index, 1);
        }
        
        // 显示成功消息
        uni.showToast({
          title: result.Message || '删除成功',
          icon: 'success'
        });
        
        // 隐藏模态框
        hideDeleteModal();
      } else if (result && result.Msg === 'Error') {
        // 错误处理
        uni.showToast({
          title: result.Message || '删除失败',
          icon: 'none',
          duration: 2000
        });
      } else if (result && result.Msg === 'LoginError') {
        // 登录失效处理
        uni.showToast({
          title: result.Message || '登录失效',
          icon: 'none',
          duration: 2000
        });
        
        // 跳转到登录页
        setTimeout(() => {
          uni.reLaunch({
            url: '/pages/login/login'
          });
        }, 1500);
      } else {
        // 其他错误
        uni.showToast({
          title: '删除失败',
          icon: 'none',
          duration: 2000
        });
      }
    } catch (error) {
      // 隐藏加载提示
      uni.hideLoading();
      
      // 显示错误消息
      uni.showToast({
        title: error.message || '删除失败',
        icon: 'none',
        duration: 2000
      });
    }
  };
  
  // 模态框控制
  const hideDeleteModal = () => {
    showDeleteModalFlag.value = false;
  };
  
  // 返回上一页
  const goBack = () => {
    // 使用switchTab跳转到首页
    uni.switchTab({
      url: '/pages/index/index'
    });
  };
  
  // 获取状态标签类
  const getStatusClass = (status) => {
    switch (status) {
      case '待扫描':
        return 'status-warning'; // 黄色警告色
      case '已装箱':
      case '已发货':
        return 'status-success'; // 绿色成功色
      case '发货中':
        return 'status-info'; // 灰色信息色
      case '退货中':
        return 'status-return'; // 红色退货状态
      default:
        return 'status-default'; // 默认样式
    }
  };
  
  // 下拉刷新处理
  const onRefresh = () => {
    isRefreshing.value = true;
    currentPage.value = 1;
    displayList.value = [];
    fetchOrders(1, false);
  };
  
  // 打印订单
  const printItem = async (item) => {
    try {
      if (!item || !item.id) {
        uni.showToast({
          title: '无法获取单据ID',
          icon: 'none',
          duration: 2000
        });
        return;
      }
      
      // 获取DLID和DNo
      const dlid = item.id;
      const dno = item.orderNo;
      
      // 显示加载提示
      uni.showLoading({
        title: '打印中...',
        mask: true
      });
      
      // 调用打印API
      const result = await printDeliverOrder(dlid, dno);
      
      // 隐藏加载提示
      uni.hideLoading();
      
      // 处理响应结果
      if (result && result.Msg === 'Success') {
        // 打印成功
        uni.showToast({
          title: result.Message || '打印成功',
          icon: 'success'
        });
      } else if (result && result.Msg === 'Error') {
        // 错误处理
        uni.showToast({
          title: result.Message || '打印失败',
          icon: 'none',
          duration: 2000
        });
      } else if (result && result.Msg === 'LoginError') {
        // 登录失效处理
        uni.showToast({
          title: result.Message || '登录失效',
          icon: 'none',
          duration: 2000
        });
        
        // 跳转到登录页
        setTimeout(() => {
          uni.reLaunch({
            url: '/pages/login/login'
          });
        }, 1500);
      } else {
        // 其他错误
        uni.showToast({
          title: '打印失败',
          icon: 'none',
          duration: 2000
        });
      }
    } catch (error) {
      // 隐藏加载提示
      uni.hideLoading();
      
      // 显示错误消息
      uni.showToast({
        title: error.message || '打印失败',
        icon: 'none',
        duration: 2000
      });
    }
  };
  
  // 生命周期
  onMounted(() => {
    // 禁用页面滚动
    uni.setPageMeta({
      pageStyle: {
        disableScroll: true
      }
    });
    
    // 检查是否已登录
    checkLogin();
    
    // 初始加载数据
    fetchOrders(1, false);
  });
  
  // 检查登录状态
  const checkLogin = () => {
    // 使用userStore的isLoggedIn getter
    const isLoggedIn = userStore.isLoggedIn;
    
    if (!isLoggedIn) {
      setTimeout(() => {
        // 再次检查，防止状态更新延迟
        if (!userStore.isLoggedIn) {
          uni.navigateTo({
            url: '/pages/login/login'
          });
        }
      }, 100);
    }
  };
  
  // 确保方法在模板中可用
  defineExpose({
    handleSearch,
    handleSync,
    executeItem,
    clearSearch,
    viewDetail,
    loadMore,
    onRefresh,
    showDeleteConfirm,
    deleteItem,
    hideDeleteModal,
    goBack,
    getStatusClass,
    printItem
  });
  </script>
  
  <style scoped>
  /* 导航栏容器 */
  .nav-bar-container {
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
    position: relative;
    z-index: 100;
  }
  
  /* 主体内容区域 */
  .main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    width: 100%;
    overflow: hidden;
    position: relative;
    margin-top: 10px; /* 减小与标题栏的距离 */
  }
  
  /* 容器样式 */
  .container {
    height: 100vh;
    width: 100%;
    display: flex;
    flex-direction: column;
    background-color: #f7f9fc;
    position: relative;
    overflow: hidden;
    margin: 0;
    border: none;
    padding: 0;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, sans-serif;
  }
  
  /* 搜索框容器 */
  .search-container {
    padding: 10px;
    background-color: #fff;
    position: sticky;
    top: 0;
    z-index: 10;
    border-bottom: none;
    margin-bottom: 0;
    box-shadow: none;
  }
  
  /* 搜索框样式 */
  .search-box {
    display: flex;
    align-items: center;
    height: 40px;
    border-radius: 20px;
    padding: 0 15px;
    border: 1px solid rgba(12, 135, 61, 0.1);
    background-color: #fff;
    transition: all 0.3s ease;
    margin-bottom: 8px;
  }
  
  .search-box:focus-within {
    border-color: #0c873d;
    box-shadow: 0 0 0 2px rgba(12, 135, 61, 0.1);
  }
  
  .search-icon {
    width: 18px;
    height: 18px;
    margin-right: 10px;
    opacity: 0.6;
  }
  
  .search-input {
    flex: 1;
    height: 100%;
    font-size: 15px;
    color: #333;
    border: none;
    outline: none;
    background: transparent;
  }
  
  .search-input::placeholder {
    color: #999;
  }
  
  .clear-icon {
    font-size: 16px;
    color: #999;
    padding: 4px;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.2s ease;
  }
  
  .clear-icon:active {
    background-color: rgba(12, 135, 61, 0.1);
  }
  
  /* 搜索操作按钮区域 */
  .search-actions {
    display: flex;
    justify-content: space-between;
    margin-top: 8px;
    flex-direction: row;
  }
  
  .search-btn, .sync-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 4px 12px;
    border-radius: 16px;
    font-size: 13px;
    font-weight: 500;
    transition: all 0.2s ease;
    height: 28px;
  }
  
  .search-btn {
    background-color: #0c873d;
    color: white;
    margin-right: 10px;
    flex: 1;
  }
  
  .sync-btn {
    background-color: #f0f9ff;
    color: #0369a1;
    border: 1px solid #bae6fd;
    flex: 1;
  }
  
  .search-btn:active {
    background-color: #0a6e31;
  }
  
  .sync-btn:active {
    background-color: #e0f2fe;
  }
  
  .action-icon {
    display: none;
  }
  
  /* 内容区域 */
  .content {
    flex: 1;
    position: relative;
    overflow: hidden;
    width: 100%;
    margin: 0;
    padding: 0;
    display: flex;
    flex-direction: column;
  }
  
  /* 列表容器 */
  .list-container {
    flex: 1;
    height: 100%;
    box-sizing: border-box;
    padding: 0 0 12px 0;
  }
  
  /* 卡片样式 */
  .list-item {
    background-color: #ffffff;
    margin: 8px 12px;
    padding: 12px 16px;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    position: relative;
    transition: none;
  }
  
  .list-item:active {
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  }
  
  .list-item::before {
    content: none;
  }
  
  .list-item-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
  }
  
  .list-item-customer {
    font-weight: 600;
    font-size: 15px;
    color: #1e293b;
    line-height: 1.2;
    padding-right: 0;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 75%;
  }
  
  .list-item-status {
    margin-left: auto;
  }
  
  .list-item-row {
    margin-bottom: 4px;
    position: relative;
    display: flex;
    flex-wrap: wrap;
  }
  
  .list-item-id {
    color: #64748b;
    font-size: 13px;
    margin-bottom: 8px;
    letter-spacing: 0.3px;
    display: inline-block;
    margin-right: 10px;
  }
  
  .list-item-sale-no {
    color: #64748b;
    font-size: 13px;
    margin-bottom: 8px;
    letter-spacing: 0.3px;
    display: inline-block;
  }
  
  .list-item-info {
    display: flex;
    border-top: 1px solid #f1f5f9;
    padding-top: 8px;
    font-size: 12px;
    align-items: flex-start;
    flex-wrap: wrap;
    justify-content: space-between;
  }
  
  .list-item-hospital {
    width: 100%;
    margin-bottom: 8px;
    color: #334155;
    font-size: 13px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  
  .list-item-type {
    color: #334155;
    font-size: 13px;
    margin-right: 12px;
    margin-bottom: 8px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  
  .list-item-create-date {
    color: #334155;
    font-size: 13px;
    margin-bottom: 8px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  
  .list-item-user {
    margin-right: 12px;
    color: #334155;
    display: flex;
    align-items: center;
  }
  
  .list-item-user::before {
    content: "";
    display: inline-block;
    width: 6px;
    height: 6px;
    background-color: #0c873d;
    border-radius: 50%;
    margin-right: 4px;
  }
  
  .list-item-department {
    color: #64748b;
    margin-right: 12px;
  }
  
  .list-item-shipvia {
    color: #64748b;
    margin-right: 12px;
  }
  
  .list-item-date {
    color: #64748b;
    margin-left: 4px;
  }
  
  .list-item-bottom-row {
    border-bottom: 1px solid #f1f5f9;
    padding-bottom: 8px;
    margin-bottom: 8px;
  }
  
  .list-item-actions {
    display: flex;
    justify-content: space-between;
    gap: 8px;
  }
  
  .list-action-button {
    flex: 1;
    text-align: center;
    padding: 6px 0;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 500;
    transition: all 0.2s;
  }
  
  .list-action-button.primary {
    background-color: rgba(12, 135, 61, 0.12);
    color: #0c873d;
  }
  
  .list-action-button.primary:active {
    background-color: rgba(12, 135, 61, 0.2);
  }
  
  .list-action-button.warning {
    background-color: rgba(230, 162, 60, 0.12);
    color: #E6A23C;
  }
  
  .list-action-button.warning:active {
    background-color: rgba(230, 162, 60, 0.2);
  }
  
  .list-action-button.danger {
    background-color: rgba(240, 56, 56, 0.12);
    color: #e03131;
  }
  
  .list-action-button.danger:active {
    background-color: rgba(240, 56, 56, 0.2);
  }
  
  .list-action-button.print {
    background-color: #f0f9ff;
    color: #0369a1;
    border: 1px solid #bae6fd;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 6px 0;
  }
  
  .list-action-button.print:active {
    background-color: #e0f2fe;
  }
  
  .print-icon {
    display: none;
  }
  
  /* 加载中提示 */
  .loading {
    text-align: center;
    padding: 15px 0;
    color: #64748b;
    font-size: 14px;
    width: 100%;
    opacity: 0.8;
  }
  
  /* 模态框样式 */
  .modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(15, 23, 42, 0.6);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 100;
    animation: fadeIn 0.2s ease-out;
  }
  
  @keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
  }
  
  .modal-content {
    width: 85%;
    max-width: 360px;
    background-color: #fff;
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    animation: slideUp 0.3s ease-out;
    transform: translateY(0);
  }
  
  @keyframes slideUp {
    from { transform: translateY(20px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
  }
  
  .modal-header {
    padding: 16px;
    background-color: #ffffff;
    color: #1e293b;
    text-align: center;
    border-bottom: 1px solid #f1f5f9;
    font-weight: 600;
    font-size: 16px;
  }
  
  .modal-body {
    padding: 16px;
    color: #334155;
    font-size: 14px;
    line-height: 1.5;
  }
  
  .modal-footer {
    padding: 12px 16px;
    display: flex;
    justify-content: space-between;
    border-top: 1px solid #f1f5f9;
  }
  
  .delete-confirm .modal-footer {
    padding: 12px 16px;
    border-top: none;
    justify-content: center;
    gap: 16px;
  }
  
  .delete-confirm .modal-button {
    flex: 0 1 auto;
    height: 36px;
    width: 90px;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
    font-size: 14px;
    padding: 0;
  }
  
  .delete-confirm .modal-button.cancel {
    border-right: none;
  }
  
  .modal-button {
    padding: 8px 12px;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
    background-color: #fff;
    color: #64748b;
    margin: 0;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.2s;
    flex: 1;
  }
  
  .modal-button:active {
    background-color: #f8fafc;
  }
  
  .modal-button.cancel {
    background-color: #ffffff;
    color: #64748b;
  }
  
  .modal-button.confirm {
    background-color: #0c873d;
    color: white;
  }
  
  .modal-button.primary {
    background-color: #0c873d;
    color: white;
    border: none;
  }
  
  .modal-button.primary:active {
    background-color: #0a6e31;
  }
  
  /* 安全区域适配 */
  /* #ifdef APP-PLUS */
  .container {
    padding-top: 0;
    padding-bottom: constant(safe-area-inset-bottom);
    padding-bottom: env(safe-area-inset-bottom);
  }
  
  /* APP环境下的增强立体感样式 */
  .header {
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.12) !important;
  }
  /* #endif */
  
  /* 在样式部分添加 */
  .status-tag {
    padding: 2px 6px;
    border-radius: 10px;
    font-size: 8px;
    margin-right: 6px;
    color: white;
    display: inline-block;
  }
  
  .bill-type {
    font-size: 11px;
    color: #64748b;
    margin-left: 4px;
    display: inline-block;
  }
  
  .bill-type-fh {
    color: #67C23A; /* 绿色，对应发货单 */
    font-weight: 600;
  }
  
  .bill-type-th {
    color: #F56C6C; /* 红色，对应退货单 */
    font-weight: 600;
  }
  
  .status-warning {
    background-color: #E6A23C; /* 黄色警告色，对应 el-tag type="warning" */
  }
  
  .status-success {
    background-color: #67C23A; /* 绿色成功色，对应 el-tag type="success" */
  }
  
  .status-info {
    background-color: #909399; /* 灰色信息色，对应 el-tag type="info" */
  }
  
  .status-return {
    background-color: #F56C6C; /* 红色退货状态，对应 el-tag type="danger" */
  }
  
  .status-default {
    background-color: #606266; /* 默认灰色 */
  }
  
  /* 添加样式 */
  .list-item-date-wrapper {
    display: flex;
    align-items: center;
    margin-left: auto;
  }
  
  .list-item-date {
    color: #64748b;
    margin-left: 4px;
  }
  
  /* 删除金额相关样式 */
  .list-item-amount {
    display: none;
  }
  
  .list-item-customer {
    font-weight: 600;
    font-size: 15px;
    color: #1e293b;
    line-height: 1.2;
    margin-bottom: 2px;
    padding-right: 0;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 63%;
  }
  </style>
  
  <!-- 全局重置样式，防止样式冲突 -->
  <style>
  /* 必须应用于根元素 */
  page {
    height: 100vh;
    width: 100%;
    margin: 0;
    padding: 0;
    overflow: hidden;
    background-color: #f7f9fc !important;
    position: relative;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, sans-serif;
    color: #1e293b;
  }
  
  /* 全局header样式 */
  .header {
    background-color: #ffffff !important;
    color: #2c3e50 !important;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.03);
  }
  
  .header text {
    color: #2c3e50 !important;
  }
  
  /* #ifdef APP-PLUS */
  /* APP环境下的导航栏适配 */
  uni-page-head {
    height: 44px !important;
  }
  
  /* 自定义导航栏后，原生导航栏需要隐藏 */
  .uni-page-head-hd {
    display: none !important;
  }
  /* #endif */
  </style> 
{"name": "睿械通", "appid": "__UNI__83A54F6", "description": "生产执行、发货系统", "versionName": "1.0.4", "versionCode": "100", "transformPx": false, "app-plus": {"usingComponents": true, "nvueStyleCompiler": "uni-app", "compilerVersion": 3, "splashscreen": {"alwaysShowBeforeRender": true, "waiting": true, "autoclose": true, "delay": 0}, "modules": {"Camera": {}, "Bluetooth": {}}, "distribute": {"android": {"permissions": ["<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\"/>", "<uses-permission android:name=\"android.permission.VIBRATE\"/>", "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>", "<uses-feature android:name=\"android.hardware.camera.autofocus\"/>", "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\"/>", "<uses-permission android:name=\"android.permission.CAMERA\"/>", "<uses-permission android:name=\"android.permission.FLASHLIGHT\"/>", "<uses-feature android:name=\"android.hardware.camera\"/>", "<uses-permission android:name=\"android.permission.WRITE_EXTERNAL_STORAGE\"/>", "<uses-permission android:name=\"android.permission.INTERNET\"/>", "<uses-permission android:name=\"android.permission.BLUETOOTH\"/>", "<uses-permission android:name=\"android.permission.BLUETOOTH_ADMIN\"/>", "<uses-permission android:name=\"android.permission.ACCESS_COARSE_LOCATION\"/>", "<uses-permission android:name=\"android.permission.ACCESS_FINE_LOCATION\"/>"], "minSdkVersion": 21, "abiFilters": ["armeabi-v7a", "arm64-v8a"]}, "ios": {"dSYMs": false, "privacyDescription": {"NSPhotoLibraryUsageDescription": "需要访问您的相册来选择图片", "NSCameraUsageDescription": "需要使用您的相机来拍照或扫描", "NSBluetoothPeripheralUsageDescription": "需要使用蓝牙来连接打印机", "NSBluetoothAlwaysUsageDescription": "需要使用蓝牙来连接打印机", "NSLocationWhenInUseUsageDescription": "需要使用位置服务来搜索蓝牙设备", "NSLocationAlwaysUsageDescription": "需要使用位置服务来搜索蓝牙设备"}}, "sdkConfigs": {"network": {"request": true}}, "icons": {"android": {"hdpi": "unpackage/res/icons/72x72.png", "xhdpi": "unpackage/res/icons/96x96.png", "xxhdpi": "unpackage/res/icons/144x144.png", "xxxhdpi": "unpackage/res/icons/192x192.png"}, "ios": {"appstore": "unpackage/res/icons/1024x1024.png", "ipad": {"app": "unpackage/res/icons/76x76.png", "app@2x": "unpackage/res/icons/152x152.png", "notification": "unpackage/res/icons/20x20.png", "notification@2x": "unpackage/res/icons/40x40.png", "proapp@2x": "unpackage/res/icons/167x167.png", "settings": "unpackage/res/icons/29x29.png", "settings@2x": "unpackage/res/icons/58x58.png", "spotlight": "unpackage/res/icons/40x40.png", "spotlight@2x": "unpackage/res/icons/80x80.png"}, "iphone": {"app@2x": "unpackage/res/icons/120x120.png", "app@3x": "unpackage/res/icons/180x180.png", "notification@2x": "unpackage/res/icons/40x40.png", "notification@3x": "unpackage/res/icons/60x60.png", "settings@2x": "unpackage/res/icons/58x58.png", "settings@3x": "unpackage/res/icons/87x87.png", "spotlight@2x": "unpackage/res/icons/80x80.png", "spotlight@3x": "unpackage/res/icons/120x120.png"}}}}, "statusbar": {"immersed": "supportedDevice", "style": "dark", "background": "#FFFFFF"}, "nativePlugins": {"Mpaas-Scan": {"AppId": "ALIPUB1590525051352", "License": "pcRvTm2ngfTlawOfcA2mI1CmuOd2lwusDfgUnUcSra3PuJxx+UIjCVq+TVZ555WBkNLf4XxWMayKRfmydxxxtedc3UDhp+rD99Z8oN5q5eDa55owqf4anVKPHbOQ5bFQcXfOz94jeRwCRjy+Xmr4Ea8VnjT/oxHEa4DRt8ZDEhfG8Bt2WVjpMucgya455Vu7ejncxJcSsTyjCO3BNuIAY2We//5ovRg9peK9hohPrRewwlGKeD7ReKbxUNTY6t890DvNHBOTelQvMC4cfJSlX2rkq2wzgQuog+OYUsO67edXYh4KBZkyQitAgzwVXrvUWIfPYBgGDVpH23bwJY7wuA==", "WorkspaceId": "default", "__plugin_info__": {"name": "支付宝原生扫码插件", "description": "支付宝原生扫码组件，包体积仅0.7MB，15分钟即可完成接入。同时，mPaaS提供「扫码分析」大盘", "platforms": "Android,iOS", "url": "https://ext.dcloud.net.cn/plugin?id=2636", "android_package_name": "com.ruixietong", "ios_bundle_id": "", "isCloud": true, "bought": 1, "pid": "2636", "parameters": {"AppId": {"des": "Android平台的AppId，请填写Android的config文件中的appId对应的值", "key": "mobilegw.appid", "value": ""}, "License": {"des": "Android平台的License,，请填写Android的config文件中的mpaasConfigLicense对应的值", "key": "mpaasConfigLicense", "value": ""}, "WorkspaceId": {"des": "Android平台的WorkspaceId，请填写Android的config文件中的workspaceId对应的值", "key": "workspaceId", "value": ""}}}}}, "network": {"request": {"timeout": 30000, "protocols": ["http", "https"]}}}, "mp-weixin": {"appid": "", "setting": {"urlCheck": false}, "usingComponents": true}, "vueVersion": "3"}
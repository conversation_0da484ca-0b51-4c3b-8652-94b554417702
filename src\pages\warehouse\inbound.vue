<template>
  <view class="container">
    <!-- 使用uni-nav-bar组件 -->
    <view class="nav-bar-container">
      <uni-nav-bar
        :fixed="true"
        :status-bar="true"
        title="入库管理"
        left-icon="left"
        @clickLeft="goBack"
        :border="false"
      />
    </view>
    
    <!-- 主体内容区 -->
    <view class="main-content">
      <!-- 扫码和输入区域 -->
      <view class="scan-container">
        <view class="scan-icon-btn" @click="handleScan">
          <image class="scan-icon-left" src="/static/icons/scan.png" mode="aspectFit"></image>
        </view>
        <view class="input-wrapper" :class="{ 'input-focus': isInputFocused }">
          <input 
            class="code-input" 
            type="text" 
            v-model="codeInput" 
            placeholder="扫描或输入产品条码" 
            @confirm="handleCodeSubmit"
            @focus="handleInputFocus"
            @blur="handleInputBlur"
          />
          <view v-if="codeInput" class="clear-icon" @click.stop="clearCodeInput">×</view>
        </view>
      </view>
      
      <!-- 入库表单 -->
      <view class="inbound-form">
        <view class="form-title">入库信息</view>
        
        <view class="form-item">
          <text class="form-label">产品编号</text>
          <view class="form-input-wrapper">
            <input 
              class="form-input" 
              type="text" 
              v-model="inboundForm.productCode" 
              placeholder="产品编号"
              disabled
            />
          </view>
        </view>
        
        <view class="form-item">
          <text class="form-label">产品名称</text>
          <view class="form-input-wrapper">
            <input 
              class="form-input" 
              type="text" 
              v-model="inboundForm.productName" 
              placeholder="产品名称"
              disabled
            />
          </view>
        </view>
        
        <view class="form-item">
          <text class="form-label">规格型号</text>
          <view class="form-input-wrapper">
            <input 
              class="form-input" 
              type="text" 
              v-model="inboundForm.specification" 
              placeholder="规格型号"
              disabled
            />
          </view>
        </view>
        
        <view class="form-item">
          <text class="form-label">入库数量</text>
          <view class="form-input-wrapper">
            <input 
              class="form-input" 
              type="number" 
              v-model="inboundForm.quantity" 
              placeholder="请输入入库数量"
            />
          </view>
        </view>
        
        <view class="form-item">
          <text class="form-label">入库类型</text>
          <view class="form-input-wrapper">
            <picker 
              class="form-picker" 
              :value="inboundTypeIndex" 
              :range="inboundTypes" 
              @change="handleInboundTypeChange"
            >
              <view class="picker-text">{{ inboundTypes[inboundTypeIndex] || '请选择入库类型' }}</view>
            </picker>
          </view>
        </view>
        
        <view class="form-item">
          <text class="form-label">仓库位置</text>
          <view class="form-input-wrapper">
            <picker 
              class="form-picker" 
              :value="warehouseLocationIndex" 
              :range="warehouseLocations" 
              @change="handleWarehouseLocationChange"
            >
              <view class="picker-text">{{ warehouseLocations[warehouseLocationIndex] || '请选择仓库位置' }}</view>
            </picker>
          </view>
        </view>
        
        <view class="form-item">
          <text class="form-label">备注</text>
          <view class="form-input-wrapper">
            <textarea 
              class="form-textarea" 
              v-model="inboundForm.remark" 
              placeholder="请输入备注信息"
              maxlength="200"
            />
          </view>
        </view>
      </view>
      
      <!-- 提交按钮 -->
      <view class="submit-container">
        <button class="submit-btn" @click="submitInbound">确认入库</button>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, reactive } from 'vue';
import { onLoad } from '@dcloudio/uni-app';
import { useUserStore } from '@/store/user';
import scanCodeUtil from '@/utils/scanCode';

// 导入uni-nav-bar组件
import uniNavBar from '@dcloudio/uni-ui/lib/uni-nav-bar/uni-nav-bar.vue'

// 用户状态管理
const userStore = useUserStore();

// 输入框内容
const codeInput = ref('');

// 输入框焦点状态
const isInputFocused = ref(false);

// 入库表单数据
const inboundForm = reactive({
  productCode: '',
  productName: '',
  specification: '',
  quantity: '',
  inboundType: '',
  warehouseLocation: '',
  remark: ''
});

// 入库类型选择
const inboundTypes = ref(['生产入库', '采购入库', '退货入库', '其他入库']);
const inboundTypeIndex = ref(0);

// 仓库位置选择
const warehouseLocations = ref(['A区-01', 'A区-02', 'B区-01', 'B区-02', 'C区-01', 'C区-02']);
const warehouseLocationIndex = ref(0);

// 处理页面加载
onLoad((options) => {
  // 如果有传入的条码参数，直接处理
  if (options.code) {
    processCode(decodeURIComponent(options.code));
  }
});

// 处理输入框获得焦点
const handleInputFocus = () => {
  isInputFocused.value = true;
};

// 处理输入框失去焦点
const handleInputBlur = () => {
  isInputFocused.value = false;
};

// 处理输入提交
const handleCodeSubmit = () => {
  if (!codeInput.value.trim()) {
    uni.showToast({
      title: '请输入条码',
      icon: 'none'
    });
    return;
  }
  
  // 处理条码
  processCode(codeInput.value.trim());
  
  // 清空输入框
  codeInput.value = '';
};

// 清除输入框
const clearCodeInput = () => {
  codeInput.value = '';
};

// 扫码功能
const handleScan = async () => {
  try {
    // 使用scanCode.js中的扫码功能
    const result = await scanCodeUtil.start({
      preserveGS1Separators: true
    });
    
    // 处理扫描结果
    processCode(result);
  } catch (error) {
    uni.showToast({
      title: '扫码失败',
      icon: 'none'
    });
    console.error('扫码失败:', error);
  }
};

// 处理条码
const processCode = (code) => {
  // 模拟获取产品信息
  // TODO: 实际项目中应该调用API获取产品信息
  setTimeout(() => {
    // 模拟产品数据
    const productInfo = {
      productCode: code,
      productName: '测试产品',
      specification: '规格型号123'
    };
    
    // 更新表单数据
    inboundForm.productCode = productInfo.productCode;
    inboundForm.productName = productInfo.productName;
    inboundForm.specification = productInfo.specification;
    
    uni.showToast({
      title: '已获取产品信息',
      icon: 'success'
    });
  }, 500);
};

// 处理入库类型变更
const handleInboundTypeChange = (e) => {
  inboundTypeIndex.value = e.detail.value;
  inboundForm.inboundType = inboundTypes.value[inboundTypeIndex.value];
};

// 处理仓库位置变更
const handleWarehouseLocationChange = (e) => {
  warehouseLocationIndex.value = e.detail.value;
  inboundForm.warehouseLocation = warehouseLocations.value[warehouseLocationIndex.value];
};

// 提交入库
const submitInbound = () => {
  // 表单验证
  if (!inboundForm.productCode) {
    uni.showToast({
      title: '请先扫描产品条码',
      icon: 'none'
    });
    return;
  }
  
  if (!inboundForm.quantity || inboundForm.quantity <= 0) {
    uni.showToast({
      title: '请输入有效的入库数量',
      icon: 'none'
    });
    return;
  }
  
  // 确保入库类型和仓库位置已选择
  inboundForm.inboundType = inboundTypes.value[inboundTypeIndex.value];
  inboundForm.warehouseLocation = warehouseLocations.value[warehouseLocationIndex.value];
  
  // 显示确认对话框
  uni.showModal({
    title: '确认入库',
    content: `确定要入库 ${inboundForm.quantity} 个 ${inboundForm.productName} 吗？`,
    success: (res) => {
      if (res.confirm) {
        // 执行入库操作
        // TODO: 实际项目中应该调用API执行入库操作
        setTimeout(() => {
          uni.showToast({
            title: '入库成功',
            icon: 'success'
          });
          
          // 重置表单
          resetForm();
        }, 1000);
      }
    }
  });
};

// 重置表单
const resetForm = () => {
  inboundForm.productCode = '';
  inboundForm.productName = '';
  inboundForm.specification = '';
  inboundForm.quantity = '';
  inboundForm.remark = '';
  inboundTypeIndex.value = 0;
  warehouseLocationIndex.value = 0;
};

// 返回上一页
const goBack = () => {
  uni.navigateBack({
    delta: 1
  });
};
</script>

<style scoped>
/* 容器样式 */
.container {
  height: 100vh;
  width: 100%;
  display: flex;
  flex-direction: column;
  background-color: #f7f9fc;
  position: relative;
  overflow: hidden;
  margin: 0;
  border: none;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, sans-serif;
}

/* 导航栏容器 */
.nav-bar-container {
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 100;
}

/* 主体内容区域 */
.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  width: 100%;
  overflow-y: auto;
  position: relative;
  padding: 0 15px;
  margin-top: 10px;
}

/* 扫码和输入区域 */
.scan-container {
  display: flex;
  align-items: center;
  margin: 10px 0;
  width: 100%;
}

.scan-icon-btn {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 10px;
}

.scan-icon-left {
  width: 24px;
  height: 24px;
}

.input-wrapper {
  flex: 1;
  display: flex;
  align-items: center;
  height: 36px;
  border-radius: 8px;
  border: 1px solid #d1d5db;
  background-color: #fff;
  padding: 0 10px;
  position: relative;
  transition: all 0.3s ease;
}

.input-focus {
  border-color: #4CAF50;
  box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.1);
}

.code-input {
  flex: 1;
  height: 100%;
  border: none;
  outline: none;
  font-size: 14px;
  color: #333;
}

.clear-icon {
  font-size: 16px;
  color: #999;
  padding: 4px;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
}

/* 入库表单样式 */
.inbound-form {
  margin: 15px 0;
  background-color: #fff;
  border-radius: 12px;
  padding: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.form-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 15px;
}

.form-item {
  margin-bottom: 15px;
}

.form-label {
  display: block;
  font-size: 14px;
  color: #666;
  margin-bottom: 5px;
}

.form-input-wrapper {
  border: 1px solid #d1d5db;
  border-radius: 8px;
  background-color: #fff;
  overflow: hidden;
}

.form-input {
  width: 100%;
  height: 40px;
  padding: 0 10px;
  font-size: 14px;
  color: #333;
}

.form-input[disabled] {
  background-color: #f9f9f9;
  color: #666;
}

.form-textarea {
  width: 100%;
  height: 80px;
  padding: 10px;
  font-size: 14px;
  color: #333;
}

.form-picker {
  width: 100%;
  height: 40px;
}

.picker-text {
  height: 40px;
  line-height: 40px;
  padding: 0 10px;
  font-size: 14px;
  color: #333;
}

/* 提交按钮样式 */
.submit-container {
  margin: 20px 0;
  padding: 0 15px;
}

.submit-btn {
  width: 100%;
  height: 44px;
  line-height: 44px;
  background-color: #4CAF50;
  color: #fff;
  font-size: 16px;
  border-radius: 8px;
  text-align: center;
}
</style> 
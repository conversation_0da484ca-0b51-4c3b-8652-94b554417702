import request from '@/utils/request';
import { baseUrl } from '@/utils/config';

/**
 * 获取生产工单信息
 * @param {Object} params 查询参数
 * @returns {Promise} 返回工单信息
 */
export function getProductionOrder(params) {
  return request({
    url: `${baseUrl}/api/production/order`,
    method: 'get',
    params
  });
}

/**
 * 获取生产工单列表
 * @param {Object} params 查询参数
 * @returns {Promise} 返回工单列表
 */
export function getProductionOrderList(params) {
  return request({
    url: `${baseUrl}/api/production/orders`,
    method: 'get',
    params
  });
}

/**
 * 提交生产执行记录
 * @param {Object} data 执行记录数据
 * @returns {Promise} 返回提交结果
 */
export function submitProductionExecution(data) {
  return request({
    url: `${baseUrl}/api/production/execute`,
    method: 'post',
    data
  });
}

/**
 * 获取生产执行历史记录
 * @param {Object} params 查询参数
 * @returns {Promise} 返回执行历史记录
 */
export function getProductionHistory(params) {
  return request({
    url: `${baseUrl}/api/production/history`,
    method: 'get',
    params
  });
}

/**
 * 同步生产工单信息
 * @param {String} orderNo 工单编号
 * @returns {Promise} 返回同步结果
 */
export function syncProductionOrder(orderNo) {
  return request({
    url: `${baseUrl}/api/production/sync`,
    method: 'post',
    data: { orderNo }
  });
}

/**
 * 删除生产执行记录
 * @param {String} id 执行记录ID
 * @returns {Promise} 返回删除结果
 */
export function deleteProductionExecution(id) {
  return request({
    url: `${baseUrl}/api/production/execution/${id}`,
    method: 'delete'
  });
}

/**
 * 获取生产统计数据
 * @param {Object} params 查询参数
 * @returns {Promise} 返回统计数据
 */
export function getProductionStatistics(params) {
  return request({
    url: `${baseUrl}/api/production/statistics`,
    method: 'get',
    params
  });
} 
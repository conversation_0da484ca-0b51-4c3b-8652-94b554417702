import { defineConfig } from 'vite'
import uni from '@dcloudio/vite-plugin-uni'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [
    uni(),
  ],
  server: {
    port: 5173, // 指定开发服务器端口，与错误日志中一致
    // 配置代理，解决跨域问题
    proxy: {
      '/Service': {
        target: 'http://*************:3306',
        //target: 'http://localhost:18127',
        // 生产环境测试服务器
        //target: "http://***********:81",
        // 生产环境正式服务器
        //target: "http://***********:80",
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/Service/, ''),
      }
    }
  },
  // 添加 Sass 配置
  css: {
    preprocessorOptions: {
      scss: {
        // 禁用 legacy API 警告
        quietDeps: true,
        // 如果需要添加全局 SCSS 变量，可以在这里添加
        // additionalData: `@import "@/styles/variables.scss";`
      }
    }
  }
})

<template>
  <view class="profile-settings">
    <!-- 状态栏占位 -->
    <!-- #ifdef MP-WEIXIN -->
    <view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>
    <!-- #endif -->

    <!-- #ifdef APP-PLUS -->
    <view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>
    <!-- #endif -->

    <!-- Header with back button -->
    <view class="header">
      <view class="back-btn" @click="navigateBack">
        <image class="back-icon" src="/static/icons/return.png"></image>
      </view>
      <text class="header-title">个人资料</text>
      <view class="placeholder"></view>
    </view>
    <!-- Profile Avatar Section -->
    <view class="avatar-section">
      <view class="avatar-container" @click="changeAvatar">
        <image :src="userInfo && baseURL.serverUrl + userInfo.avatarPath || ''" mode="aspectFill" class="avatar-image">
        </image>
      </view>
    </view>

    <!-- Form Fields -->
    <view class="form-container">
      <view class="form-group">
        <view class="form-label">用户名</view>
        <input type="text" v-model="formData.username" class="form-input" placeholder="请输入用户名" />
      </view>

      <view class="form-group">
        <view class="form-label">昵称</view>
        <input type="text" v-model="formData.nickname" class="form-input" placeholder="请输入昵称" />
      </view>

      <view class="form-group">
        <view class="form-label">手机号码</view>
        <input type="number" v-model="formData.phone" class="form-input" placeholder="请输入手机号码" />
      </view>

      <view class="form-group">
        <view class="form-label">邮箱</view>
        <input type="text" v-model="formData.email" class="form-input" placeholder="请输入邮箱地址" />
      </view>
    </view>

    <!-- Save Button -->
    <view class="button-container">
      <button class="save-button" @click="saveProfile">保存修改</button>
    </view>
  </view>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { getUserInfo, updateUserProfile } from '@/api/user';
import baseURL from '@/config/index';
import { useUserStore } from '@/store/user';

// 初始化用户store
const userStore = useUserStore();

// 状态栏高度
const statusBarHeight = ref(20);

// User info state
const userInfo = ref({});

// Form data
const formData = reactive({
  username: '',
  nickname: '',
  phone: '',
  email: ''
});

// Form validation
const formErrors = reactive({
  username: '',
  nickname: '',
  phone: '',
  email: ''
});

// Initialize data
onMounted(async () => {
  // 获取状态栏高度
  try {
    const systemInfo = uni.getSystemInfoSync();

    // #ifdef MP-WEIXIN
    statusBarHeight.value = systemInfo.statusBarHeight || 20;
    // #endif

    // #ifdef APP-PLUS
    statusBarHeight.value = systemInfo.statusBarHeight || 20;
    console.log('APP状态栏高度:', statusBarHeight.value);
    // #endif
  } catch (error) {
    console.error('获取系统信息失败', error);
  }

  // 获取用户信息
  getUserDetailInfo()
});

// 获取用户信息
const getUserDetailInfo = async () => {
  const res = await getUserInfo();
  userInfo.value = res.user;
  formData.username = res.user.username || '';
  formData.nickname = res.user.nickName || '';
  formData.phone = res.user.phone || '';
  formData.email = res.user.email || '';
}

// Navigate back to profile page
const navigateBack = () => {
  try {
    // 获取当前页面栈
    const pages = getCurrentPages();

    // 如果当前页面栈只有1个页面，那么无法返回上一页，需要重定向到个人中心
    if (pages.length <= 1) {
      uni.switchTab({
        url: '/pages/profile/index'
      });
    } else {
      // 有上一页，正常返回
      uni.navigateBack();
    }
  } catch (error) {
    console.error('导航错误', error);
    // 出错时也重定向到个人中心
    uni.switchTab({
      url: '/pages/profile/index'
    });
  }
};

// Change avatar
const changeAvatar = () => {
  uni.chooseImage({
    count: 1,
    sizeType: ['compressed'],
    sourceType: ['album', 'camera'],
    success: (res) => {
      uni.showLoading({ title: '上传中...' });
      const tempFilePath = res.tempFilePaths[0];
      uni.uploadFile({
        url: baseURL.serverUrl + '/api/user/update/avatar',
        header: {
          'Authorization': `Bearer ${userStore.token}`, // 从缓存获取token
        },
        filePath: tempFilePath,
        name: 'avatar',
        success: (uploadRes) => {
          getUserDetailInfo()
          uni.showToast({ title: '上传成功' });
        }
      });
    }
  });
};

// Validate form
const validateForm = () => {
  let isValid = true;

  // Reset errors
  Object.keys(formErrors).forEach(key => {
    formErrors[key] = '';
  });

  // Username validation
  if (!formData.username.trim()) {
    formErrors.username = '用户名不能为空';
    isValid = false;
  }

  // Email validation
  if (formData.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
    formErrors.email = '请输入有效的邮箱地址';
    isValid = false;
  }

  // Phone validation
  if (formData.phone && ! /^1[3-9]\d{9}$/.test(formData.phone)) {
    formErrors.phone = '请输入有效的手机号码';
    isValid = false;
  }

  return isValid;
};

// Save profile
const saveProfile = async () => {
  if (!validateForm()) {
    // Show first error
    const firstError = Object.values(formErrors).find(error => error);
    if (firstError) {
      uni.showToast({
        title: firstError,
        icon: 'none'
      });
    }
    return;
  }

  uni.showLoading({ title: '保存中...' });

  // 模拟API调用
  await updateUserProfile(formData)

  uni.hideLoading();
  uni.showToast({
    title: '个人资料更新成功',
    icon: 'success'
  });

  // Navigate back after save
  setTimeout(() => {
    navigateBack();
  }, 1500);
};
</script>

<style scoped>
.profile-settings {
  min-height: 100vh;
  background-color: #f8f9fa;
  padding-bottom: 30px;
  display: flex;
  flex-direction: column;
}

/* 状态栏占位 */
.status-bar {
  width: 100%;
  background-color: white;
}

.header {
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
  background-color: white;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  position: relative;
  top: 0;
  z-index: 100;
  box-sizing: border-box;
  width: 100%;
}

.back-btn {
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  -webkit-tap-highlight-color: transparent;
}

.back-icon {
  width: 20px;
  height: 20px;
}

.header-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  flex: 1;
  text-align: center;
}

.placeholder {
  width: 30px;
}

.avatar-section {
  padding: 20px 0;
  background-color: white;
  margin-bottom: 12px;
  display: flex;
  justify-content: center;
}

.avatar-container {
  position: relative;
  width: 80px;
  height: 80px;
  border-radius: 50%;
  overflow: hidden;
  background-color: #0c873d;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: center;
  align-items: center;
}


.avatar-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-edit-button {
  position: absolute;
  right: 0;
  bottom: 0;
  width: 24px;
  height: 24px;
  background-color: #0c873d;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.edit-icon {
  color: white;
  font-size: 14px;
  line-height: 1;
}

.form-container {
  padding: 16px;
  background-color: white;
  border-radius: 8px;
  margin: 0 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.form-group {
  margin-bottom: 16px;
}

.form-label {
  font-size: 14px;
  color: #666;
  margin-bottom: 6px;
  font-weight: 500;
}

.form-input {
  width: 100%;
  height: 44px;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  padding: 0 12px;
  font-size: 15px;
  background-color: #f9f9f9;
  transition: border-color 0.3s, box-shadow 0.3s;
}

.form-input:focus {
  border-color: #0c873d;
  background-color: #fff;
  box-shadow: 0 0 0 2px rgba(12, 135, 61, 0.1);
}

.button-container {
  padding: 20px 16px;
}

.save-button {
  width: 100%;
  height: 44px;
  background-color: #0c873d;
  color: white;
  border-radius: 22px;
  font-size: 16px;
  font-weight: 500;
  border: none;
  box-shadow: 0 2px 8px rgba(12, 135, 61, 0.25);
  transition: background-color 0.3s, transform 0.2s;
}

.save-button:active {
  background-color: #097033;
  transform: scale(0.98);
}

/* Responsive adjustments for larger screens */
@media screen and (min-width: 768px) {
  .form-container {
    max-width: 500px;
    margin: 0 auto;
    padding: 24px;
  }

  .button-container {
    max-width: 500px;
    margin: 0 auto;
  }
}
</style>

<template>
  <view class="tab-pane">
    <view class="info-card">
      <view class="card-body">
        <view v-if="exceptionInfo.length === 0" class="empty-tip">暂无异常信息</view>
        <view v-else class="exception-list">
          <view v-for="(item, index) in exceptionInfo" :key="index" class="exception-item">
            <view class="exception-header">
              <text class="exception-title">{{ item.exceptionNo || '未知异常' }}</text>
            </view>
            <view class="exception-body">
              <view class="exception-info">
                <text class="exception-label">异常类型</text>
                <text class="exception-value">{{ item.exceptionType || '-' }}</text>
              </view>
              <view class="exception-info">
                <text class="exception-label">异常描述</text>
                <text class="exception-value">{{ item.description || '-' }}</text>
              </view>
              <view class="exception-info">
                <text class="exception-label">发生时间</text>
                <text class="exception-value">{{ item.occurTime || '-' }}</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
// 定义props接收父组件传递的数据
const props = defineProps({
  exceptionInfo: {
    type: Array,
    default: () => []
  }
});
</script>

<style scoped>
/* 卡片样式 */
.info-card {
  background-color: #ffffff;
  border-radius: 10px;
  margin-bottom: 10px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.card-body {
  padding: 15px;
}

/* 空数据提示 */
.empty-tip {
  text-align: center;
  padding: 30px 0;
  color: #9ca3af;
  font-size: 14px;
}

/* 异常信息样式 */
.exception-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.exception-item {
  background-color: #f8fafc;
  border-radius: 8px;
  padding: 12px;
  border: 1px solid #e2e8f0;
}

.exception-header {
  margin-bottom: 8px;
  padding-bottom: 6px;
  border-bottom: 1px solid #e2e8f0;
}

.exception-title {
  font-size: 14px;
  font-weight: 600;
  color: #0f172a;
}

.exception-body {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
}

.exception-info {
  display: flex;
  flex-direction: column;
}

.exception-label {
  font-size: 11px;
  color: #64748b;
  margin-bottom: 2px;
}

.exception-value {
  font-size: 13px;
  color: #334155;
}

.tab-pane {
  height: 100%;
}
</style> 
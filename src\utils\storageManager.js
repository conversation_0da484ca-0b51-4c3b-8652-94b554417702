/**
 * 本地存储管理工具
 * 用于管理和清理本地存储数据，避免数据累积过多
 */

// 存储键名常量
export const STORAGE_KEYS = {
  ORDER_DETAIL_PREFIX: 'orderDetail_',
  PRINT_DATA: 'printData',
  EXECUTE_DELIVER_STATE: 'executeDeliverState',
  LAST_ORDER_NO: 'lastOrderNo',
  LAST_ITEM_ID: 'lastItemId',
  CACHED_PRINTER: 'cachedPrinter',
  USER_INFO: 'userInfo',
  TOKEN_EXPIRE_TIME: 'tokenExpireTime'
};

// 配置项
const CONFIG = {
  // orderDetail 最大保留数量
  MAX_ORDER_DETAILS: 10,
  // 数据过期时间（毫秒）- 7天
  DATA_EXPIRE_TIME: 7 * 24 * 60 * 60 * 1000,
  // 清理检查间隔（毫秒）- 每次应用启动时检查
  CLEANUP_INTERVAL: 24 * 60 * 60 * 1000
};

/**
 * 获取所有 orderDetail 相关的存储键
 * @returns {Array} orderDetail 键名数组
 */
export const getOrderDetailKeys = () => {
  try {
    const info = uni.getStorageInfoSync();
    return info.keys.filter(key => key.startsWith(STORAGE_KEYS.ORDER_DETAIL_PREFIX));
  } catch (e) {
    console.error('获取存储键列表失败:', e);
    return [];
  }
};

/**
 * 清理过期的 orderDetail 数据
 * 保留最近的 MAX_ORDER_DETAILS 个数据，删除其余的
 */
export const cleanupOrderDetails = () => {
  try {
    const orderDetailKeys = getOrderDetailKeys();
    
    if (orderDetailKeys.length <= CONFIG.MAX_ORDER_DETAILS) {
      console.log(`orderDetail 数据数量(${orderDetailKeys.length})未超过限制(${CONFIG.MAX_ORDER_DETAILS})，无需清理`);
      return;
    }
    
    // 获取每个 orderDetail 的时间戳信息
    const orderDetailsWithTime = [];
    
    orderDetailKeys.forEach(key => {
      try {
        const data = uni.getStorageSync(key);
        if (data) {
          // 尝试解析数据获取时间戳
          let timestamp = Date.now(); // 默认使用当前时间
          
          try {
            const parsedData = typeof data === 'string' ? JSON.parse(data) : data;
            // 如果数据中有时间戳字段，使用它
            if (parsedData.timestamp) {
              timestamp = parsedData.timestamp;
            } else if (parsedData.CreateTime) {
              timestamp = new Date(parsedData.CreateTime).getTime();
            } else if (parsedData.UpdateTime) {
              timestamp = new Date(parsedData.UpdateTime).getTime();
            }
          } catch (parseError) {
            // 解析失败，使用默认时间戳
          }
          
          orderDetailsWithTime.push({
            key,
            timestamp,
            data
          });
        }
      } catch (e) {
        console.error(`获取存储数据失败 ${key}:`, e);
        // 如果数据损坏，标记为需要删除
        orderDetailsWithTime.push({
          key,
          timestamp: 0, // 最旧的时间戳，会被优先删除
          data: null
        });
      }
    });
    
    // 按时间戳降序排序（最新的在前）
    orderDetailsWithTime.sort((a, b) => b.timestamp - a.timestamp);
    
    // 保留最新的 MAX_ORDER_DETAILS 个，删除其余的
    const toDelete = orderDetailsWithTime.slice(CONFIG.MAX_ORDER_DETAILS);
    
    let deletedCount = 0;
    toDelete.forEach(item => {
      try {
        uni.removeStorageSync(item.key);
        deletedCount++;
        console.log(`已删除过期的 orderDetail: ${item.key}`);
      } catch (e) {
        console.error(`删除存储数据失败 ${item.key}:`, e);
      }
    });
    
    console.log(`orderDetail 清理完成，删除了 ${deletedCount} 个过期数据，保留了 ${CONFIG.MAX_ORDER_DETAILS} 个最新数据`);
    
  } catch (e) {
    console.error('清理 orderDetail 数据失败:', e);
  }
};

/**
 * 清理临时数据
 * 清理一些临时使用的存储数据
 */
export const cleanupTemporaryData = () => {
  try {
    // 清理可能遗留的临时数据
    const tempKeys = [
      STORAGE_KEYS.PRINT_DATA,
      STORAGE_KEYS.EXECUTE_DELIVER_STATE
    ];
    
    tempKeys.forEach(key => {
      try {
        const data = uni.getStorageSync(key);
        if (data) {
          // 检查数据是否过期（超过1天）
          let shouldDelete = false;
          
          try {
            const parsedData = typeof data === 'string' ? JSON.parse(data) : data;
            if (parsedData.timestamp) {
              const age = Date.now() - parsedData.timestamp;
              if (age > 24 * 60 * 60 * 1000) { // 超过1天
                shouldDelete = true;
              }
            } else {
              // 没有时间戳的数据，认为是旧数据，删除
              shouldDelete = true;
            }
          } catch (parseError) {
            // 解析失败，删除
            shouldDelete = true;
          }
          
          if (shouldDelete) {
            uni.removeStorageSync(key);
            console.log(`已清理临时数据: ${key}`);
          }
        }
      } catch (e) {
        console.error(`清理临时数据失败 ${key}:`, e);
      }
    });
    
  } catch (e) {
    console.error('清理临时数据失败:', e);
  }
};

/**
 * 保存 orderDetail 数据（带时间戳）
 * @param {string} orderNo 订单号
 * @param {Object} data 订单数据
 */
export const saveOrderDetail = (orderNo, data) => {
  try {
    const key = `${STORAGE_KEYS.ORDER_DETAIL_PREFIX}${orderNo}`;
    const dataWithTimestamp = {
      ...data,
      timestamp: Date.now() // 添加时间戳
    };
    
    uni.setStorageSync(key, JSON.stringify(dataWithTimestamp));
    console.log(`已保存 orderDetail: ${key}`);
    
    // 保存后检查是否需要清理
    setTimeout(() => {
      cleanupOrderDetails();
    }, 100);
    
  } catch (e) {
    console.error(`保存 orderDetail 失败 ${orderNo}:`, e);
  }
};

/**
 * 获取 orderDetail 数据
 * @param {string} orderNo 订单号
 * @returns {Object|null} 订单数据
 */
export const getOrderDetail = (orderNo) => {
  try {
    const key = `${STORAGE_KEYS.ORDER_DETAIL_PREFIX}${orderNo}`;
    const data = uni.getStorageSync(key);
    
    if (data) {
      const parsedData = typeof data === 'string' ? JSON.parse(data) : data;
      // 移除时间戳字段，返回原始数据
      const { timestamp, ...originalData } = parsedData;
      return originalData;
    }
    
    return null;
  } catch (e) {
    console.error(`获取 orderDetail 失败 ${orderNo}:`, e);
    return null;
  }
};

/**
 * 执行完整的存储清理
 * 应该在应用启动时调用
 */
export const performFullCleanup = () => {
  console.log('开始执行存储清理...');
  
  try {
    // 清理 orderDetail 数据
    cleanupOrderDetails();
    
    // 清理临时数据
    cleanupTemporaryData();
    
    // 记录清理时间
    uni.setStorageSync('lastCleanupTime', Date.now().toString());
    
    console.log('存储清理完成');
    
  } catch (e) {
    console.error('存储清理失败:', e);
  }
};

/**
 * 检查是否需要执行清理
 * @returns {boolean} 是否需要清理
 */
export const shouldPerformCleanup = () => {
  try {
    const lastCleanupTime = uni.getStorageSync('lastCleanupTime');
    if (!lastCleanupTime) {
      return true; // 从未清理过
    }
    
    const timeSinceLastCleanup = Date.now() - parseInt(lastCleanupTime);
    return timeSinceLastCleanup > CONFIG.CLEANUP_INTERVAL;
    
  } catch (e) {
    console.error('检查清理时间失败:', e);
    return true; // 出错时执行清理
  }
};

/**
 * 获取存储使用情况统计
 * @returns {Object} 存储统计信息
 */
export const getStorageStats = () => {
  try {
    const info = uni.getStorageInfoSync();
    const orderDetailKeys = getOrderDetailKeys();
    
    return {
      totalKeys: info.keys.length,
      totalSize: info.currentSize,
      limitSize: info.limitSize,
      orderDetailCount: orderDetailKeys.length,
      orderDetailKeys: orderDetailKeys
    };
    
  } catch (e) {
    console.error('获取存储统计失败:', e);
    return {
      totalKeys: 0,
      totalSize: 0,
      limitSize: 0,
      orderDetailCount: 0,
      orderDetailKeys: []
    };
  }
};

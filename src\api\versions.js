import http from '@/utils/request'

/**
 * 查询版本信息
 * @param {Object} params - 查询参数 {VersionsType}
 * @returns {Promise} - 返回版本信息
 */
export const queryVersions = (params) => {
  return http.post('/Service/BaseModuleAjax.ashx', {
    OP: 'GetVersionInfoList',
    Data: JSON.stringify(params),
    limit: 10,
    page: 1
  }, {
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    transformRequest: [(data) => {
      let ret = ''
      for (let it in data) {
        ret += encodeURIComponent(it) + '=' + encodeURIComponent(data[it]) + '&'
      }
      return ret.substring(0, ret.length - 1)
    }]
  })
} 
<template>
  <view class="container">
    <!-- 使用uni-nav-bar组件 -->
    <view class="nav-bar-container">
      <uni-nav-bar
        :fixed="true"
        :status-bar="true"
        title="执行发货"
        left-icon="left"
        @clickLeft="goBack"
        :border="false"
        :right-icon="null"
        @clickRight="showOperationLog"
      >
        <template #right>
          <view class="nav-right-icon" @click="showOperationLog">
            <image class="add-icon" src="/static/icons/add.png" mode="aspectFit"></image>
          </view>
        </template>
      </uni-nav-bar>
    </view>
    
    <!-- 使用操作记录组件 -->
    <operation-log
      v-model:showLogModal="showLogModal"
      :operationLogs="operationLogs"
      @close="closeOperationLog"
    />
    
    <!-- 主体内容区 -->
    <view class="main-content">
      <!-- 扫描输入框区域 - 替换原来的订单状态显示 -->
      <view class="scan-area">
        <view class="scan-input-wrapper">
          <view class="scan-btn" @click="openScanCode">
            <image class="scan-icon" src="/static/icons/scan.png" mode="aspectFit"></image>
          </view>
          <uni-easyinput
            class="scan-input"
            v-model="scanCode"
            placeholder="扫描或输入条码"
            @confirm="handleScan"
            :focus="scanInputFocused"
            @focus="scanInputFocused = true"
            @blur="scanInputFocused = false"
            border-color="#14772f"
            primary-color="#14772f"
            :styles="{
              borderWidth: '1px',
              borderColor: '#14772f',
              borderRadius: '8px'
            }"
            clearable
          />
        </view>
      </view>
      
      <!-- Tab栏 -->
      <view class="tabs">
        <view 
          v-for="(tab, index) in tabs" 
          :key="index" 
          class="tab-item" 
          :class="{ active: currentTab === index }"
          @click="switchTab(index)"
        >
          <text>{{ tab.name }}</text>
        </view>
      </view>
      
      <!-- Tab内容区 -->
      <scroll-view scroll-y class="tab-content">
        <!-- 基本信息Tab -->
        <view v-if="currentTab === 0" class="tab-pane">
          <!-- 发货信息表单卡片 -->
          <view class="info-card">
            <view class="card-body">
              <view class="form-list">
                <view class="form-item">
                  <text class="form-label">发货方式</text>
                  <picker class="form-picker" :range="shipMethods" @change="onShipMethodChange">
                    <view class="picker-value">{{ selectedShipMethod || '请选择' }}</view>
                  </picker>
                </view>
                
                <view class="form-item">
                  <text class="form-label">运单号</text>
                  <view class="tracking-input-wrapper">
                    <uni-easyinput
                      class="form-input tracking-input"
                      v-model="trackingNo"
                      placeholder="请输入运单号"
                      :focus="trackingNoFocused"
                      @focus="trackingNoFocused = true"
                      @blur="trackingNoFocused = false"
                      border-color="#14772f"
                      primary-color="#14772f"
                      :styles="{
                        borderWidth: '0 0 1px 0',
                        borderColor: '#14772f',
                        borderRadius: '0'
                      }"
                      :clearable="false"
                    />
                    <view class="tracking-scan-btn" @click="openTrackingNoScan">
                      <image class="scan-icon" src="/static/icons/scan.png" mode="aspectFit"></image>
                    </view>
                  </view>
                </view>
                
                <view class="form-item">
                  <text class="form-label">储运条件</text>
                  <uni-easyinput
                    class="form-input"
                    v-model="storageConditions"
                    placeholder="请输入储运条件"
                    :focus="storageConditionsFocused"
                    @focus="storageConditionsFocused = true"
                    @blur="storageConditionsFocused = false"
                    border-color="#14772f"
                    primary-color="#14772f"
                    :styles="{
                      borderWidth: '0 0 1px 0',
                      borderColor: '#14772f',
                      borderRadius: '0'
                    }"
                    clearable
                  />
                </view>

                <view class="form-item">
                  <text class="form-label">单箱容量（个）</text>
                  <uni-easyinput
                    class="form-input"
                    v-model="singleBoxQuantity"
                    placeholder="请输入单箱容量"
                    :focus="singleBoxQuantityFocused"
                    @focus="singleBoxQuantityFocused = true"
                    @blur="singleBoxQuantityFocused = false"
                    type="number"
                    border-color="#14772f"
                    primary-color="#14772f"
                    :styles="{
                      borderWidth: '0 0 1px 0',
                      borderColor: '#14772f',
                      borderRadius: '0'
                    }"
                    clearable
                  />
                </view>
              </view>
            </view>
          </view>
          
          <!-- 基本信息卡片 -->
          <view class="info-card">
            <view class="card-body">
              <view class="info-list">
                <view class="info-item">
                  <text class="info-label">发货单号</text>
                  <text class="info-value">{{ orderDetail.DNo || '-' }}</text>
                </view>
                <view class="info-item">
                  <text class="info-label">单据类型</text>
                  <text class="info-value" :class="orderDetail.BillType === 'FH' ? 'bill-type-fh' : 'bill-type-th'">{{ orderDetail.BillType === 'FH' ? '发货单' : '退货单' }}</text>
                </view>
                <view class="info-item">
                  <text class="info-label">合同编号</text>
                  <text class="info-value">{{ contractNo || '-' }}</text>
                </view>
                <view class="info-item">
                  <text class="info-label">销售单号</text>
                  <text class="info-value">{{ orderDetail.SaleNo || '-' }}</text>
                </view>
                <view class="info-item">
                  <text class="info-label">收货单位</text>
                  <text class="info-value">{{ orderDetail.CustName || '-' }}</text>
                </view>
                <view class="info-item">
                  <text class="info-label">收货地址</text>
                  <text class="info-value">{{ orderDetail.Addr || '-' }}</text>
                </view>
                <view class="info-item">
                  <text class="info-label">收件人</text>
                  <text class="info-value">{{ orderDetail.Consignee || '-' }}</text>
                </view>
                <view class="info-item">
                  <text class="info-label">联系电话</text>
                  <text class="info-value">{{ orderDetail.Phone || '-' }}</text>
                </view>
                <view class="info-item">
                  <text class="info-label">生产许可证号</text>
                  <text class="info-value">{{ orderDetail.PrdXKZ || '-' }}</text>
                </view>
                <view class="info-item">
                  <text class="info-label">发货人</text>
                  <text class="info-value">{{ shipper }}</text>
                </view>
                <view class="info-item">
                  <text class="info-label">发货日期</text>
                  <text class="info-value">{{ shipDate }}</text>
                </view>
              </view>
            </view>
          </view>
        </view>
        
        <!-- 序列号详情Tab -->
        <view v-if="currentTab === 1" class="tab-pane serial-tab-content">
          <view class="serial-header">
            <text class="card-title">序列号列表</text>
            <text class="item-count" v-if="serialList.length > 0">共 {{ serialList.length }} 条</text>
          </view>
          <view v-if="serialLoading" class="loading-box">
            <text>加载中...</text>
          </view>
          <view v-else-if="serialList.length > 0" class="serial-list">
            <view v-for="(item, index) in serialList" :key="index" class="serial-item">
              <view class="serial-delete-btn" @click="deleteSerialItem(item, index)">×</view>
              <view class="serial-item-header">
                <text class="serial-title">{{ item.MaterName || '未知产品' }}</text>
                <text class="serial-code">{{ item.MaterNo || '-' }}</text>
              </view>
              <view class="serial-body">
                <view class="serial-info">
                  <text class="serial-label">序列号</text>
                  <text class="serial-value">{{ item.BatchSN || '-' }}</text>
                </view>
                <view class="serial-info">
                  <text class="serial-label">数量</text>
                  <text class="serial-value">{{ item.Qty || '0' }}</text>
                </view>
                <view class="serial-info">
                  <text class="serial-label">箱号</text>
                  <text class="serial-value">{{ item.BoxNo || '-' }}</text>
                </view>
                <view class="serial-info">
                  <text class="serial-label">生产日期</text>
                  <text class="serial-value">{{ item.PrdDate || '-' }}</text>
                </view>
                <view class="serial-info">
                  <text class="serial-label">生产批号</text>
                  <text class="serial-value">{{ item.PrdBatch || '-' }}</text>
                </view>
                <view class="serial-info">
                  <text class="serial-label">使用期限</text>
                  <text class="serial-value">{{ item.EfftDate || '-' }}</text>
                </view>
              </view>
            </view>
          </view>
          <view v-else class="empty-list">暂无序列号数据</view>
        </view>
        
        <!-- 发货信息Tab -->
        <view v-if="currentTab === 2" class="tab-pane">
          <view class="card-header">
            <text class="card-title">发货列表</text>
            <text class="item-count" v-if="shippingInfoList.length > 0">共 {{ shippingInfoList.length }} 条</text>
          </view>
          
          <view v-if="shippingInfoLoading" class="loading-box">
            <text>加载中...</text>
          </view>
          
          <view v-else-if="shippingInfoList.length === 0" class="empty-list">
            <text>暂无发货信息数据</text>
          </view>
          
          <view v-else class="serial-list">
            <view v-for="(item, index) in shippingInfoList" :key="index" class="serial-item">
              <view class="serial-item-header">
                <text class="serial-title">{{ item.MaterName || '未知产品' }}</text>
                <text class="serial-code">{{ item.MaterNo || '-' }}</text>
              </view>
              <view class="serial-body">
                <view class="serial-info">
                  <text class="serial-label">部件名称</text>
                  <text class="serial-value">{{ item.PartMame || '-' }}</text>
                </view>
                <view class="serial-info">
                  <text class="serial-label">规格型号</text>
                  <text class="serial-value">{{ item.Spec || '-' }}</text>
                </view>
                <view class="serial-info">
                  <text class="serial-label">需发数量</text>
                  <text class="serial-value">{{ item.ReqQty || '-' }}{{ item.Unit ? '（' + item.Unit + '）' : '' }}</text>
                </view>
                <view class="serial-info">
                  <text class="serial-label">实发数量</text>
                  <text class="serial-value">{{ item.RealQty || '0' }}</text>
                </view>
                <view class="serial-info">
                  <text class="serial-label">状态</text>
                  <text class="serial-value">{{ item.Status || '-' }}</text>
                </view>
              </view>
            </view>
          </view>
        </view>
      </scroll-view>
    </view>
    
    <!-- 底部操作栏 -->
    <view class="footer">
      <view class="footer-button print" @click="printDelivery">打印</view>
      <view class="footer-button submit" @click="submitDelivery">确认发货</view>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted, computed, onBeforeUnmount } from 'vue';
import { getOrderSerialDetail, getOrderInfo, deliverScanNoSerialNo, submitPRDDeliver, deleteSerialNo, getDeliverPrintInfo } from '@/api/deliver';
import { useUserStore } from '@/store/user';
import audioHelper from '@/utils/audioHelper';
import scanCodeUtil from '@/utils/scanCode';
// 导入uni组件
import uniNavBar from '@dcloudio/uni-ui/lib/uni-nav-bar/uni-nav-bar.vue'
import uniEasyinput from '@dcloudio/uni-ui/lib/uni-easyinput/uni-easyinput.vue'
// 导入操作记录组件
import OperationLog from '@/components/OperationLog.vue'

// 添加获取当前日期的函数
const getCurrentDate = () => {
  const now = new Date();
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, '0');
  const day = String(now.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
};

// Tab栏配置
const tabs = [
  { name: '基本信息' },
  { name: '序列号详情' },
  { name: '发货信息' }
];
const currentTab = ref(0);

// 切换Tab
const switchTab = (index) => {
  currentTab.value = index;
  
  // 如果切换到序列号详情tab
  if (index === 1) {
    // 每次切换到序列号详情tab时，都重新获取序列号数据
    serialLoading.value = true;
    serialList.value = [];
    
    try {
      const params = {
        CFlag: '240',
        No: orderNo.value,
        Item: itemId.value || ""
      };
      
      getOrderSerialDetail(orderNo.value, itemId.value || "", params).then(result => {
        if (result && result.data) {
          serialList.value = Array.isArray(result.data) ? result.data : [result.data];
        } else if (result && Array.isArray(result)) {
          serialList.value = result;
        } else {
          serialList.value = [];
        }
        serialLoading.value = false;
      }).catch(error => {
        console.error('获取序列号失败:', error);
        uni.showToast({
          title: '获取序列号失败',
          icon: 'none',
          mask: false
        });
        serialLoading.value = false;
      });
    } catch (error) {
      console.error('获取序列号失败:', error);
      serialLoading.value = false;
    }
  }
  
  // 如果切换到发货信息tab，每次都重新加载数据
  if (index === 2) {
    fetchShippingInfo();
  }
};

// 获取状态标签样式
const getStatusClass = (status) => {
  switch (status) {
    case '待扫描':
    case '待发货':
      return 'status-warning'; // 黄色警告色
    case '已装箱':
    case '已发货':
      return 'status-success'; // 绿色成功色
    case '发货中':
      return 'status-info'; // 灰色信息色
    default:
      return '';
  }
};

// 订单详情数据
const orderDetail = ref({});
const orderNo = ref('');
const itemId = ref('');

// 物料列表
const materialList = ref([]);
const materialLoading = ref(false);

// 序列号列表
const serialList = ref([]);
const serialLoading = ref(false);
const showSerialList = ref(false);
const currentMaterial = ref({});

// 表单数据
const scanCode = ref('');
const selectedShipMethod = ref('快递');
const shipMethods = ['快递', '物流'];
const trackingNo = ref('');
const contractNo = ref('');
const storageConditions = ref('常温');
const singleBoxQuantity = ref('60');

// 发货信息数据
const shippingInfo = ref({});
const shippingInfoLoading = ref(false);
const shippingInfoLoaded = ref(false);
const shippingInfoList = ref([]);

// 获取用户store
const userStore = useUserStore();

// 发货人和发货日期
const shipper = computed(() => userStore.getUserInfo?.userId || '');
const shipDate = ref(getCurrentDate());

// 添加输入框聚焦状态变量
const scanInputFocused = ref(false);
const trackingNoFocused = ref(false);
const storageConditionsFocused = ref(false);
const singleBoxQuantityFocused = ref(false);

// 返回上一页
const goBack = () => {
  // 使用navigateBack返回上一页
  uni.navigateBack({
    delta: 1,
    fail: () => {
      // 如果返回失败，则重定向到列表页
      uni.redirectTo({
        url: '/pages/deliver/deliverIndex'
      });
    }
  });
};

  // 处理扫描结果
const handleScan = () => {
  if (!scanCode.value) return;
  
  // 进行输入验证
  // 1. 检查必填项
  if (!selectedShipMethod.value) {
    showMessage("请选择发货方式！");
    scanCode.value = "";
    focusOnScanInput(); // 重新聚焦输入框
    return;
  }
  
  if (!trackingNo.value) {
    showMessage("请输入运单号！");
    scanCode.value = "";
    focusOnScanInput(); // 重新聚焦输入框
    return;
  }
  
  if (!storageConditions.value) {
    showMessage("请输入储运条件！");
    scanCode.value = "";
    focusOnScanInput(); // 重新聚焦输入框
    return;
  }
  
  if (!singleBoxQuantity.value) {
    showMessage("请输入单箱容量！");
    scanCode.value = "";
    focusOnScanInput(); // 重新聚焦输入框
    return;
  }
  
  if (!/^\d*$/.test(singleBoxQuantity.value) || singleBoxQuantity.value === "0") {
    showMessage("单箱容量只能输入数字，且大于0！");
    scanCode.value = "";
    focusOnScanInput(); // 重新聚焦输入框
    return;
  }
  
  // 2. 检查扫码内容格式
  if (/[\u4e00-\u9fa5]/.test(scanCode.value)) {
    showMessage("不能输入中文！");
    scanCode.value = "";
    focusOnScanInput(); // 重新聚焦输入框
    return;
  }
  
  // 保存原始扫码值，用于后续处理
  let scanInfo = scanCode.value;
  let msg = "";
  
  // 3. 检查批号格式 - 分号分隔的格式
  if (scanInfo.indexOf(";") !== -1) {
    if (!/^[a-zA-Z0-9-]+;[a-zA-Z0-9]+;\d+$/.test(scanInfo)) {
      showMessage("输入的批号格式错误，正确格式：产品编码;批号;数量！");
      scanCode.value = "";
      focusOnScanInput(); // 重新聚焦输入框
      return;
    }
    
    const str = scanInfo.split(";");
    
    if (parseInt(str[2]) === 0) {
      showMessage("输入的批号数量必须大于0！");
      scanCode.value = "";
      focusOnScanInput(); // 重新聚焦输入框
      return;
    }
    
    if (parseInt(str[2]) > parseInt(singleBoxQuantity.value)) {
      showMessage("输入的批号数量必须小于或等于单箱数量！");
      scanCode.value = "";
      focusOnScanInput(); // 重新聚焦输入框
      return;
    }
  }
  
  // 4. 检查特殊GS1 UDI格式
  if (scanInfo.indexOf("[CR]") !== -1) {
    const uidInfo = scanCodeUtil.parseSpecialGS1UDI(scanInfo);
    scanInfo = uidInfo.SerialNo || "";
    
    if (scanInfo === "") {
      showMessage("解析失败，请检查输入格式！");
      scanCode.value = "";
      focusOnScanInput(); // 重新聚焦输入框
      return;
    }
    
    // 更新扫码输入框的值为解析后的序列号
    scanCode.value = scanInfo;
  }
  
  // 通过验证，调用扫码接口
  callScanNoSerialNoApi();
};

// 修改调用无序列号扫码接口的方法
const callScanNoSerialNoApi = async () => {
  try {
    
    // 构建请求参数
    const params = {
      No: scanCode.value,                  // 扫描内容
      Item: selectedShipMethod.value,      // 发货方式（原来是空字符串）
      Name: storageConditions.value,       // 储运条件（原来是空字符串）
      A: trackingNo.value,                 // 运单号
      B: itemId.value || "",               // DLID
      C: orderDetail.value.DNo || orderNo.value, // 发货单号
      D: singleBoxQuantity.value,          // 单箱容量
      F: orderDetail.value.BillType || "", // 单据类型
      H: shipper.value,                    // 发货人
      I: shipDate.value,                   // 发货日期
      Flag: "1"                            // 标识
    };
    
    // 调用API
    const result = await deliverScanNoSerialNo(params);
    
    // 处理响应
    if (result && result.Msg === 'Success') {
      // 成功处理
      showMessage('扫码成功', 'success', 2000);
      scanCode.value = ''; // 清空扫码框
      
      // 如果当前不在序列号详情Tab，则切换到序列号详情Tab
      if (currentTab.value !== 1) {
        currentTab.value = 1;
      }
      
      // 检查是否可以打印
      if (result.Data && result.Data.DType === "FH") {
        // 不再保存箱号到本地存储，而是直接获取序列号数据
        
        // 调用打印信息接口检查是否可以打印
        try {
          const deliverNo = orderDetail.value.DNo || orderNo.value;
          // 从结果中获取箱号，但不保存到本地存储
          const boxNo = result.Data.BoxNo || '';
          const printResult = await getDeliverPrintInfo(deliverNo, singleBoxQuantity.value, boxNo);
          
          // 如果允许打印，提示用户
          if (printResult && printResult.IsPrint === 'Y') {
            uni.showModal({
              title: '打印提示',
              content: '扫码成功，是否立即打印发货单？',
              success: (res) => {
                if (res.confirm) {
                  // 用户确认，执行打印
                  printDelivery();
                } else {
                  // 用户取消，聚焦回输入框
                  focusOnScanInput();
                }
              }
            });
          } else {
            // 不需要打印，聚焦回输入框
            focusOnScanInput();
          }
        } catch (printError) {
          console.error('检查打印状态失败:', printError);
          focusOnScanInput(); // 发生错误时也聚焦回输入框
        }
      } else {
        // 不需要打印，聚焦回输入框
        focusOnScanInput();
      }
      
      // 直接获取序列号数据，不再调用fetchMaterialList
      serialLoading.value = true;
      serialList.value = [];
      
      try {
        // 构建序列号查询参数，使用正确的参数
        const serialParams = {
          CFlag: '240',
          No: orderNo.value,
          // 重要：这里使用发货单号和DLID，而不是物料编码
          Item: itemId.value || ""
        };
        
        // 直接调用获取序列号的API
        const serialResult = await getOrderSerialDetail(orderNo.value, itemId.value || "", serialParams);
        
        if (serialResult && serialResult.data) {
          serialList.value = Array.isArray(serialResult.data) ? serialResult.data : [serialResult.data];
        } else if (serialResult && Array.isArray(serialResult)) {
          serialList.value = serialResult;
        } else {
          serialList.value = [];
        }
      } catch (error) {
        console.error('获取序列号失败:', error);
        showMessage('获取序列号失败');
      } finally {
        serialLoading.value = false;
      }
    } else {
      // 失败处理
      showMessage(result?.Message || '操作失败');
      scanCode.value = ''; // 清空扫码框
      focusOnScanInput(); // 失败后聚焦回输入框
    }
  } catch (error) {
    console.error('扫码处理失败:', error);
    showErrorToast('扫码处理失败，请重试');
    focusOnScanInput(); // 异常后聚焦回输入框
  } finally {
    // uni.hideLoading();
  }
};

// 显示错误提示
const showErrorToast = (message) => {
  uni.hideLoading();
  
  // 记录失败操作
  recordFailedOperation(message);
  
  // 播放错误音效
  audioHelper.playErrorAudio();
  
  // 使用uni.showToast实现自动消失的提示框，不阻止用户交互
  uni.showToast({
    title: message,
    icon: 'none',
    duration: 2000,
    mask: false // 设置为false，不显示透明蒙层，允许用户点击其他区域
  });
};

// 显示成功提示
const showSuccessToast = (message) => {
  // 播放成功音效
  audioHelper.playSuccessAudio();
  
  uni.showToast({
    title: message,
    icon: 'success',
    duration: 2000,
    mask: false // 设置为false，不显示透明蒙层，允许用户点击其他区域
  });
};

// 封装uni.showToast提示方法
const showMessage = (title, type = 'none', duration = 2000, logOperation = true) => {
  uni.showToast({
    title,
    icon: type,
    duration,
    mask: false
  });

  // 同时记录操作日志（如果需要记录且不是处理中或加载中等提示语）
  if (logOperation && !title.includes('中...') && !title.includes('处理中')) {
    recordFailedOperation(title);
  }
};

// 添加一个函数来标准化扫码结果格式
const standardizeBarcodeSeparators = (result) => {
  if (!result) return result;
  
  // 移除可能的开头[GS]
  let standardized = result;
  if (standardized.startsWith('^') || standardized.startsWith('\u001d')) {
    standardized = standardized.substring(1);
  }
  if (standardized.startsWith('[GS]')) {
    standardized = standardized.substring(4);
  }
  
  // 将支付宝扫码模块返回的^符号转换为[GS]
  standardized = standardized.replace(/\^/g, '[GS]');
  
  // 确保ASCII控制字符也被正确转换
  standardized = standardized.replace(/\u001d/g, '[GS]');
  standardized = standardized.replace(/\u000d/g, '[CR]');
  
  // 确保以[CR]结尾
  if (!standardized.endsWith('[CR]')) {
    standardized += '[CR]';
  }
  
  // 确保格式与扫码枪一致 - 示例: 010697169084025910WO25005[GS]112503251726032421JF240352000134[CR]
  return standardized;
};

// 打开扫码页面
const openScanCode = async () => {
  try {
    // 显示加载提示，但设置较短的时间
    uni.showLoading({
      title: '准备扫码...',
      mask: true
    });
    
    // 延迟一小段时间后自动关闭加载提示，避免卡在加载状态
    setTimeout(() => {
      uni.hideLoading();
    }, 1000);
    
    // 尝试使用支付宝的Mpaas-Scan-Module插件
    let usedMpaasModule = false;
    try {
      const mpaasScanModule = uni.requireNativePlugin("Mpaas-Scan-Module");
      
      if (mpaasScanModule && typeof mpaasScanModule.mpaasScan === 'function') {
        usedMpaasModule = true;
        
        // 在调用扫码前先关闭加载提示
        uni.hideLoading();
        
        mpaasScanModule.mpaasScan({
          // 扫码识别类型，参数可多选，qrCode、barCode，不设置，默认识别所有
          'scanType': ['qrCode', 'barCode'],
          // 是否隐藏相册，默认false不隐藏
          'hideAlbum': false,
          //相册选择照片识别错误提示(ios)
          'failedMsg': '未识别到条码，请重试',
          //Android支持全屏需要设置此参数
          'screenType': 'full',
          'timeoutInterval': '10', //设置超时时间
          'timeoutText': '未识别到条码？', //超时提醒文本
          'preserveGS1': true // 尝试保留GS1分隔符，如果支持的话
        }, (ret) => {
          // 确保加载提示已关闭
          uni.hideLoading();
          
          // 处理扫码结果
          if (ret.resp_code === 1000) {
            // 扫码成功
            if (!ret.resp_result || ret.resp_result.trim() === '') {
              showMessage('扫描结果为空');
              return;
            }
            
            // 标准化扫码结果，确保分隔符格式一致
            const standardizedResult = standardizeBarcodeSeparators(ret.resp_result.trim());
            
            // 设置扫码结果到输入框并直接处理
            scanCode.value = standardizedResult;
            
            // 直接调用handleScan处理扫码结果
            handleScan();
          } else if (ret.resp_code === 10) {
            // 用户取消扫码
            uni.showToast({
              title: '已取消扫码',
              icon: 'none',
              duration: 1500
            });
          } else {
            // 其他错误
            audioHelper.playErrorAudio();
            showMessage(ret.resp_message || '扫码失败，请重试');
          }
        });
      }
    } catch (mpaasError) {
      console.error('支付宝扫码模块加载失败:', mpaasError);
      usedMpaasModule = false;
    }
    
    // 如果支付宝扫码模块不可用，回退到scanCodeUtil
    if (!usedMpaasModule) {
      console.log('回退到scanCodeUtil进行扫码');
      
      // 确保加载提示已关闭
      uni.hideLoading();
      
      try {
        // 使用scanCode.js中的start方法进行扫码，并保留原始格式
        const result = await scanCodeUtil.start({ 
          preserveGS1Separators: true,
          rawFormat: true // 添加参数，尝试获取原始格式
        });
        
        // 标准化扫码结果，确保分隔符格式一致
        const standardizedResult = standardizeBarcodeSeparators(result);
        
        // 将扫码结果设置到输入框
        scanCode.value = standardizedResult;
        
        console.log('原始扫码结果:', result);
        console.log('标准化后结果:', standardizedResult);
        
        // 直接调用handleScan处理扫码结果
        handleScan();
      } catch (scanUtilError) {
        console.error('scanCodeUtil扫码失败:', scanUtilError);
        
        // 播放错误音效
        audioHelper.playErrorAudio();
        
        // 显示更具体的错误信息
        let errorMsg = '扫码失败';
        if (scanUtilError && scanUtilError.errMsg) {
          errorMsg = scanUtilError.errMsg;
          // 处理常见错误
          if (scanUtilError.errMsg.includes('cancel')) {
            errorMsg = '用户取消扫码';
          } else if (scanUtilError.errMsg.includes('permission')) {
            errorMsg = '相机权限被拒绝';
          }
        }
        
        uni.showToast({
          title: errorMsg,
          icon: 'none',
          duration: 2000
        });
      }
    }
  } catch (error) {
    // 确保加载提示已关闭
    uni.hideLoading();
    
    console.error('扫码功能异常:', error);
    
    // 播放错误音效
    audioHelper.playErrorAudio();
    
    // 显示错误信息
    uni.showToast({
      title: '扫码功能异常，请重试',
      icon: 'none',
      duration: 2000
    });
  }
};

// 发货方式选择
const onShipMethodChange = (e) => {
  const index = e.detail.value;
  selectedShipMethod.value = shipMethods[index];
};

// 获取物料列表
const fetchMaterialList = async () => {
  if (materialLoading.value || !orderNo.value) return;
  
  materialLoading.value = true;
  
  try {
    const params = {
      CFlag: '239',
      No: orderNo.value
    };
    
    const result = await getOrderInfo(params);
    
    if (result && result.data) {
      materialList.value = Array.isArray(result.data) ? result.data : [result.data];
    } else if (result && Array.isArray(result)) {
      materialList.value = result;
    } else {
      materialList.value = [];
      showMessage('获取物料列表失败');
    }
  } catch (error) {
    console.error('获取物料列表失败:', error);
    uni.showToast({
      title: '获取物料列表失败',
      icon: 'none',
      mask: false
    });
  } finally {
    materialLoading.value = false;
  }
};

// 查看序列号
const showSerialNumbers = async (material) => {
  currentMaterial.value = material;
  showSerialList.value = true;
  
  if (serialLoading.value) return;
  
  serialLoading.value = true;
  serialList.value = [];
  
  // 如果切换到了序列号详情，自动切换到序列号Tab
  if (currentTab.value !== 1) {
    currentTab.value = 1;
  }
  
  try {
    const params = {
      CFlag: '240',
      No: orderNo.value,
      // 使用DLID作为Item参数，而不是物料编码
      Item: itemId.value || ""
    };
    
    // 调用API时也使用DLID
    const result = await getOrderSerialDetail(orderNo.value, itemId.value || "", params);
    
    if (result && result.data) {
      serialList.value = Array.isArray(result.data) ? result.data : [result.data];
    } else if (result && Array.isArray(result)) {
      serialList.value = result;
    } else {
      serialList.value = [];
    }
  } catch (error) {
    console.error('获取序列号失败:', error);
    showMessage('获取序列号失败');
  } finally {
    serialLoading.value = false;
  }
};

// 获取发货信息
const fetchShippingInfo = async () => {
  if (shippingInfoLoading.value || !orderNo.value) return;
  
  shippingInfoLoading.value = true;
  
  try {
    
    // 确保能获取到正确的值
    const orderNoValue = orderNo.value;
    const itemCodeValue = orderDetail.value.DLID || '';
    
    // 构建完整的参数
    const params = {
      OP: 'GetOrderInfo',
      CFlag: '239',
      page: 1,
      limit: 20,
      No: orderNoValue,
      Item: itemCodeValue
    };
    
    // 调用获取发货信息的API
    const result = await getOrderInfo(params);
    
    if (result && result.data) {
      // 如果返回的是对象数据
      if (!Array.isArray(result.data)) {
        shippingInfo.value = result.data;
        shippingInfoList.value = [result.data]; // 转换为数组以便列表显示
      } 
      // 如果返回的是数组数据
      else {
        shippingInfoList.value = result.data;
        if (result.data.length > 0) {
          shippingInfo.value = result.data[0];
        }
      }
    } else if (result && Array.isArray(result)) {
      shippingInfoList.value = result;
      if (result.length > 0) {
        shippingInfo.value = result[0];
      }
    } else {
      shippingInfoList.value = [];
      showMessage('获取发货信息失败');
    }
    
    // 标记为已加载
    shippingInfoLoaded.value = true;
  } catch (error) {
    console.error('获取发货信息失败:', error);
    uni.showToast({
      title: '获取发货信息失败',
      icon: 'none',
      mask: false
    });
  } finally {
    shippingInfoLoading.value = false;
  }
};

// 提交发货
const submitDelivery = () => {
  // 表单验证
  if (!selectedShipMethod.value) {
    showErrorToast('请选择发货方式！');
    return;
  }
  
  if (!trackingNo.value) {
    showErrorToast('请输入运单号！');
    return;
  }
  
  if (!storageConditions.value) {
    showErrorToast('请输入储运条件！');
    return;
  }

  // 确认对话框
  uni.showModal({
    title: '确认发货',
    content: '确定要提交发货信息吗？',
    success: (res) => {
      if (res.confirm) {
        // 显示加载中
        uni.showLoading({
          title: '提交中...',
          mask: false
        });
        
        // 构建请求参数
        const params = {
          No: "",                          // 保留字段，置空
          Item: orderDetail.value.BillType || "",  // 单据类型
          Name: storageConditions.value,   // 储运条件
          A: trackingNo.value,             // 运单号
          B: itemId.value || "",           // DLID
          C: orderDetail.value.DNo || orderNo.value, // 发货单号
          D: selectedShipMethod.value,     // 发货方式
          E: shipper.value,                // 发货人
          F: shipDate.value,               // 发货日期
          Flag: "4"                        // 标识，与web端保持一致
        };
        
        // 调用发货接口
        callDeliverApi(params);
      }
    }
  });
};

// 调用发货接口
const callDeliverApi = async (params) => {
  try {
    uni.showLoading({
      title: '提交中...',
      mask: false
    });
    
    // 调用API
    const result = await submitPRDDeliver(params);
    
    // 处理响应
    if (result && result.Msg === 'Success') {
      // 成功处理
      showSuccessToast('装箱完成，请打印发货单');
      
      // 记录成功信息
      recordFailedOperation('装箱完成，请打印发货单');
      
      // 如果当前不在序列号详情Tab，则切换到序列号详情Tab
      if (currentTab.value !== 1) {
        currentTab.value = 1;
      }
      
      // 直接获取序列号数据，不再调用fetchMaterialList
      serialLoading.value = true;
      serialList.value = [];
      
      try {
        // 构建序列号查询参数，使用正确的参数
        const serialParams = {
          CFlag: '240',
          No: orderNo.value,
          // 重要：这里使用发货单号和DLID，而不是物料编码
          Item: itemId.value || ""
        };
        
        // 直接调用获取序列号的API
        const serialResult = await getOrderSerialDetail(orderNo.value, itemId.value || "", serialParams);
        
        if (serialResult && serialResult.data) {
          serialList.value = Array.isArray(serialResult.data) ? serialResult.data : [serialResult.data];
        } else if (serialResult && Array.isArray(serialResult)) {
          serialList.value = serialResult;
        } else {
          serialList.value = [];
        }
      } catch (error) {
        console.error('获取序列号失败:', error);
        uni.showToast({
          title: '获取序列号失败',
          icon: 'none',
          mask: false
        });
      } finally {
        serialLoading.value = false;
      }
    } else if (result && result.Msg === 'Error') {
      // 错误处理
      showErrorToast(result.Message || '操作失败');
    } else if (result && result.Msg === 'LoginError') {
      // 登录失效处理
      showErrorToast(result.Message || '登录失效');
      
      // 跳转到登录页
      setTimeout(() => {
        uni.reLaunch({
          url: '/pages/login/login'
        });
      }, 1500);
    } else {
      // 其他错误
      showErrorToast('操作失败，请重试');
    }
  } catch (error) {
    console.error('发货请求失败:', error);
    showErrorToast('系统出错，请重试！');
  } finally {
    uni.hideLoading();
  }
};

// 生命周期
onMounted(() => {
  // 预加载音频文件
  audioHelper.preloadAudios();
  
  // 添加延时聚焦，确保组件已完全渲染
  setTimeout(() => {
    focusOnScanInput();
  }, 500);
  
  // 检查是否有保存的状态
  const savedState = uni.getStorageSync('executeDeliverState');
  if (savedState) {
    try {
      const state = JSON.parse(savedState);
      // 恢复页面状态
      orderNo.value = state.orderNo || '';
      itemId.value = state.itemId || '';
      orderDetail.value = state.orderDetail || {};
      trackingNo.value = state.trackingNo || '';
      contractNo.value = state.contractNo || '';
      storageConditions.value = state.storageConditions || '常温';
      singleBoxQuantity.value = state.singleBoxQuantity || '60';
      selectedShipMethod.value = state.selectedShipMethod || '快递';
      currentTab.value = state.currentTab || 0;
      
      // 获取物料列表和发货信息
      fetchMaterialList();
      
      // 首次进入页面时自动获取序列号详情
      fetchSerialDetail();
      
      // 清除保存的状态，避免下次进入页面时重复使用
      uni.removeStorageSync('executeDeliverState');
      
      return; // 如果恢复了状态，则不再执行后续的初始化逻辑
    } catch (error) {
      // 清除可能损坏的状态数据
      uni.removeStorageSync('executeDeliverState');
    }
  }
  
  // #ifdef APP-PLUS || MP-WEIXIN
  // APP环境下通过事件通道获取数据
  try {
    // 尝试多种方式获取事件通道
    let eventChannel;
    
    try {
      // 方法1：通过当前页面实例获取
      const pages = getCurrentPages();
      const currentPage = pages[pages.length - 1];
      
      if (currentPage.getOpenerEventChannel) {
        eventChannel = currentPage.getOpenerEventChannel();
      } else if (currentPage.$getOpenerEventChannel) {
        eventChannel = currentPage.$getOpenerEventChannel();
      } else {
        // 方法2：直接使用getOpenerEventChannel
        eventChannel = getOpenerEventChannel();
      }
    } catch (err1) {
      console.error('获取事件通道失败(方法1):', err1);
      
      try {
        // 尝试其他方法获取事件通道
        const pages = getCurrentPages();
        if (pages.length > 0) {
          const currentPage = pages[pages.length - 1];
          
          // 尝试使用其他可能的方法
          if (typeof currentPage.$vm !== 'undefined' && currentPage.$vm.$mp && currentPage.$vm.$mp.page) {
            eventChannel = currentPage.$vm.$mp.page.getOpenerEventChannel();
          } else {
            throw new Error('无法获取事件通道');
          }
        } else {
          throw new Error('无法获取当前页面');
        }
      } catch (err2) {
        throw err2;
      }
    }
    
    // 使用事件通道获取数据
    eventChannel.on('deliverExecute', (data) => {
      orderDetail.value = data.item || {};
      orderNo.value = data.orderNo || '';
      itemId.value = data.id || '';
      
      // 保存到本地存储，以便在打印页面返回时恢复
      uni.setStorageSync('lastOrderNo', data.orderNo || '');
      uni.setStorageSync('lastItemId', data.id || '');
      
      // 初始化表单数据
      if (orderDetail.value) {
        trackingNo.value = orderDetail.value.TrackingNo || '';
        contractNo.value = orderDetail.value.ContractNo || '';
        storageConditions.value = orderDetail.value.StorageConditions || '常温';
        
        // 设置发货方式
        const shipVia = orderDetail.value.ShipVia;
        if (shipVia && shipMethods.includes(shipVia)) {
          selectedShipMethod.value = shipVia;
        }
      }
      
      // 获取物料列表和发货信息
      fetchMaterialList();
      
      // 首次进入页面时自动获取序列号详情
      fetchSerialDetail();
    });
  } catch (error) {
    console.error('获取页面参数失败:', error);
    
    // 尝试从URL参数或本地存储中恢复数据
    try {
      const pages = getCurrentPages();
      const currentPage = pages[pages.length - 1];
      
      // 检查页面参数
      const options = currentPage.$page?.options || {};
      
      // 从URL或页面参数中获取数据
      if (options.orderNo) {
        orderNo.value = options.orderNo;
        
        // 如果有ID参数
        if (options.id) {
          itemId.value = options.id;
        }
        
        // 尝试从本地存储获取数据
        const lastOrderNo = uni.getStorageSync('lastOrderNo');
        const lastItemId = uni.getStorageSync('lastItemId');
        
        if (lastOrderNo && lastItemId) {
          orderNo.value = lastOrderNo;
          itemId.value = lastItemId;
        }
        
        // 首次进入页面时自动获取序列号详情
        fetchSerialDetail();
      } else {
        console.error('URL参数中没有订单号');
        
        // 尝试从本地存储获取数据
        const lastOrderNo = uni.getStorageSync('lastOrderNo');
        const lastItemId = uni.getStorageSync('lastItemId');
        
        if (lastOrderNo && lastItemId) {
          orderNo.value = lastOrderNo;
          itemId.value = lastItemId;
          
          // 首次进入页面时自动获取序列号详情
          fetchSerialDetail();
        } else {
          uni.showToast({
            title: '获取参数失败',
            icon: 'none'
          });
        }
      }
    } catch (optionsError) {
      uni.showToast({
        title: '获取参数失败',
        icon: 'none'
      });
    }
  }
  // #endif
  
  // #ifdef H5
  // H5环境通过路由参数获取数据
  try {
    const pages = getCurrentPages();
    const currentPage = pages[pages.length - 1];
    const query = currentPage.$page?.options || {};
    
    if (query && query.orderData) {
      // 从URL参数中解析订单数据
      try {
        const decodedData = decodeURIComponent(query.orderData);
        orderDetail.value = JSON.parse(decodedData);
        orderNo.value = orderDetail.value.DNo || query.orderNo || '';
        
        // 使用传入的物料编码或ID参数
        itemId.value = orderDetail.value.DLID || query.id || '';
        
        // 初始化表单数据
        if (orderDetail.value) {
          trackingNo.value = orderDetail.value.TrackingNo || '';
          contractNo.value = orderDetail.value.ContractNo || '';
          storageConditions.value = orderDetail.value.StorageConditions || '常温';
          
          // 设置发货方式
          const shipVia = orderDetail.value.ShipVia;
          if (shipVia && shipMethods.includes(shipVia)) {
            selectedShipMethod.value = shipVia;
          }
        }
        
        // 获取物料列表和发货信息
        fetchMaterialList();
        
        // 首次进入页面时自动获取序列号详情
        fetchSerialDetail();
      } catch (error) {
        console.error('解析订单数据失败:', error);
        
        // 如果解析失败，尝试直接使用URL参数
        if (query.orderNo) {
          orderNo.value = query.orderNo;
          itemId.value = query.id || '';
          
          // 尝试从本地存储获取数据
          const lastOrderNo = uni.getStorageSync('lastOrderNo');
          const lastItemId = uni.getStorageSync('lastItemId');
          
          if (lastOrderNo && lastItemId) {
            orderNo.value = lastOrderNo;
            itemId.value = lastItemId;
          }
          
          // 首次进入页面时自动获取序列号详情
          fetchSerialDetail();
        } else {
          uni.showToast({
            title: '获取订单详情失败',
            icon: 'none'
          });
        }
      }
    } else if (query.orderNo) {
      // 如果没有完整数据但有orderNo参数
      orderNo.value = query.orderNo;
      itemId.value = query.id || '';
      
      // 尝试从本地存储获取数据
      const lastOrderNo = uni.getStorageSync('lastOrderNo');
      const lastItemId = uni.getStorageSync('lastItemId');
      
      if (lastOrderNo && lastItemId) {
        orderNo.value = lastOrderNo;
        itemId.value = lastItemId;
      }
      
      // 首次进入页面时自动获取序列号详情
      fetchSerialDetail();
    } else {
      uni.showToast({
        title: '参数不完整',
        icon: 'none'
      });
    }
  } catch (error) {
    console.error('H5环境获取详情数据失败:', error);
    uni.showToast({
      title: '获取详情数据失败',
      icon: 'none'
    });
  }
  // #endif
});

// 在组件卸载前释放音频资源
onBeforeUnmount(() => {
  audioHelper.destroyAudios();
});

// 操作记录相关
const showLogModal = ref(false);
const operationLogs = ref([]);

// 显示操作记录弹框
const showOperationLog = () => {
  showLogModal.value = true;
};

// 关闭操作记录弹框
const closeOperationLog = () => {
  showLogModal.value = false;
};

// 记录失败操作
const recordFailedOperation = (message) => {
  const now = new Date();
  const timeString = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}-${String(now.getDate()).padStart(2, '0')} ${String(now.getHours()).padStart(2, '0')}:${String(now.getMinutes()).padStart(2, '0')}:${String(now.getSeconds()).padStart(2, '0')}`;
  
  operationLogs.value.unshift({
    time: timeString,
    message: message
  });
};

// 删除序列号项
const deleteSerialItem = (item, index) => {
  // 显示确认对话框
  uni.showModal({
    title: '确认删除',
    content: `确定要删除序列号 ${item.BatchSN || ''} 吗？`,
    success: (res) => {
      if (res.confirm) {
        // 用户点击确定，执行删除操作
        deleteSerialItemConfirm(item, index);
      }
    }
  });
};

// 确认删除序列号项
const deleteSerialItemConfirm = async (item, index) => {
  try {
    uni.showLoading({
      title: '删除中...',
      mask: false
    });
    
    // 构建删除参数，与web端保持一致
    const params = {
      No: item.BatchSN || "",    // 序列号
      MNo: item.MaterNo || "",   // 物料编码
      A: item.Qty || "",         // 数量
      B: item.DLID || "",        // DLID
      C: item.DNo || "",         // 发货单号
      D: orderDetail.value.BillType || "" // 单据类型
    };
    
    // 调用删除序列号API
    const result = await deleteSerialNo(params);
    
    // 处理响应结果
    if (result && result.Msg === 'Success') {
      // 从列表中移除该项
      serialList.value.splice(index, 1);
      
      // 显示成功消息
      uni.hideLoading();
      showMessage(result.Message || '删除成功');
      
      // 记录操作日志
      recordFailedOperation(`删除序列号 ${item.BatchSN || ''} 成功`);
    } else if (result && result.Msg === 'Error') {
      // 错误处理
      uni.hideLoading();
      showMessage(result.Message || '删除失败');
    } else if (result && result.Msg === 'LoginError') {
      // 登录失效处理
      uni.hideLoading();
      uni.showToast({
        title: result.Message || '登录失效',
        icon: 'none',
        duration: 2000,
        mask: false
      });
      
      // 跳转到登录页
      setTimeout(() => {
        uni.reLaunch({
          url: '/pages/login/login'
        });
      }, 1500);
    } else {
      // 其他错误
      uni.hideLoading();
      uni.showToast({
        title: '删除失败，请重试',
        icon: 'none',
        duration: 2000,
        mask: false
      });
    }
  } catch (error) {
    console.error('删除序列号失败:', error);
    uni.hideLoading();
    uni.showToast({
      title: '系统出错，请重试！',
      icon: 'none',
      duration: 2000,
      mask: false
    });
  }
};

// 打印发货单
const printDelivery = async () => {
  // 显示加载中
  uni.showLoading({
    title: '准备打印...',
    mask: true
  });
  
  try {
    // 获取发货单号和单箱数量
    const deliverNo = orderDetail.value.DNo || orderNo.value;
    const boxQuantity = singleBoxQuantity.value || '60'; // 使用单箱数量作为Item参数
    
    // 从序列号列表中获取箱号，而不是从本地存储
    let boxNo = '';
    if (serialList.value && serialList.value.length > 0) {
      // 获取第一个有效的箱号
      for (const item of serialList.value) {
        if (item.BoxNo) {
          boxNo = item.BoxNo;
          break;
        }
      }
    }
    
    // 保存当前订单号和ID到本地存储，以便在打印页面返回时恢复
    uni.setStorageSync('lastOrderNo', orderNo.value);
    uni.setStorageSync('lastItemId', itemId.value);
    
    // 调用获取打印信息的接口，使用更新后的参数
    const result = await getDeliverPrintInfo(deliverNo, boxQuantity, boxNo);

    // 检查是否允许打印
    if (result && result.IsPrint === 'N') {
      // 不允许打印，显示提示并返回
      uni.hideLoading();
      showMessage('当前订单不允许打印');
      return;
    }
    
    // 创建适合资福医疗公司格式的打印数据
    const printData = {
      company: '深圳市资福医疗技术有限公司',
      title: '装箱清单',
      deliverNo: deliverNo,
      shipNo: trackingNo.value || '',
      boxNo: boxNo || '',
      items: [],
      tempInfo: result?.tempInfo || []
    };
    
    // 优先使用接口返回的数据
    if (result && result.Data) {
      // 设置箱号和运单号
      if (result.Data.delivery && result.Data.delivery.length > 0) {
        printData.shipNo = result.Data.delivery[0].TrackingNo || trackingNo.value || '';
        printData.deliverNo = result.Data.delivery[0].DNo || deliverNo;
        printData.boxNo = result.Data.delivery[0].BoxNo || boxNo || '';
      }
      
      // 使用接口返回的明细数据
      if (result.Data.deliveryDetail && Array.isArray(result.Data.deliveryDetail) && result.Data.deliveryDetail.length > 0) {
        result.Data.deliveryDetail.forEach(item => {
          printData.items.push({
            ID: item.ID || '', // 确保ID字段名称大写，与后端返回保持一致
            name: item.PName || '',
            batchNo: item.BatchSN || '-',
            quantity: parseInt(item.Qty || '1', 10),
            productBatch: item.PrdBatch || '-', // 添加生产批号
            productionDate: item.PrdDate || '-', // 添加生产日期
            expiryDate: item.EfftDate || '-', // 添加使用期限
            // 新增字段
            partSpec: item.PartSpec || '', // 部件规格型号
            partName: item.PartName || ''  // 部件名称
          });
        });
      }
    }
    // 如果接口没有返回有效数据，则使用序列号列表作为备选
    else if (serialList.value && serialList.value.length > 0) {
      // 从序列号列表中获取箱号（再次确认）
      if (!printData.boxNo && serialList.value.length > 0) {
        for (const item of serialList.value) {
          if (item.BoxNo) {
            printData.boxNo = item.BoxNo;
            break;
          }
        }
      }
      
      // 直接将序列号列表转换为打印项目
      serialList.value.forEach((item, index) => {
        printData.items.push({
          ID: item.ID || (index + 1), // 使用序列号中的ID或者索引+1
          name: item.MaterName || '',
          batchNo: item.BatchSN || '-',
          quantity: parseInt(item.Qty || '1', 60),
          productBatch: item.PrdBatch || '-', // 添加生产批号
          productionDate: item.PrdDate || '-', // 添加生产日期
          expiryDate: item.EfftDate || '-', // 添加使用期限
          // 新增字段
          partSpec: item.PartSpec || '', // 部件规格型号
          partName: item.PartName || ''  // 部件名称
        });
      });
    }
    
    // 确保至少有一项数据
    if (printData.items.length === 0) {
      // 如果没有数据，添加一个空项作为占位
      printData.items.push({
        id: '',
        name: '无物料数据',
        batchNo: '-',
        quantity: 0
      });
    }
    
    console.log('准备发送到打印预览的数据:', printData);

    // 将打印数据存储到本地，以便打印页面使用
    uni.setStorageSync('printData', JSON.stringify(printData));
    
    // 保存当前页面状态，以便返回时恢复
    uni.setStorageSync('executeDeliverState', JSON.stringify({
      orderNo: orderNo.value,
      itemId: itemId.value,
      orderDetail: orderDetail.value,
      trackingNo: trackingNo.value,
      contractNo: contractNo.value,
      storageConditions: storageConditions.value,
      singleBoxQuantity: singleBoxQuantity.value,
      selectedShipMethod: selectedShipMethod.value,
      currentTab: currentTab.value
    }));
    
    // 使用navigateTo而非redirectTo，这样返回时不会销毁当前页面
    uni.navigateTo({
      url: '/pages/print/printPreview',
      success: () => {
        uni.hideLoading();
        
        // 记录打印操作
        recordFailedOperation('已跳转到打印预览页面，单号：' + printData.deliverNo);
      },
      fail: (err) => {
        console.error('跳转到打印预览页面失败:', err);
        uni.hideLoading();
        
        uni.showToast({
          title: '跳转打印页面失败',
          icon: 'none',
          duration: 2000
        });
      }
    });
  } catch (error) {
    console.error('准备打印数据失败:', error);
    uni.hideLoading();
    uni.showToast({
      title: '准备打印数据失败',
      icon: 'none',
      duration: 2000
    });
  }
};

// 添加获取序列号详情的函数
const fetchSerialDetail = () => {
  if (!orderNo.value || serialLoading.value) return;
  
  serialLoading.value = true;
  serialList.value = [];
  
  try {
    const params = {
      CFlag: '240',
      No: orderNo.value,
      Item: itemId.value || ""
    };
    
    getOrderSerialDetail(orderNo.value, itemId.value || "", params).then(result => {
      
      if (result && result.data) {
        serialList.value = Array.isArray(result.data) ? result.data : [result.data];
      } else if (result && Array.isArray(result)) {
        serialList.value = result;
      } else {
        serialList.value = [];
      }
      serialLoading.value = false;
      
      // 在数据加载完成后，延时聚焦到扫描输入框
      setTimeout(() => {
        focusOnScanInput();
      }, 200);
    }).catch(error => {
      showMessage('获取序列号失败');
      serialLoading.value = false;
      
      // 即使数据加载失败，也要聚焦到扫描输入框
      setTimeout(() => {
        focusOnScanInput();
      }, 200);
    });
  } catch (error) {
    console.error('获取序列号失败:', error);
    serialLoading.value = false;
    
    // 发生异常时也聚焦到扫描输入框
    setTimeout(() => {
      focusOnScanInput();
    }, 200);
  }
};

// 添加扫描运单号的函数
const openTrackingNoScan = async () => {
  try {
    // 显示加载提示，但设置较短的时间
    uni.showLoading({
      title: '准备扫码...',
      mask: true
    });
    
    // 延迟一小段时间后自动关闭加载提示，避免卡在加载状态
    setTimeout(() => {
      uni.hideLoading();
    }, 1000);
    
    // 尝试使用支付宝的Mpaas-Scan-Module插件
    let usedMpaasModule = false;
    try {
      const mpaasScanModule = uni.requireNativePlugin("Mpaas-Scan-Module");
      
      if (mpaasScanModule && typeof mpaasScanModule.mpaasScan === 'function') {
        usedMpaasModule = true;
        
        // 在调用扫码前先关闭加载提示
        uni.hideLoading();
        
        mpaasScanModule.mpaasScan({
          // 扫码识别类型，参数可多选，qrCode、barCode，不设置，默认识别所有
          'scanType': ['qrCode', 'barCode'],
          // 是否隐藏相册，默认false不隐藏
          'hideAlbum': false,
          //相册选择照片识别错误提示(ios)
          'failedMsg': '未识别到条码，请重试',
          //Android支持全屏需要设置此参数
          'screenType': 'full',
          'timeoutInterval': '10', //设置超时时间
          'timeoutText': '未识别到条码？', //超时提醒文本
          'preserveGS1': true // 尝试保留GS1分隔符，如果支持的话
        }, (ret) => {
          // 确保加载提示已关闭
          uni.hideLoading();
          
          // 处理扫码结果
          if (ret.resp_code === 1000) {
            // 扫码成功
            if (!ret.resp_result || ret.resp_result.trim() === '') {
              showMessage('扫描结果为空');
              return;
            }
            
            // 处理扫码结果，提取运单号
            let scanResult = ret.resp_result.trim();
            
            // 处理顺丰快递链接格式
            if (scanResult.includes('ucmp.sf-express.com') && scanResult.includes('p1=')) {
              // 提取p1=后面的运单号
              const match = scanResult.match(/p1=([A-Za-z0-9]+)/);
              if (match && match[1]) {
                scanResult = match[1];
              }
            }
            
            // 设置运单号
            trackingNo.value = scanResult;
            
            // 播放成功音效
            audioHelper.playSuccessAudio();
          } else if (ret.resp_code === 10) {
            // 用户取消扫码
            console.log('用户取消扫码');
            uni.showToast({
              title: '已取消扫码',
              icon: 'none',
              duration: 1500
            });
          } else {
            // 其他错误
            audioHelper.playErrorAudio();
            uni.showToast({
              title: ret.resp_message || '扫码失败，请重试',
              icon: 'none',
              duration: 2000
            });
            console.error('扫码错误:', ret.resp_message);
          }
        });
      }
    } catch (mpaasError) {
      console.error('支付宝扫码模块加载失败:', mpaasError);
      usedMpaasModule = false;
    }
    
    // 如果支付宝扫码模块不可用，回退到scanCodeUtil
    if (!usedMpaasModule) {
      console.log('回退到scanCodeUtil进行扫码');
      
      // 确保加载提示已关闭
      uni.hideLoading();
      
      try {
        // 使用scanCode.js中的start方法进行扫码
        const result = await scanCodeUtil.start();
        
        // 处理扫码结果，提取运单号
        let scanResult = result;
        
        // 处理顺丰快递链接格式
        if (scanResult.includes('ucmp.sf-express.com') && scanResult.includes('p1=')) {
          // 提取p1=后面的运单号
          const match = scanResult.match(/p1=([A-Za-z0-9]+)/);
          if (match && match[1]) {
            scanResult = match[1];
          }
        }
        
        // 设置运单号
        trackingNo.value = scanResult;
        
        // 播放成功音效
        audioHelper.playSuccessAudio();
      } catch (scanUtilError) {
        console.error('scanCodeUtil扫码失败:', scanUtilError);
        
        // 播放错误音效
        audioHelper.playErrorAudio();
        
        // 显示更具体的错误信息
        let errorMsg = '扫码失败';
        if (scanUtilError && scanUtilError.errMsg) {
          errorMsg = scanUtilError.errMsg;
          // 处理常见错误
          if (scanUtilError.errMsg.includes('cancel')) {
            errorMsg = '用户取消扫码';
          } else if (scanUtilError.errMsg.includes('permission')) {
            errorMsg = '相机权限被拒绝';
          }
        }
        
        uni.showToast({
          title: errorMsg,
          icon: 'none',
          duration: 2000
        });
      }
    }
  } catch (error) {
    // 确保加载提示已关闭
    uni.hideLoading();
    
    console.error('扫码功能异常:', error);
    
    // 播放错误音效
    audioHelper.playErrorAudio();
    
    // 显示错误信息
    uni.showToast({
      title: '扫码功能异常，请重试',
      icon: 'none',
      duration: 2000
    });
  }
  };

// 添加聚焦扫码输入框的函数
const focusOnScanInput = () => {
  // 先取消焦点状态，然后延迟设置，触发组件重新渲染
  scanInputFocused.value = false;
  setTimeout(() => {
    scanInputFocused.value = true;
  }, 100);
};
</script>

<style scoped>
/* 导航栏容器 */
.nav-bar-container {
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 100;
}

/* 导航栏右侧图标 */
.nav-right-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 44px;
  height: 44px;
}

.add-icon {
  width: 20px;
  height: 20px;
}

/* 主体内容区域 */
.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  width: 100%;
  overflow: hidden;
  position: relative;
  margin-top: 10px; /* 减小与标题栏的距离 */
}

/* 容器样式 */
.container {
  height: 100vh;
  width: 100%;
  display: flex;
  flex-direction: column;
  background-color: #f7f9fc;
  position: relative;
  overflow: hidden;
  margin: 0;
  border: none;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, sans-serif;
}

/* 扫描输入框区域 */
.scan-area {
  display: flex;
  flex-direction: column;
  padding: 5px 16px;
  background-color: #fff;
  border-bottom: 1px solid #f0f0f0;
}

.scan-input-wrapper {
  display: flex;
  align-items: center;
}

.scan-btn {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 8px; /* 添加右侧间距 */
}

.scan-icon {
  width: 24px;
  height: 24px;
}

.scan-input {
  flex: 1;
}

/* 自定义uni-easyinput组件样式 */
.scan-input :deep(.uni-easyinput__content) {
  min-height: 35px;
  height: 35px;
  line-height: 35px;
}

.scan-input :deep(.uni-easyinput__placeholder-class) {
  height: 35px;
  line-height: 35px;
}

/* 表单输入框样式 */
.form-input {
  width: 100%;
}

/* 自定义uni-easyinput表单样式 */
.form-input :deep(.uni-easyinput__content) {
  min-height: 40px;
  height: 40px;
  line-height: 40px;
  background-color: transparent;
}

.form-input :deep(.is-input-border) {
  border: none !important;
  border-bottom: 1px solid #e2e8f0 !important;
}

.form-input :deep(.is-focused) {
  border-bottom-color: #0c873d !important;
}

/* 优化下拉框样式，使用更细的底线 */
.form-picker {
  height: 40px;
  border-radius: 0;
  border: none;
  border-bottom: 1px solid #e2e8f0;
  padding: 0 2px;
  font-size: 14px;
  width: 100%;
  box-sizing: border-box;
  background-color: #fff;
  position: relative;
}

.picker-value {
  height: 40px;
  line-height: 40px;
  color: #334155;
  position: relative;
  padding-right: 20px;
}

.picker-value::after {
  content: '';
  position: absolute;
  right: 2px;
  top: 50%;
  transform: translateY(-50%);
  width: 0;
  height: 0;
  border-left: 5px solid transparent;
  border-right: 5px solid transparent;
  border-top: 5px solid #64748b;
}

/* 添加表单标签与输入框的间距 */
.form-label {
  font-size: 13px;
  color: #64748b;
  margin-bottom: 4px;
  display: block;
}

/* 调整表单项间距 */
.form-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

/* 表单项样式 */
.form-item {
  margin-bottom: 0;
  position: relative;
}

/* 调整基本信息卡片内部样式 */
.info-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

/* 调整卡片内边距 */
.card-body {
  padding: 16px;
}

/* Tab栏样式 */
.tabs {
  display: flex;
  background-color: #fff;
  border-bottom: 1px solid #f0f0f0;
  position: relative;
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 12px 0;
  font-size: 14px;
  color: #666;
  position: relative;
}

.tab-item.active {
  color: #0c873d;
  font-weight: 500;
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 20px;
  height: 3px;
  background-color: #0c873d;
  border-radius: 3px;
}

/* Tab内容区 */
.tab-content {
  flex: 1;
  height: 0;
  padding: 8px;
  box-sizing: border-box;
}

.tab-pane {
  height: 100%;
}

/* 卡片通用样式 */
.info-card,
.form-card,
.materials-card,
.serial-card,
.detail-card {
  background-color: #ffffff;
  border-radius: 12px;
  margin-bottom: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

.card-header {
  padding: 8px 16px;
  border-bottom: 1px solid #f1f5f9;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.card-title {
  font-size: 15px;
  font-weight: 600;
  color: #1e293b;
}

.card-subtitle {
  font-size: 12px;
  color: #64748b;
}

.card-body {
  padding: 12px 16px;
}

/* 基本信息样式 */
.info-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.info-item {
  width: 100%;
  margin-bottom: 0;
}

.info-label {
  font-size: 13px;
  color: #64748b;
  margin-bottom: 2px;
  display: block;
}

.info-value {
  font-size: 14px;
  color: #334155;
  display: block;
  word-break: break-all;
}

/* 物料列表样式 */
.material-list {
  margin-top: 8px;
}

/* 状态标签 */
.status-tag {
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  color: white;
}

.status-warning {
  background-color: #E6A23C; /* 黄色警告色 */
}

.status-success {
  background-color: #67C23A; /* 绿色成功色 */
}

.status-info {
  background-color: #909399; /* 灰色信息色 */
}

.material-item {
  border-bottom: 1px solid #f1f5f9;
  padding-bottom: 12px;
  margin-bottom: 12px;
}

.material-item:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.material-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.material-name {
  font-size: 14px;
  font-weight: 500;
  color: #1e293b;
  flex: 1;
}

.material-code {
  font-size: 12px;
  color: #64748b;
}

.material-info {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 8px;
}

.material-info-item {
  flex: 1;
  min-width: 30%;
  margin-bottom: 4px;
}

.material-actions {
  display: flex;
  justify-content: flex-end;
}

.action-button {
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 12px;
  background-color: rgba(12, 135, 61, 0.1);
  color: #0c873d;
  text-align: center;
}

/* 序列号列表样式 */
.serial-header {
  padding: 8px 16px;
  border-bottom: 1px solid #f1f5f9;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: #ffffff;
  border-radius: 12px 12px 0 0;
  margin: 4px 4px 0 4px;
}

.serial-list {
  margin: 0 4px;
  padding: 8px;
  display: flex;
  flex-direction: column;
  gap: 10px; /* 增加卡片间距 */
  background-color: #ffffff;
  border-radius: 0 0 12px 12px;
}

.serial-item {
  background-color: #ffffff;
  border-radius: 8px;
  padding: 12px; /* 增加内边距 */
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0; /* 添加边框增强视觉区分 */
  position: relative; /* 相对定位用于放置删除按钮 */
}

.serial-item .serial-item-header {
  margin-bottom: 8px;
  padding: 0 0 6px 0;
  border-bottom: 1px solid #e2e8f0;
  border-radius: 0;
  margin: 0;
  background-color: transparent;
}

.serial-title {
  font-size: 14px; /* 增大字体 */
  font-weight: 600;
  color: #0f172a;
  display: block;
  white-space: normal; /* 允许换行 */
  overflow: visible; /* 允许内容超出显示 */
  text-overflow: clip; /* 不使用省略号 */
  line-height: 1.3; /* 增加行高 */
  margin-bottom: 4px; /* 增加与下方编码的间距 */
}

.serial-code {
  font-size: 12px;
  color: #64748b;
  display: block;
}

.serial-body {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px 12px; /* 增加行列间距 */
}

.serial-info {
  display: flex;
  flex-direction: column;
}

.serial-label {
  font-size: 10px;
  color: #64748b;
  margin-bottom: 1px;
}

.serial-value {
  font-size: 12px;
  color: #334155;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 序列号删除按钮样式 - 改为右上角 */
.serial-delete-btn {
  position: absolute;
  top: 8px;
  right: 8px;
  width: 20px;
  height: 20px;
  background-color: rgba(220, 38, 38, 0.1);
  color: #dc2626;
  font-size: 16px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  z-index: 2;
  line-height: 1;
}

.serial-delete-btn:active {
  background-color: rgba(220, 38, 38, 0.2);
}

.item-count {
  font-size: 12px;
  color: #64748b;
}

/* 加载中和空数据提示 - 通用样式 */
.loading-box, .empty-list, .empty-box {
  padding: 24px;
  text-align: center;
  color: #64748b;
  font-size: 14px;
  background-color: #ffffff;
  border-radius: 10px;
  margin: 4px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.06);
}

/* 序列号Tab特殊样式 */
.serial-tab-content .loading-box,
.serial-tab-content .empty-list {
  border-radius: 0 0 12px 12px;
  margin: 0 4px 4px 4px;
}

/* 发货信息列表样式 */
.shipping-list {
  display: flex;
  flex-direction: column;
  gap: 0px;
}

.shipping-item {
  background-color: #ffffff;
  border-radius: 10px;
  padding: 16px;
  margin-bottom: 8px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.06);
  border: 1px solid #f1f5f9;
}

.shipping-body {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.shipping-info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 14px;
  margin-top: 4px;
}

.shipping-info {
  display: flex;
  flex-direction: column;
}

.shipping-label {
  font-size: 12px;
  color: #64748b;
  margin-bottom: 3px;
}

.shipping-value {
  font-size: 14px;
  color: #334155;
  font-weight: 500;
}

/* 底部操作栏 */
.footer {
  height: 60px;
  background-color: #ffffff;
  border-top: 1px solid #f1f5f9;
  display: flex;
  padding: 8px 16px;
  box-sizing: border-box;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.03);
  justify-content: space-between; /* 修改为两端对齐 */
}

.footer-button {
  width: 48%; /* 修改宽度为48%，留出间距 */
  height: 44px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 15px;
  font-weight: 500;
}

.footer-button.submit {
  background-color: #0c873d;
  color: #ffffff;
}

.footer-button.print {
  background-color: #f0f9ff;
  color: #0369a1;
  border: 1px solid #bae6fd;
}

/* 安全区域适配 */
/* #ifdef APP-PLUS */
.footer {
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}
/* #endif */

/* 新的CSS样式 */
.waiting-scan-btn {
  display: none;
}

.info-divider {
  display: none;
}

.info-item.form-item {
  margin-bottom: 8px;
}

.info-item.form-item .info-label {
  margin-bottom: 2px;
}

/* uni-easyinput组件已处理聚焦效果，无需额外样式 */

/* 添加按钮样式 */
.add-btn {
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  width: 26px;
  height: 26px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.add-icon {
  width: 20px;
  height: 20px;
}

.add-btn:active {
  background-color: rgba(0, 0, 0, 0.05);
}

/* 添加运单号输入框样式 */
.tracking-input-wrapper {
  display: flex;
  align-items: center;
  width: 100%;
}

.tracking-input {
  flex: 1;
}

.tracking-input :deep(.uni-easyinput__content-input) {
  padding-right: 40px; /* 为扫码按钮留出空间 */
}

.tracking-scan-btn {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: -36px; /* 使按钮覆盖在输入框上 */
}

.bill-type-fh {
  color: #67C23A; /* 绿色成功色 */
  font-weight: 600;
}

.bill-type-th {
  color: #F56C6C; /* 红色错误色，与其他页面保持一致 */
  font-weight: 600;
}
</style> 
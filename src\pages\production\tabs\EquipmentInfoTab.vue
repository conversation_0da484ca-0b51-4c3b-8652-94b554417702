<template>
  <view class="tab-pane">
    <view class="info-card">
      <view class="card-body">
        <view v-if="equipmentInfo.length === 0" class="empty-tip">暂无设备信息</view>
        <view v-else class="equipment-list">
          <view v-for="(item, index) in equipmentInfo" :key="index" class="equipment-item">
            <view class="equipment-header">
              <text class="equipment-title">{{ item.equipmentName || '未知设备' }}</text>
            </view>
            <view class="equipment-body">
              <view class="equipment-info">
                <text class="equipment-label">设备编号</text>
                <text class="equipment-value">{{ item.equipmentNo || '-' }}</text>
              </view>
              <view class="equipment-info">
                <text class="equipment-label">设备状态</text>
                <text class="equipment-value">{{ item.status || '-' }}</text>
              </view>
              <view class="equipment-info">
                <text class="equipment-label">使用时间</text>
                <text class="equipment-value">{{ item.useTime || '-' }}</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
// 定义props接收父组件传递的数据
const props = defineProps({
  equipmentInfo: {
    type: Array,
    default: () => []
  }
});
</script>

<style scoped>
/* 卡片样式 */
.info-card {
  background-color: #ffffff;
  border-radius: 10px;
  margin-bottom: 10px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.card-body {
  padding: 15px;
}

/* 空数据提示 */
.empty-tip {
  text-align: center;
  padding: 30px 0;
  color: #9ca3af;
  font-size: 14px;
}

/* 设备信息样式 */
.equipment-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.equipment-item {
  background-color: #f8fafc;
  border-radius: 8px;
  padding: 12px;
  border: 1px solid #e2e8f0;
}

.equipment-header {
  margin-bottom: 8px;
  padding-bottom: 6px;
  border-bottom: 1px solid #e2e8f0;
}

.equipment-title {
  font-size: 14px;
  font-weight: 600;
  color: #0f172a;
}

.equipment-body {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
}

.equipment-info {
  display: flex;
  flex-direction: column;
}

.equipment-label {
  font-size: 11px;
  color: #64748b;
  margin-bottom: 2px;
}

.equipment-value {
  font-size: 13px;
  color: #334155;
}

.tab-pane {
  height: 100%;
}
</style> 
<template>
  <view class="container" :class="{ 'no-interaction': showUpdateDialog || showProgressDialog || isUpdating }">
    <!-- H5 环境下的绿色标题栏 -->
    <!-- #ifdef H5 -->
    <view class="custom-header">
      <view class="header-title">首页</view>
    </view>
    <!-- #endif -->

    <!-- 内容区域 -->
    <view :class="['content', { 'h5-container': isH5 }]">
      <!-- 功能区标题 -->
      <view class="section-title">
        <text>常用功能</text>
      </view>

      <!-- 功能卡片网格 -->
      <view class="feature-grid">
        <!-- 注释掉生产执行功能卡片 -->
        <!-- 
        <view class="feature-card" @click="navigateToProduction">
          <view class="feature-icon">
            <image src="/static/icons/production-active.png" mode="aspectFit" style="width: 32px; height: 32px;"></image>
          </view>
          <text class="feature-name">生产执行</text>
          <text class="feature-desc">生产管理</text>
        </view>
        -->
        
        <!-- 注释掉仓库管理功能卡片 -->
        <!-- 
        <view class="feature-card" @click="navigateToWarehouse">
          <view class="feature-icon">
            <image src="/static/icons/warehouse-active.png" mode="aspectFit" style="width: 32px; height: 32px;"></image>
          </view>
          <text class="feature-name">仓库管理</text>
          <text class="feature-desc">库存管理</text>
        </view>
        -->
        
        <view class="feature-card" @click="navigateToDeliver">
          <view class="feature-icon">
            <image src="/static/icons/deliver-active.png" mode="aspectFit" style="width: 32px; height: 32px;"></image>
          </view>
          <text class="feature-name">发货管理</text>
          <text class="feature-desc">处理发货</text>
        </view>
      </view>
    </view>

    <!-- 版本更新弹窗 -->
    <view v-if="showUpdateDialog" class="mask" @click.stop>
      <view class="update-dialog" @click.stop>
        <text class="update-title">发现新版本 {{ updateInfo.version }}</text>
        <text class="update-content">{{ updateInfo.remark || '发现新版本，请更新获得更好的体验' }}</text>
        <button class="update-btn" @click="handleUpdate">立即更新</button>
      </view>
    </view>

    <!-- 下载进度弹窗 -->
    <view v-if="showProgressDialog" class="mask" @click.stop>
      <view class="progress-dialog">
        <view class="progress-bar">
          <view class="progress-inner" :style="{ width: downloadProgress + '%' }"></view>
        </view>
        <text class="progress-text">已下载 {{ downloadProgress }}%</text>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue';
import { useUserStore } from '@/store/user';

// 初始化用户store
const userStore = useUserStore();

// 判断是否为H5环境
const isH5 = computed(() => {
  // #ifdef H5
  return true;
  // #endif

  // #ifndef H5
  return false;
  // #endif
});

// 发货页面导航
const navigateToDeliver = () => {
  try {
    uni.navigateTo({
      url: '/pages/deliver/deliverIndex'
    });
  } catch (error) {
    console.error('导航错误:', error);
    // 尝试备用路径
    uni.navigateTo({
      url: 'pages/deliver/deliverIndex'
    });
  }
};

// 生产页面导航
const navigateToProduction = () => {
  try {
    uni.navigateTo({
      url: '/pages/production/productionIndex'
    });
  } catch (error) {
    console.error('导航错误:', error);
    // 尝试备用路径
    uni.navigateTo({
      url: 'pages/production/productionIndex'
    });
  }
};

// 仓库页面导航
const navigateToWarehouse = () => {
  try {
    uni.navigateTo({
      url: '/pages/warehouse/warehouseIndex'
    });
  } catch (error) {
    console.error('导航错误:', error);
    // 尝试备用路径
    uni.navigateTo({
      url: 'pages/warehouse/warehouseIndex'
    });
  }
};

// 版本更新相关
const showUpdateDialog = ref(false)
const showProgressDialog = ref(false)
const downloadProgress = ref(0)
const isUpdating = ref(false)
const updateInfo = ref({
  version: '',
  downloadUrl: '',
  remark: ''
})

// 生命周期
onMounted(() => {
  // 仅在H5环境下使用setPageMeta
  // #ifdef H5
  try {
    // 禁用页面滚动
    uni.setPageMeta({
      pageStyle: {
        disableScroll: true
      }
    });
  } catch (error) {
    console.error('setPageMeta不被支持', error);
  }
  // #endif

  // 检查是否已登录
  checkLogin();
  
  // 监听版本更新事件
  uni.$on('checkAppUpdate', handleAppUpdateCheck);
});

// 检查登录状态
const checkLogin = () => {
  // 使用userStore的isLoggedIn getter
  const isLoggedIn = userStore.isLoggedIn;
  
  // console.log('首页检查登录状态:', isLoggedIn);
  
  if (!isLoggedIn) {
    // console.log('未检测到登录状态，准备跳转到登录页');
    
    // 添加延迟，确保store状态已更新
    setTimeout(() => {
      // 再次检查，防止状态更新延迟
      if (!userStore.isLoggedIn) {
        // 检查是否token过期
        const isExpired = userStore.isTokenExpired;
        
        if (isExpired) {
          console.log('Token已过期，需要重新登录');
        } else {
          console.log('未找到登录信息，需要登录');
        }
        
        uni.reLaunch({
          url: '/pages/login/login'
        });
      }
    }, 100);
  } else {
    // console.log('已检测到登录状态且token未过期，无需跳转');
  }
};

// 处理版本更新检查
const handleAppUpdateCheck = (data) => {
  console.log('收到版本更新检查事件:', data);
  updateInfo.value = data;
  showUpdateDialog.value = true;
};

// 处理更新操作
const handleUpdate = () => {
  // 标记正在更新
  isUpdating.value = true;
  
  // 隐藏更新对话框，显示进度对话框
  showUpdateDialog.value = false;
  showProgressDialog.value = true;
  
  // #ifdef APP-PLUS
  // 获取下载地址
  const downloadUrl = updateInfo.value.downloadUrl || '';
  if (!downloadUrl) {
    uni.showToast({
      title: '下载地址无效',
      icon: 'none'
    });
    showProgressDialog.value = false;
    isUpdating.value = false;
    return;
  }
  
  // 创建下载任务
  const downloadTask = uni.downloadFile({
    url: downloadUrl,
    success: (res) => {
      if (res.statusCode === 200) {
        // 下载成功，安装APK
        plus.runtime.install(
          res.tempFilePath, 
          {
            force: true
          },
          () => {
            console.log('安装成功');
            showProgressDialog.value = false;
            isUpdating.value = false;
          },
          (e) => {
            console.error('安装失败', e);
            uni.showToast({
              title: '安装失败，请重试',
              icon: 'none'
            });
            showProgressDialog.value = false;
            isUpdating.value = false;
          }
        );
      } else {
        uni.showToast({
          title: '下载失败，请重试',
          icon: 'none'
        });
        showProgressDialog.value = false;
        isUpdating.value = false;
      }
    },
    fail: (err) => {
      console.error('下载失败', err);
      uni.showToast({
        title: '下载失败，请检查网络',
        icon: 'none'
      });
      showProgressDialog.value = false;
      isUpdating.value = false;
    }
  });
  
  // 监听下载进度
  downloadTask.onProgressUpdate((res) => {
    downloadProgress.value = res.progress;
  });
  // #endif
  
  // #ifndef APP-PLUS
  // Web环境，模拟下载过程
  console.log('Web环境模拟下载过程');
  let progress = 0;
  const timer = setInterval(() => {
    progress += 2;
    downloadProgress.value = progress;
    
    if (progress >= 100) {
      clearInterval(timer);
      setTimeout(() => {
        showProgressDialog.value = false;
        isUpdating.value = false;
        uni.showToast({
          title: '更新完成',
          icon: 'success'
        });
        // Web环境下模拟更新完成后的操作
        setTimeout(() => {
          uni.showModal({
            title: '更新完成',
            content: '模拟环境下，版本已更新至 ' + updateInfo.value.version,
            showCancel: false,
            success: () => {
              // 刷新页面模拟重启应用
              location.reload();
            }
          });
        }, 1000);
      }, 500);
    }
  }, 100);
  // #endif
};
</script>

<style scoped>
page {
  height: 100vh;
  overflow: hidden;
  position: fixed;
  width: 100%;
}

/* 容器样式 */
.container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f9f5;
  position: relative;
  box-sizing: border-box;
  padding-bottom: 50px;
}

/* 仅在非H5环境下添加顶部内边距 */
/* #ifndef H5 */
.container {
  padding-top: var(--window-top);
}

/* #endif */

/* H5环境下的绿色标题栏样式 */
.custom-header {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 44px;
  background-color: #FFFFFF;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 999;
  box-shadow: 0 1px 5px rgba(0, 0, 0, 0.1);
}

/* #ifdef APP-PLUS */
.header,
.custom-header {
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.12) !important;
}

/* #endif */

.header-title {
  color: #333;
  font-size: 16px;
  font-weight: 500;
}

/* H5环境下的容器样式 */
.h5-container {
  padding-top: 44px;
}

/* 内容区域 */
.content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: auto;
  position: relative;
  box-sizing: border-box;
  justify-content: flex-start;
  align-items: stretch;
  padding: 15px;
}

/* 功能区标题 */
.section-title {
  margin: 10px 5px;
  padding-left: 5px;
  border-left: 3px solid #0c873d;
}

.section-title text {
  font-size: 15px;
  font-weight: 500;
  color: #333;
}

/* 功能卡片网格 */
.feature-grid {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  margin: 15px 0;
  padding: 0 5px;
  gap: 15px;
}

/* 居中显示的功能卡片网格 */
.feature-grid-centered {
  justify-content: center;
}

.feature-card {
  width: 45%;
  max-width: none;
  margin: 0;
  padding: 18px;
  border-radius: 10px;
  background-color: #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  display: flex;
  flex-direction: column;
  align-items: center;
  transition: all 0.3s ease;
}

.feature-card:active {
  transform: scale(0.98);
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
}

.feature-card.disabled {
  opacity: 0.6;
}

.feature-icon {
  width: 32px;
  height: 32px;
  margin-bottom: 8px;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 小程序环境下的自定义图标 */
.feature-icon-mp {
  margin-bottom: 12px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.circle-icon {
  width: 48px;
  height: 48px;
  border-radius: 24px;
  background-color: #0c873d;
  display: flex;
  justify-content: center;
  align-items: center;
  color: white;
}

.payment-icon {
  font-size: 24px;
  font-weight: bold;
  color: white;
  line-height: 48px;
  text-align: center;
}

.customer-icon {
  font-size: 20px;
  font-weight: bold;
  color: white;
  line-height: 48px;
  text-align: center;
}

.plus-icon {
  font-size: 32px;
  font-weight: bold;
  color: white;
  line-height: 48px;
  text-align: center;
}

.feature-name {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}

.feature-desc {
  font-size: 11px;
  color: #666;
  text-align: center;
}

/* 新增SVG图标样式 */
.svg-icon {
  margin-bottom: 12px;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 进度条样式 */
.progress-bar {
  width: 100%;
  height: 6px;
  background: #f0f0f0;
  border-radius: 3px;
  overflow: hidden;
  margin: 12px 0;
}

.progress-inner {
  height: 100%;
  background: #0c873d;
  border-radius: 3px;
  transition: width 0.3s ease-out;
}

/* 禁止交互样式 */
.no-interaction {
  pointer-events: none;
}

.no-interaction .content {
  opacity: 0.7;
}

/* 遮罩层样式 */
.mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  z-index: 999;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: fadeIn 0.3s ease-out;
  pointer-events: auto !important;
}

/* 更新弹窗样式 */
.update-dialog {
  width: 80%;
  max-width: 320px;
  padding: 24px;
  background: #fff;
  border-radius: 16px;
  text-align: center;
  animation: slideIn 0.3s ease-out;
  pointer-events: auto !important;
}

.update-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 16px;
  display: block;
}

.update-content {
  font-size: 14px;
  color: #666;
  margin-bottom: 24px;
  display: block;
  line-height: 1.6;
  padding: 0 12px;
  white-space: pre-line;
  text-align: left;
}

.update-btn {
  width: 80%;
  height: 44px;
  line-height: 44px;
  background: #0c873d;
  color: #fff;
  border-radius: 22px;
  font-size: 16px;
  margin: 0 auto;
  border: none;
  outline: none;
  pointer-events: auto !important;
}

.update-btn:active {
  opacity: 0.9;
}

/* 进度弹窗样式 */
.progress-dialog {
  width: 80%;
  max-width: 320px;
  padding: 24px;
  background: #fff;
  border-radius: 16px;
  text-align: center;
  animation: slideIn 0.3s ease-out;
  pointer-events: auto !important;
}

.progress-bar {
  width: 100%;
  height: 6px;
  background: #f0f0f0;
  border-radius: 3px;
  overflow: hidden;
  margin: 12px 0;
}

.progress-inner {
  height: 100%;
  background: #0c873d;
  border-radius: 3px;
  transition: width 0.3s ease-out;
}

.progress-text {
  font-size: 14px;
  color: #666;
  margin-top: 16px;
  display: block;
}

/* 底部导航栏样式 */
:deep(.uni-tabbar) {
  pointer-events: auto !important;
  opacity: 1 !important;
}

/* 弹窗动画 */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideIn {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}
</style>

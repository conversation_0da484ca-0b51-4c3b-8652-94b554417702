<template>
  <view class="tab-pane">
    <view class="info-card">
      <view class="card-body">
        <view v-if="materialInfo.length === 0" class="empty-tip">暂无追溯物料信息</view>
        <view v-else class="material-list">
          <view v-for="(item, index) in materialInfo" :key="index" class="material-item">
            <view class="material-header">
              <text class="material-title">{{ item.materialName || '未知物料' }}</text>
            </view>
            <view class="material-body">
              <view class="material-info">
                <text class="material-label">物料编号</text>
                <text class="material-value">{{ item.materialNo || '-' }}</text>
              </view>
              <view class="material-info">
                <text class="material-label">批次号</text>
                <text class="material-value">{{ item.batchNo || '-' }}</text>
              </view>
              <view class="material-info">
                <text class="material-label">使用数量</text>
                <text class="material-value">{{ item.quantity || '-' }}</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
// 定义props接收父组件传递的数据
const props = defineProps({
  materialInfo: {
    type: Array,
    default: () => []
  }
});
</script>

<style scoped>
/* 卡片样式 */
.info-card {
  background-color: #ffffff;
  border-radius: 10px;
  margin-bottom: 10px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.card-body {
  padding: 15px;
}

/* 空数据提示 */
.empty-tip {
  text-align: center;
  padding: 30px 0;
  color: #9ca3af;
  font-size: 14px;
}

/* 物料信息样式 */
.material-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.material-item {
  background-color: #f8fafc;
  border-radius: 8px;
  padding: 12px;
  border: 1px solid #e2e8f0;
}

.material-header {
  margin-bottom: 8px;
  padding-bottom: 6px;
  border-bottom: 1px solid #e2e8f0;
}

.material-title {
  font-size: 14px;
  font-weight: 600;
  color: #0f172a;
}

.material-body {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
}

.material-info {
  display: flex;
  flex-direction: column;
}

.material-label {
  font-size: 11px;
  color: #64748b;
  margin-bottom: 2px;
}

.material-value {
  font-size: 13px;
  color: #334155;
}

.tab-pane {
  height: 100%;
}
</style> 
import http from '@/utils/request';

/**
 * 获取发货订单列表
 * @param {Object} params 查询参数
 * @returns {Promise} 返回发货订单列表数据
 */
export function getOrderInfo(params = {}) {
  // 如果已经传入了Data参数(字符串形式)，则直接使用
  let dataStr = params.Data;
  
  // 如果没有传入Data参数，则构建默认的Data参数
  if (!dataStr) {
    // 构建Data参数
    const dataParams = {
      No: params.No || "",      // 发货单号
      Item: params.Item || "",  // 货品编码
      Name: params.Name || "",  // 客户名称
      MNo: params.MNo || "",    // 销售单号
      MName: params.MName || "", // 销售单客户
      Status: params.Status || "", // 状态
      BDate: params.BDate || "", // 开始日期
      EDate: params.EDate || "", // 结束日期
      A: params.keyword || "",  // 关键词搜索(发货单号/销售单号/客户简称)
      B: params.B || "",        // 预留字段
      C: params.C || "",        // 预留字段
      D: params.D || ""         // 预留字段
    };
    
    dataStr = JSON.stringify(dataParams);
  }
  
  // 构建基础参数
  const queryParams = {
    OP: 'GetOrderInfo',
    CFlag: params.CFlag || '238',  // 使用传入的CFlag参数，默认为238
    Data: dataStr,
    page: params.page || 1,
    limit: params.limit || 20
  };
  
  // 构建URL查询字符串
  const queryString = Object.entries(queryParams)
    .map(([key, value]) => `${key}=${encodeURIComponent(value)}`)
    .join('&');
  
  return http.get(`Service/OrderAjax.ashx?${queryString}`);
}

/**
 * 删除发货订单
 * @param {String} id 订单ID
 * @returns {Promise} 返回操作结果
 */
export function deleteOrder(id) {
  const Data = JSON.stringify({ DLID: id });
  return http.post("Service/OrderAjax.ashx?OP=DeleteOrder&CFlag=238", { Data });
}

/**
 * 同步发货单据信息
 * @param {String} orderNo 发货单号
 * @returns {Promise} 返回同步结果
 */
export function syncDispatchInfo(orderNo = '') {
  // 构建Data参数
  const dataParams = {
    No: orderNo,       // 发货单号
    Item: "",          // 货品编码
    Name: "",          // 客户名称
    MNo: "",           // 销售单号
    MName: "",         // 销售单客户
    A: "",             // 预留字段
    B: "",             // 预留字段
    C: "",             // 预留字段
    D: "",             // 预留字段
    Flag: "1"          // 同步标志
  };
  
  // 构建基础参数
  const queryParams = {
    OP: 'SynDispatchInfo',
    Data: JSON.stringify(dataParams)
  };
  
  // 构建URL查询字符串
  const queryString = Object.entries(queryParams)
    .map(([key, value]) => `${key}=${encodeURIComponent(value)}`)
    .join('&');
  
  return http.get(`Service/OrderAjax.ashx?${queryString}`);
}

/**
 * 获取发货单序列号详情
 * @param {String} orderNo 发货单号
 * @param {String} itemCode 物料编码
 * @param {Object} params 分页参数
 * @returns {Promise} 返回序列号详情数据
 */
export function getOrderSerialDetail(orderNo, itemCode, params = {}) {
  // 构建Data参数
  const dataParams = {
    No: orderNo || "",     // 发货单号
    Item: itemCode || "",  // 货品编码
    Name: "",              // 客户名称
    MNo: "",               // 销售单号
    MName: "",             // 销售单客户
    Status: "",            // 状态
    BDate: "",             // 开始日期
    EDate: "",             // 结束日期
    A: "",                 // 预留字段
    B: "",                 // 预留字段
    C: "",                 // 预留字段
    D: ""                  // 预留字段
  };
  
  // 构建基础参数
  const queryParams = {
    OP: 'GetOrderInfo',
    CFlag: params.CFlag,  // 使用传入的CFlag参数
    Data: JSON.stringify(dataParams),
    page: params.page || 1,
    limit: params.limit || 20
  };
  
  // 构建URL查询字符串
  const queryString = Object.entries(queryParams)
    .map(([key, value]) => `${key}=${encodeURIComponent(value)}`)
    .join('&');
  
  return http.get(`Service/OrderAjax.ashx?${queryString}`);
}

/**
 * 扫描条码处理接口
 * @param {Object} params 扫描相关参数
 * @returns {Promise} 返回处理结果
 */
export function deliverScanNoSerialNo(params = {}) {
  
  // 构建基础参数
  const queryParams = {
    OP: 'DeliverScanNoSerialNo',
    Data: JSON.stringify(params)
  };
  
  // 构建URL查询字符串
  const queryString = Object.entries(queryParams)
    .map(([key, value]) => `${key}=${encodeURIComponent(value)}`)
    .join('&');
  
  return http.get(`Service/OrderAjax.ashx?${queryString}`);
}

/**
 * 提交发货
 * @param {Object} params 发货相关参数
 * @returns {Promise} 返回处理结果
 */
export function submitPRDDeliver(params = {}) {
  // 构建Data参数
  const dataParams = {
    No: params.No || "",        // 保留字段
    Item: params.Item || "",    // 保留字段
    Name: params.Name || "",    // 储运条件
    A: params.A || "",          // 运单号
    B: params.B || "",          // DLID
    C: params.C || "",          // 发货单号
    D: params.D || "",          // 发货方式
    E: params.E || "",          // 发货人
    F: params.F || "",          // 发货日期
    Flag: params.Flag || "4"    // 标识
  };
  
  return http.post("Service/OrderAjax.ashx?OP=PRDDeliver", { 
    Data: JSON.stringify(dataParams) 
  });
}

/**
 * 删除发货单
 * @param {String} dlid 发货单ID
 * @param {String} dno 发货单号
 * @returns {Promise} 返回操作结果
 */
export function deleteDeliverOrder(dlid, dno) {
  // 构建Data参数，与web端保持一致
  const dataParams = {
    No: "",        // 空值
    Item: "",      // 空值
    Name: "",      // 空值
    MNo: "",       // 空值
    MName: "",     // 空值
    A: "",         // 空值
    B: dlid,       // 发货单ID
    C: dno,        // 发货单号
    D: "",         // 空值
    E: "",         // 空值
    F: "",         // 空值
    Flag: "2"      // 标识为删除操作
  };
  
  return http.post("Service/OrderAjax.ashx?OP=PRDDeliver", { 
    Data: JSON.stringify(dataParams) 
  });
}

/**
 * 删除序列号
 * @param {Object} params 序列号相关参数
 * @returns {Promise} 返回操作结果
 */
export function deleteSerialNo(params = {}) {
  // 构建Data参数，与web端保持一致
  const dataParams = {
    No: params.No || "",       // 序列号BatchSN
    Item: "",                  // 空值
    Name: "",                  // 空值
    MNo: params.MNo || "",     // 物料编码MaterNo
    MName: "",                 // 空值
    A: params.A || "",         // 数量Qty
    B: params.B || "",         // DLID
    C: params.C || "",         // 发货单号DNo
    D: params.D || "",         // 单据类型BillType
    E: "",                     // 空值
    F: "",                     // 空值
    Flag: "3"                  // 标识为删除序列号操作
  };
  
  return http.post("Service/OrderAjax.ashx?OP=PRDDeliver", { 
    Data: JSON.stringify(dataParams) 
  });
}

/**
 * 打印发货单
 * @param {String} dlid 发货单ID
 * @param {String} dno 发货单号
 * @returns {Promise} 返回打印结果
 */
export function printDeliverOrder(dlid, dno) {
  // 构建Data参数，与web端保持一致
  const dataParams = {
    No: "",        // 空值
    Item: "",      // 空值
    Name: "",      // 空值
    MNo: "",       // 空值
    MName: "",     // 空值
    A: "",         // 空值
    B: dlid,       // 发货单ID
    C: dno,        // 发货单号
    D: "",         // 空值
    E: "",         // 空值
    F: "",         // 空值
    Flag: "5"      // 标识为打印操作
  };
  
  return http.post("Service/OrderAjax.ashx?OP=PRDDeliver", { 
    Data: JSON.stringify(dataParams) 
  });
}

/**
 * 获取发货单打印信息
 * @param {String} deliverNo 发货单号
 * @param {String} boxQuantity 单箱数量
 * @param {String} boxNo 箱号
 * @returns {Promise} 返回打印信息数据
 */
export function getDeliverPrintInfo(deliverNo, boxQuantity, boxNo = '') {
  // 构建查询参数
  const queryParams = {
    OP: 'GetDeliverPrintInfo',
    CFlag: '2',  // 修改为1
    CNO: deliverNo || '',
    Item: boxQuantity || '10',  // 单箱数量，默认10
    CPNO: boxNo || ''  // 添加箱号参数
  };
  
  // 构建URL查询字符串
  const queryString = Object.entries(queryParams)
    .map(([key, value]) => `${key}=${encodeURIComponent(value)}`)
    .join('&');
  
  return http.get(`Service/OrderAjax.ashx?${queryString}`);
} 
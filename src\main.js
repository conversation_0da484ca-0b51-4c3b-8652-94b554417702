import {
	createSSRApp
} from "vue";
import App from "./App.vue";
import http from './utils/request'
import pinia from './store'

// 导入uni-ui组件
import uniNavBar from '@dcloudio/uni-ui/lib/uni-nav-bar/uni-nav-bar.vue'
import uniIcons from '@dcloudio/uni-ui/lib/uni-icons/uni-icons.vue'

// 清除可能损坏的本地存储数据
try {
	const userInfo = uni.getStorageSync('userInfo')
	if (userInfo && typeof userInfo === 'object' && !(userInfo instanceof Array)) {
		// 如果存储的是对象而非字符串，重新序列化并保存
		uni.setStorageSync('userInfo', JSON.stringify(userInfo))
		console.log('已修复本地存储的用户信息格式')
	}
} catch (e) {
	console.error('清理本地存储失败:', e)
	// 如果出错，直接移除
	uni.removeStorageSync('userInfo')
}

export function createApp() {
	const app = createSSRApp(App);
	
	// 挂载请求工具到全局
	app.config.globalProperties.$http = http
	
	// 使用pinia
	app.use(pinia)
	
	// 注册全局组件
	app.component('uni-nav-bar', uniNavBar)
	app.component('uni-icons', uniIcons)
	
	// 初始化HTTP请求
	const setupHttp = () => {
		// 在这里可以进行额外的HTTP配置
		uni.$u = uni.$u || {}
		uni.$u.http = http
	}
	
	// 设置HTTP
	setupHttp()
	
	return {
		app,
	};
}

<template>
  <view class="login-container">
    <!-- 状态栏占位 -->
    <view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>
    
    <!-- 背景装饰 -->
    <view class="bg-pattern"></view>
    <view class="bg-shape shape-1"></view>
    <view class="bg-shape shape-2"></view>
    
    <!-- 内容区域 -->
    <view class="login-content">
      <!-- 顶部Logo -->
      <view class="logo-container">
        <view class="logo-circle">
          <text class="logo-icon">!</text>
        </view>
      </view>
      
      <!-- 欢迎标题 -->
      <view class="welcome-text">
        <text class="title">欢迎回来</text>
        <text class="subtitle">登录您的账户</text>
      </view>
      
      <!-- 登录表单 -->
      <view class="login-form">
        <!-- 用户名输入框 -->
        <view class="form-group">
          <text class="form-label">账号</text>
          <view class="form-item">
            <image class="form-icon-img" src="@/static/icons/account.png" mode="aspectFit"></image>
            <input 
              class="input" 
              type="text" 
              v-model="loginForm.account" 
              placeholder="输入用户名"
            />
          </view>
        </view>
        
        <!-- 密码输入框 -->
        <view class="form-group">
          <text class="form-label">密码</text>
          <view class="form-item">
            <image class="form-icon-img" src="@/static/icons/password.png" mode="aspectFit"></image>
            <!-- 使用微信小程序特有的password属性来控制密码显示/隐藏 -->
            <input 
              class="input" 
              type="text" 
              :password="!showPassword"
              v-model="loginForm.passWord" 
              placeholder="输入密码"
              id="passwordInput"
            />
            <view class="password-toggle" @mousedown.prevent @click.stop="togglePasswordVisibility">
              <image 
                v-if="showPassword"
                class="eye-img" 
                src="@/static/icons/eye-open.png" 
                mode="aspectFit"
              ></image>
              <image 
                v-else
                class="eye-img" 
                src="@/static/icons/eye-close.png" 
                mode="aspectFit"
              ></image>
            </view>
          </view>
        </view>
        
        <!-- 登录按钮 -->
        <button 
          class="login-btn" 
          :class="{ 'btn-loading': loading }"
          @click="handleLogin"
        >
          {{ loading ? '登录中...' : '登录' }}
        </button>
      </view>
      
      <!-- 版本信息 -->
      <view class="app-version">
        <text>睿械通系统</text>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, reactive, nextTick, onMounted } from 'vue'
import { login as queryLogin } from '@/api/login'
import { queryVersions } from '@/api/versions'
import { baseURL } from '@/utils/request'
import { useUserStore } from '@/store/user'

// 获取用户store
const userStore = useUserStore()

// 设置页面属性
// #ifdef APP-PLUS
plus.screen.lockOrientation('portrait-primary'); // 锁定竖屏
// #endif

// 登录表单数据
const loginForm = reactive({
  account: '',
  passWord: ''
})

// 加载状态
const loading = ref(false)

// 密码显示状态
const showPassword = ref(false)

// 密码输入框引用
const passwordInput = ref(null)

// 状态栏高度
const statusBarHeight = ref(0)

// 在组件挂载时获取状态栏高度
onMounted(() => {
  // 获取系统信息
  uni.getSystemInfo({
    success: (res) => {
      statusBarHeight.value = res.statusBarHeight || 20;
    }
  });
})

// 切换密码显示/隐藏
const togglePasswordVisibility = (e) => {
  if (e) e.preventDefault()
  showPassword.value = !showPassword.value
  
  // 在下一个tick中尝试重新聚焦
  nextTick(() => {
    // 使用uni-app的方式聚焦
    uni.createSelectorQuery()
      .select('#passwordInput')
      .fields({
        context: true,
        size: true,
      })
      .exec(res => {
        if (res && res[0]) {
          // 有些平台可能不支持focus方法，所以要做检查
          try {
            res[0].node && res[0].node.focus && res[0].node.focus()
          } catch (err) {
            console.log('focus error:', err)
          }
        }
      })
  })
  
  return false
}

// 检查应用版本
const checkAppVersion = async () => {
  try {
    console.log('检查应用版本...')
    // 获取当前应用版本
    const currentVersion = plus.runtime.version
    
    // 查询最新版本信息
    const params = {
      VersionsType: 'App' // 指定查询App类型版本
    }
    
    const res = await queryVersions(params)
    
    if (res && res.Data && res.Data.length > 0) {
      // 假设接口返回的数据是按版本号降序排列的，取第一个为最新版本
      const latestVersion = res.Data[0]
      
      // 获取版本号，确保字段名匹配
      let serverVersion = latestVersion.VersionsNum || latestVersion.Versions || ''
      // 移除可能的'V'前缀
      if (serverVersion.startsWith('V') || serverVersion.startsWith('v')) {
        serverVersion = serverVersion.substring(1)
      }
      
      console.log('当前版本:', currentVersion)
      console.log('最新版本:', serverVersion)
      
      // 比较版本号
      if (compareVersion(serverVersion, currentVersion) > 0) {
        // 有新版本，先跳转到首页
        uni.switchTab({
          url: '/pages/index/index',
          success: () => {
            // 然后通过全局事件触发版本更新检查，这与App.vue的实现保持一致
            uni.$emit('checkAppUpdate', {
              version: serverVersion,
              downloadUrl: baseURL.downloadPath,
              remark: latestVersion.Remark || '发现新版本，请更新获得更好的体验'
            })
          }
        });
      } else {
        // 当前已是最新版本，直接进入首页
        goToHomePage()
      }
    } else {
      // 无法获取版本信息，直接进入首页
      console.log('无法获取版本信息')
      goToHomePage()
    }
  } catch (error) {
    console.error('检查版本出错:', error)
    // 出错时也继续登录流程
    goToHomePage()
  }
}

// 版本号比较函数
const compareVersion = (v1, v2) => {
  // 确保版本号是字符串
  v1 = String(v1)
  v2 = String(v2)
  
  // #ifdef APP-PLUS
  // 在调试基座中运行时跳过版本检查
  if (plus.runtime.isCustomLaunch) {
    return 0 // 在调试基座中返回版本相等，避免提示更新
  }
  // #endif
  
  // 移除H5环境下强制返回更新的逻辑，改为使用真实的版本比较
  // #ifdef H5
  console.log('H5环境下正常比较版本号:', v1, v2)
  // 如果需要在测试环境强制显示更新弹框，可以取消下面这行注释
  // return 1
  // #endif
  
  // 处理数字型版本号 (如14.57)
  if (/^\d+\.\d+$/.test(v2) && parseFloat(v2) > 10) {
    return 0 // 不提示更新
  }
  
  const v1Parts = v1.split('.').map(Number)
  const v2Parts = v2.split('.').map(Number)
  
  for (let i = 0; i < Math.max(v1Parts.length, v2Parts.length); i++) {
    const v1Part = v1Parts[i] || 0
    const v2Part = v2Parts[i] || 0
    
    if (v1Part > v2Part) {
      return 1
    } else if (v1Part < v2Part) {
      return -1
    }
  }
  
  return 0
}

// 跳转到首页
const goToHomePage = () => {
  uni.switchTab({
    url: '/pages/index/index'
  });
}

// 处理登录
const handleLogin = async () => {
  // 表单验证
  if (!loginForm.account.trim()) {
    uni.showToast({
      title: '请输入账号',
      icon: 'none'
    })
    return
  }
  if (!loginForm.passWord.trim()) {
    uni.showToast({
      title: '请输入密码',
      icon: 'none'
    })
    return
  }
  
  // 设置加载状态
  loading.value = true
  
  try {
    // 构建登录参数
    const loginParams = {
      User: loginForm.account,
      Pwd: loginForm.passWord
    }
    
    // 使用pinia store进行登录
    const loginResult = await userStore.login(loginParams)
    
    if (loginResult.success) {
      // 登录成功
      uni.showToast({
        title: '登录成功',
        icon: 'success',
        duration: 1500
      })
      
      console.log('登录成功，开始检查版本...');
      
      // 延迟一段时间确保状态更新和提示显示完成
      setTimeout(() => {
        // 获取App实例
        const appInstance = getApp();
        if (appInstance && typeof appInstance.resetCounters === 'function' && typeof appInstance.checkVersionAfterLogin === 'function') {
          // 重置计数器并执行版本检查
          appInstance.resetCounters();
          // 要确保首页已加载，否则可能无法显示更新弹框
          uni.switchTab({
            url: '/pages/index/index',
            success: () => {
              // 等待首页组件挂载完成后再执行版本检查
              setTimeout(() => {
                appInstance.checkVersionAfterLogin();
                console.log('已调用App版本检查方法');
              }, 500);
            }
          });
        } else {
          // 回退到原来的版本检查逻辑
          // #ifdef APP-PLUS
          // 检查应用版本
          checkAppVersion();
          // #endif
          
          // #ifndef APP-PLUS
          // 非APP环境直接跳转首页
          goToHomePage();
          // #endif
        }
      }, 300);
    } else {
      // 登录失败
      uni.showToast({
        title: loginResult.message || '登录失败',
        icon: 'none',
        duration: 2000
      })
    }
  } catch (error) {
    // 处理网络错误等异常
    console.error('登录失败:', error)
    uni.showToast({
      title: '登录失败，请检查网络连接',
      icon: 'none',
      duration: 2000
    })
  } finally {
    loading.value = false
  }
}
</script>

<style>
/* 登录页容器 */
.login-container {
  min-height: 100vh;
  padding: 0 32px;
  padding-top: 0; /* 移除顶部内边距，改用状态栏占位元素 */
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
  background: linear-gradient(to bottom, #f8f9fa, #ffffff);
  display: flex;
  flex-direction: column;
  position: relative;
  overflow: hidden;
  box-sizing: border-box;
}

/* 状态栏占位 */
.status-bar {
  width: 100%;
  position: relative;
  z-index: 10;
}

/* 背景装饰 */
.bg-pattern {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: radial-gradient(rgba(12, 135, 61, 0.07) 2px, transparent 2px);
  background-size: 30px 30px;
  opacity: 0.5;
  z-index: 0;
}

.bg-shape {
  position: absolute;
  border-radius: 50%;
  z-index: 0;
}

.shape-1 {
  top: -120px;
  right: -80px;
  width: 300px;
  height: 300px;
  background: linear-gradient(135deg, rgba(12, 135, 61, 0.15), rgba(16, 160, 88, 0.05));
}

.shape-2 {
  bottom: -150px;
  left: -100px;
  width: 350px;
  height: 350px;
  background: linear-gradient(135deg, rgba(12, 135, 61, 0.08), rgba(16, 160, 88, 0.03));
}

/* 内容区域 */
.login-content {
  position: relative;
  z-index: 1;
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 40px 0;
}

/* Logo区域 */
.logo-container {
  display: flex;
  justify-content: center;
  margin-bottom: 40px;
}

.logo-circle {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, #0c873d, #10a058);
  border-radius: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 12px 24px rgba(12, 135, 61, 0.2);
  position: relative;
  overflow: hidden;
  transform: rotate(10deg);
  transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.logo-circle:hover {
  transform: rotate(0deg) scale(1.05);
  box-shadow: 0 15px 30px rgba(12, 135, 61, 0.25);
}

.logo-circle:after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, 
    rgba(255, 255, 255, 0.15) 0%, 
    rgba(255, 255, 255, 0) 50%);
}

.logo-icon {
  font-size: 44px;
  font-weight: bold;
  color: white;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.15);
}

/* 欢迎文本 */
.welcome-text {
  text-align: center;
  margin-bottom: 50px;
}

.title {
  font-size: 32px;
  font-weight: 700;
  color: #222;
  margin-bottom: 16px;
  display: block;
  letter-spacing: -0.5px;
}

.subtitle {
  font-size: 16px;
  color: #666;
  display: block;
  line-height: 1.6;
  max-width: 280px;
  margin: 0 auto;
}

/* 登录表单 */
.login-form {
  display: flex;
  flex-direction: column;
  margin-bottom: 30px;
}

.form-group {
  margin-bottom: 24px;
}

.form-label {
  display: block;
  font-size: 14px;
  font-weight: 600;
  color: #444;
  margin-bottom: 10px;
  margin-left: 4px;
}

.form-item {
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 12px;
  padding: 4px 16px;
  position: relative;
  display: flex;
  align-items: center;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.03);
  transition: all 0.3s ease;
  border: 1px solid rgba(0, 0, 0, 0.05);
  backdrop-filter: blur(10px);
}

.form-item:focus-within {
  transform: translateY(-2px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.05);
  border-color: rgba(12, 135, 61, 0.3);
  background-color: rgba(255, 255, 255, 0.95);
}

.form-icon-img {
  width: 20px;
  height: 20px;
  margin-right: 12px;
  opacity: 0.6;
  transition: all 0.3s ease;
}

.form-item:focus-within .form-icon-img {
  opacity: 1;
}

.input {
  flex: 1;
  height: 48px;
  font-size: 16px;
  padding: 12px 0;
  background-color: transparent;
  border: none;
  color: #333;
  outline: none;
}

.input::placeholder {
  color: #aaa;
  font-size: 15px;
  transition: color 0.3s;
}

.input:focus::placeholder {
  color: #bbb;
}

.password-toggle {
  padding: 6px;
  cursor: pointer;
  z-index: 2;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  user-select: none;
  -webkit-user-select: none;
  transition: all 0.2s;
  opacity: 0.8;
}

.password-toggle:hover {
  opacity: 1;
}

.eye-img {
  width: 20px;
  height: 20px;
  opacity: 0.6;
  transition: all 0.3s;
}

.password-toggle:hover .eye-img {
  opacity: 1;
}

.form-item:focus-within .password-toggle .eye-img {
  opacity: 0.8;
}

/* 登录按钮 */
.login-btn {
  height: 56px;
  border-radius: 16px;
  background: linear-gradient(135deg, #0c873d, #10a058);
  color: white;
  font-size: 17px;
  font-weight: 600;
  margin-top: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  width: 100%;
  padding: 0;
  box-shadow: 0 10px 20px rgba(12, 135, 61, 0.2);
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  position: relative;
  overflow: hidden;
}

.login-btn:after {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, 
    rgba(255, 255, 255, 0) 0%, 
    rgba(255, 255, 255, 0.2) 50%, 
    rgba(255, 255, 255, 0) 100%);
  animation: shine 4s infinite;
}

.login-btn:active {
  transform: translateY(3px) scale(0.98);
  box-shadow: 0 5px 10px rgba(12, 135, 61, 0.15);
}

.btn-text {
  margin-right: 12px;
}

.btn-icon {
  width: 24px;
  height: 24px;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.arrow-icon {
  width: 10px;
  height: 10px;
  border-top: 2px solid white;
  border-right: 2px solid white;
  transform: rotate(45deg);
}

.btn-loading {
  opacity: 0.9;
}

@keyframes shine {
  0% { left: -100%; }
  15% { left: 100%; }
  100% { left: 100%; }
}

/* 版本信息 */
.app-version {
  margin-top: auto;
  text-align: center;
  padding: 20px 0;
}

.app-version text {
  font-size: 12px;
  color: #999;
}

/* 响应式调整 */
@media screen and (max-height: 700px) {
  .login-content {
    padding: 30px 0;
  }
  
  .logo-container {
    margin-bottom: 30px;
  }
  
  .logo-circle {
    width: 70px;
    height: 70px;
    border-radius: 18px;
  }
  
  .logo-icon {
    font-size: 36px;
  }
  
  .welcome-text {
    margin-bottom: 35px;
  }
  
  .title {
    font-size: 28px;
    margin-bottom: 12px;
  }
  
  .subtitle {
    font-size: 15px;
  }
  
  .form-group {
    margin-bottom: 20px;
  }
  
  .input {
    height: 42px;
  }
  
  .login-btn {
    height: 50px;
    margin-top: 30px;
  }
}

/* 删除旧的眼睛样式 */
.eye-outer,
.eye-inner,
.eye-line,
.eye-open .eye-outer,
.eye-open .eye-inner,
.eye-open .eye-outer::before,
.eye-open .eye-outer::after,
.eye-open .eye-inner::after,
.form-item:focus-within .eye-outer,
.form-item:focus-within .eye-outer::before,
.form-item:focus-within .eye-outer::after,
.form-item:focus-within .eye-inner,
.form-item:focus-within .eye-inner::after,
.form-item:focus-within .eye-line {
  display: none;
}
</style> 
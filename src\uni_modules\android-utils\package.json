{"id": "android-utils", "displayName": "语音合成 文字转语音 tts ", "version": "1.0.5", "description": "安卓原生语音合成.基础工具 语音状态监听， 原生吐司 对话框 启用 禁用截图录屏等 uniapp无需自定义基座，uniappx小米手机需要自定义基座 ", "keywords": ["语音合成", "，toast，", "吐司", "，原生弹窗"], "repository": "", "engines": {"HBuilderX": "^3.96"}, "dcloudext": {"type": "uts", "sale": {"regular": {"price": "0.00"}, "sourcecode": {"price": "0.00"}}, "contact": {"qq": ""}, "declaration": {"ads": "无", "data": "无", "permissions": "<uses-permission android:name=\"android.permission.ACCESSIBILITY_SERVICE\"/>"}, "npmurl": ""}, "uni_modules": {"dependencies": [], "encrypt": [], "platforms": {"cloud": {"tcb": "y", "aliyun": "y", "alipay": "y"}, "client": {"Vue": {"vue2": "y", "vue3": "y"}, "App": {"app-android": {"minVersion": "19"}, "app-ios": "u"}, "H5-mobile": {"Safari": "u", "Android Browser": "u", "微信浏览器(Android)": "u", "QQ浏览器(Android)": "u"}, "H5-pc": {"Chrome": "u", "IE": "u", "Edge": "u", "Firefox": "u", "Safari": "u"}, "小程序": {"微信": "u", "阿里": "u", "百度": "u", "字节跳动": "u", "QQ": "u", "钉钉": "u", "快手": "u", "飞书": "u", "京东": "u"}, "快应用": {"华为": "u", "联盟": "u"}}}}}
<template>
  <view class="container">
    <!-- 使用uni-nav-bar组件 -->
    <view class="nav-bar-container">
      <uni-nav-bar
        :fixed="true"
        :status-bar="true"
        title="发货单据详情"
        left-icon="left"
        @clickLeft="goBack"
        :border="false"
      />
    </view>
    
    <!-- 主体内容区 -->
    <view class="main-content">
      <!-- 订单状态显示 -->
      <view class="order-status">
        <view class="status-tag" :class="getStatusClass(orderDetail.Status)">{{ orderDetail.Status || '未知状态' }}</view>
        <text class="order-number">单号: {{ orderDetail.DNo || '-' }}</text>
      </view>
      
      <!-- Tab栏 -->
      <view class="tabs">
        <view 
          v-for="(tab, index) in tabs" 
          :key="index" 
          class="tab-item" 
          :class="{ active: currentTab === index }"
          @click="switchTab(index)"
        >
          <text>{{ tab.name }}</text>
        </view>
      </view>
      
      <!-- Tab内容区 -->
      <scroll-view scroll-y class="tab-content">
        <!-- 基本信息Tab -->
        <view v-if="currentTab === 0" class="tab-pane">
          <!-- 基本信息卡片 -->
          <view class="detail-card">
            <view class="info-grid">
              <view class="info-item">
                <text class="info-label">发货单号</text>
                <text class="info-value">{{ orderDetail.DNo || '-' }}</text>
              </view>
              <view class="info-item">
                <text class="info-label">单据类型</text>
                <text class="info-value" :class="orderDetail.BillType === 'FH' ? 'bill-type-fh' : 'bill-type-th'">{{ orderDetail.BillType === 'FH' ? '发货单' : '退货单' }}</text>
              </view>
              <view class="info-item">
                <text class="info-label">创建日期</text>
                <text class="info-value">{{ orderDetail.DDate2 || '-' }}</text>
              </view>
              <view class="info-item">
                <text class="info-label">业务类型</text>
                <text class="info-value">{{ orderDetail.Dtype || '-' }}</text>
              </view>
              <view class="info-item">
                <text class="info-label">销售订单号</text>
                <text class="info-value">{{ orderDetail.SaleNo || '-' }}</text>
              </view>
              <view class="info-item">
                <text class="info-label">销售类型</text>
                <text class="info-value">{{ orderDetail.SaleType || '-' }}</text>
              </view>
              <view class="info-item">
                <text class="info-label">销售部门</text>
                <text class="info-value">{{ orderDetail.SaleDept || '-' }}</text>
              </view>
              <view class="info-item">
                <text class="info-label">客户编号</text>
                <text class="info-value">{{ orderDetail.CustNo || '-' }}</text>
              </view>
              <view class="info-item">
                <text class="info-label">客户名称</text>
                <text class="info-value">{{ orderDetail.CustName || '-' }}</text>
              </view>
              <view class="info-item">
                <text class="info-label">发运方式</text>
                <text class="info-value">{{ orderDetail.ShipVia || '-' }}</text>
              </view>
              <view class="info-item">
                <text class="info-label">运单号</text>
                <text class="info-value">{{ orderDetail.TrackingNo || '-' }}</text>
              </view>
              <view class="info-item">
                <text class="info-label">合同编号</text>
                <text class="info-value">{{ orderDetail.ContractNo || '-' }}</text>
              </view>
              <view class="info-item">
                <text class="info-label">发货人</text>
                <text class="info-value">{{ orderDetail.DInMan || '-' }}</text>
              </view>
              <view class="info-item">
                <text class="info-label">储运条件</text>
                <text class="info-value">{{ orderDetail.StorageConditions || '-' }}</text>
              </view>
              <view class="info-item">
                <text class="info-label">发货日期</text>
                <text class="info-value">{{ orderDetail.ShipDate2 || '-' }}</text>
              </view>
            </view>
          </view>
          
          <!-- 收货信息 -->
          <view class="detail-card">
            <view class="info-list">
              <view class="info-row">
                <text class="info-label">终端医院</text>
                <text class="info-value">{{ orderDetail.Hospital || '-' }}</text>
              </view>
              <view class="info-row">
                <text class="info-label">收货地址</text>
                <text class="info-value">{{ orderDetail.Addr || '-' }}</text>
              </view>
              <view class="info-row">
                <text class="info-label">收货人</text>
                <text class="info-value">{{ orderDetail.Consignee || '-' }}</text>
              </view>
              <view class="info-row">
                <text class="info-label">联系电话</text>
                <text class="info-value">{{ orderDetail.Phone || '-' }}</text>
              </view>
            </view>
          </view>
          
          <!-- 其他信息 -->
          <view class="detail-card">
            <view class="info-list">
              <view class="info-row">
                <text class="info-label">生产许可证</text>
                <text class="info-value">{{ orderDetail.PrdXKZ || '-' }}</text>
              </view>
              <view class="info-row">
                <text class="info-label">状态</text>
                <text class="info-value">{{ orderDetail.Status || '-' }}</text>
              </view>
              <view class="info-row">
                <text class="info-label">录入人</text>
                <text class="info-value">{{ orderDetail.InMan || '-' }}</text>
              </view>
              <view class="info-row">
                <text class="info-label">录入时间</text>
                <text class="info-value">{{ orderDetail.InDate2 || orderDetail.InDate || '-' }}</text>
              </view>
              <view class="info-row">
                <text class="info-label">备注</text>
                <text class="info-value">{{ orderDetail.Remark || '-' }}</text>
              </view>
            </view>
          </view>
        </view>
        
        <!-- 序列号详情Tab -->
        <view v-if="currentTab === 1" class="tab-pane">
          <view class="serial-card">
            <view class="card-header">
              <text class="card-title">序列号列表</text>
              <text class="item-count" v-if="serialList.length > 0">共 {{ totalCount }} 条</text>
            </view>
            
            <view class="card-body">
              <view v-if="loading" class="loading-box">
                <text>加载中...</text>
              </view>
              
              <view v-else-if="serialList.length === 0" class="empty-list">暂无序列号数据</view>
              
              <view v-else class="serial-list">
                <view v-for="(item, index) in serialList" :key="index" class="serial-item">
                  <view class="serial-item-header">
                    <text class="serial-title">{{ item.MaterName || '未知产品' }}</text>
                    <text class="serial-code">{{ item.MaterNo || '-' }}</text>
                  </view>
                  <view class="serial-body">
                    <view class="serial-info">
                      <text class="serial-label">序列号</text>
                      <text class="serial-value">{{ item.BatchSN || '-' }}</text>
                    </view>
                    <view class="serial-info">
                      <text class="serial-label">数量</text>
                      <text class="serial-value">{{ item.Qty || '1' }}</text>
                    </view>
                    <view class="serial-info">
                      <text class="serial-label">箱号</text>
                      <text class="serial-value">{{ item.BoxNo || '-' }}</text>
                    </view>
                    <view class="serial-info">
                      <text class="serial-label">生产日期</text>
                      <text class="serial-value">{{ item.PrdDate || '-' }}</text>
                    </view>
                    <view class="serial-info">
                      <text class="serial-label">生产批号</text>
                      <text class="serial-value">{{ item.PrdBatch || '-' }}</text>
                    </view>
                    <view class="serial-info">
                      <text class="serial-label">使用期限</text>
                      <text class="serial-value">{{ item.EfftDate || '-' }}</text>
                    </view>
                  </view>
                </view>
                
                <!-- 加载更多 -->
                <view v-if="hasMore" class="load-more" @click="loadMore">
                  <text>加载更多</text>
                </view>
              </view>
            </view>
          </view>
        </view>
        
        <!-- 发货信息Tab -->
        <view v-if="currentTab === 2" class="tab-pane">
            <view class="card-header">
              <text class="card-title">发货列表</text>
              <text class="item-count" v-if="shippingInfoList.length > 0">共 {{ shippingInfoList.length }} 条</text>
            </view>
          
            <view v-if="shippingInfoLoading" class="loading-box">
              <text>加载中...</text>
            </view>
            
          <view v-else-if="shippingInfoList.length === 0" class="empty-list">
              <text>暂无发货信息数据</text>
            </view>
            
          <view v-else class="serial-list">
            <view v-for="(item, index) in shippingInfoList" :key="index" class="serial-item">
              <view class="serial-item-header">
                <text class="serial-title">{{ item.MaterName || '未知产品' }}</text>
                <text class="serial-code">{{ item.MaterNo || '-' }}</text>
                    </view>
              <view class="serial-body">
                <view class="serial-info">
                  <text class="serial-label">部件名称</text>
                  <text class="serial-value">{{ item.PartMame || '-' }}</text>
                </view>
                <view class="serial-info">
                  <text class="serial-label">规格型号</text>
                  <text class="serial-value">{{ item.Spec || '-' }}</text>
                    </view>
                <view class="serial-info">
                  <text class="serial-label">需发数量</text>
                  <text class="serial-value">{{ item.ReqQty || '-' }}{{ item.Unit ? '（' + item.Unit + '）' : '' }}</text>
                    </view>
                <view class="serial-info">
                  <text class="serial-label">实发数量</text>
                  <text class="serial-value">{{ item.RealQty || '0' }}</text>
                    </view>
                <view class="serial-info">
                  <text class="serial-label">状态</text>
                  <text class="serial-value">{{ item.Status || '-' }}</text>
                </view>
              </view>
            </view>
          </view>
        </view>
      </scroll-view>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, onMounted, reactive } from 'vue';
import { getOrderSerialDetail, getOrderInfo } from '@/api/deliver';
// 导入uni-nav-bar组件
import uniNavBar from '@dcloudio/uni-ui/lib/uni-nav-bar/uni-nav-bar.vue'

// 判断是否为H5环境
const isH5 = computed(() => {
  // #ifdef H5
  return true;
  // #endif
  
  // #ifndef H5
  return false;
  // #endif
});

// Tab栏配置
const tabs = [
  { name: '基本信息' },
  { name: '序列号详情' },
  { name: '发货信息' }
];
const currentTab = ref(0);

// 切换Tab
const switchTab = (index) => {
  currentTab.value = index;
  
  // 如果切换到序列号详情tab，每次都重新获取数据
  if (index === 1) {
    // 每次切换到序列号详情tab时，都重新获取序列号数据
    serialList.value = [];
    currentPage.value = 1;
    // 直接调用获取序列号的方法，不预先设置loading状态
    fetchSerialDetail();
  }
  
  // 如果切换到发货信息tab，每次都重新加载数据
  if (index === 2) {
    fetchShippingInfo();
  }
};

// 返回上一页
const goBack = () => {
  // 使用navigateBack返回上一页
  uni.navigateBack({
    delta: 1,
    fail: () => {
      // 如果返回失败，则重定向到列表页
      uni.redirectTo({
        url: './deliverIndex'
      });
    }
  });
};

// 订单详情数据
const orderDetail = ref({});
const orderNo = ref('');
const itemCode = ref('');

// 序列号列表数据
const serialList = ref([]);
const loading = ref(false);
const currentPage = ref(1);
const pageSize = 20;
const totalCount = ref(0);
const hasMore = computed(() => {
  return serialList.value.length < totalCount.value;
});

// 发货信息数据
const shippingInfo = ref({});
const shippingInfoLoading = ref(false);
const shippingInfoLoaded = ref(false);
const shippingInfoList = ref([]);

// 处理序列号数据
const processSerialData = (data) => {
  if (!data || data.length === 0) return [];
  
  return data.map(item => {
    // 标准化字段名称，兼容不同API返回格式，与executeDeliver.vue保持一致
    return {
      MaterName: item.MaterName || item.ItemName || item.Name || '',
      MaterNo: item.MaterNo || item.ItemCode || item.Item || '',
      BatchSN: item.BatchSN || item.SerialNo || item.Serial || '',
      Qty: item.Qty || '1',
      BoxNo: item.BoxNo || '',
      PrdDate: item.PrdDate || item.MDate || '',
      PrdBatch: item.PrdBatch || item.BatchNo || '',
      EfftDate: item.EfftDate || item.ExpDate || '',
      // 保留原始数据
      original: item
    };
  });
};

// 获取序列号详情
const fetchSerialDetail = async (page = 1, isLoadMore = false) => {
  // 防止重复请求，如果正在加载中且不是加载更多操作，则直接返回
  if (loading.value && !isLoadMore) return;
  
  // 设置加载状态为true
  loading.value = true;
  
  try {
    // 优先使用DLID，如果没有则使用itemCode
    const paramItem = orderDetail.value.DLID || itemCode.value || "";
    
    // 构建API参数
    const params = {
      CFlag: '240',
      No: orderNo.value,
      Item: paramItem
    };
    
    const result = await getOrderSerialDetail(orderNo.value, paramItem, params);
    
    if (result && result.data) {
      const processedData = processSerialData(result.data);
      if (isLoadMore) {
        serialList.value = [...serialList.value, ...processedData];
      } else {
        serialList.value = processedData;
      }
      
      totalCount.value = result.count || result.data.length;
    } else if (result && Array.isArray(result)) {
      const processedData = processSerialData(result);
      if (isLoadMore) {
        serialList.value = [...serialList.value, ...processedData];
      } else {
        serialList.value = processedData;
      }
      
      totalCount.value = result.length;
    } else {
      if (!isLoadMore) {
        serialList.value = [];
      }
    }
  } catch (error) {
    } finally {
      loading.value = false;
    }
};

// 获取发货信息
const fetchShippingInfo = async () => {
  if (shippingInfoLoading.value) return;
  
  shippingInfoLoading.value = true;
  
  try {
    // 确保能获取到正确的值
    const orderNoValue = orderDetail.value.DNo || orderNo.value || '';
    const itemCodeValue = orderDetail.value.DLID || itemCode.value || '';
    
    // 构建完整的参数
    const params = {
      OP: 'GetOrderInfo',
      CFlag: '239',
      page: 1,
      limit: 20,
      No: orderNoValue,
      Item: itemCodeValue
    };
    
    // 调用获取发货信息的API
    const result = await getOrderInfo(params);
    
    if (result && result.data) {
      // 如果返回的是对象数据
      if (!Array.isArray(result.data)) {
        shippingInfo.value = result.data;
        shippingInfoList.value = [result.data]; // 转换为数组以便列表显示
      } 
      // 如果返回的是数组数据
      else {
        shippingInfoList.value = result.data;
        if (result.data.length > 0) {
          shippingInfo.value = result.data[0];
        }
      }
    } else if (result && Array.isArray(result)) {
      shippingInfoList.value = result;
      if (result.length > 0) {
        shippingInfo.value = result[0];
      }
    } else {
      shippingInfoList.value = [];
      uni.showToast({
        title: '获取发货信息失败',
        icon: 'none'
      });
    }
    
    // 标记为已加载
    shippingInfoLoaded.value = true;
  } catch (error) {
    uni.showToast({
      title: '获取发货信息失败',
      icon: 'none'
    });
  } finally {
    shippingInfoLoading.value = false;
  }
};

// 加载更多数据
const loadMore = () => {
  if (loading.value || !hasMore.value) return;
  
  currentPage.value++;
  fetchSerialDetail(currentPage.value, true);
};

// 获取状态标签样式
const getStatusClass = (status) => {
  switch (status) {
    case '待扫描':
      return 'status-warning'; // 黄色警告色
    case '已装箱':
    case '已发货':
      return 'status-success'; // 绿色成功色
    case '发货中':
      return 'status-info'; // 灰色信息色
    case '退货中':
      return 'status-return'; // 红色退货状态
    default:
      return 'status-default'; // 默认样式
  }
};

// 生命周期
onMounted(() => {
  try {
    // #ifdef APP-PLUS || MP-WEIXIN
    // APP环境下通过事件通道获取数据
    try {
      // 尝试多种方式获取事件通道
      let eventChannel;
      
      try {
        // 方法1：直接使用getOpenerEventChannel
        eventChannel = getOpenerEventChannel();
      } catch (err1) {
        try {
          // 方法2：通过当前页面实例获取
          const pages = getCurrentPages();
          const currentPage = pages[pages.length - 1];
          
          if (currentPage.getOpenerEventChannel) {
            eventChannel = currentPage.getOpenerEventChannel();
          } else if (currentPage.$getOpenerEventChannel) {
            eventChannel = currentPage.$getOpenerEventChannel();
          } else {
            throw new Error('无法获取事件通道');
          }
        } catch (err2) {
          throw err2;
        }
      }
      
      eventChannel.on('deliverDetail', (data) => {
        orderDetail.value = data.item || {};
        orderNo.value = orderDetail.value.DNo || '';
        
        // 使用传入的物料编码或默认值
        itemCode.value = data.item?.ItemCode || '';
        
        // 如果当前是序列号详情Tab，立即获取序列号数据
        if (currentTab.value === 1) {
          fetchSerialDetail();
        }
      });
    } catch (channelError) {
      // 尝试从URL参数获取数据
      try {
        const pages = getCurrentPages();
        const currentPage = pages[pages.length - 1];
        
        // 检查页面参数
        const options = currentPage.$page?.options || {};
        
        // 从URL或页面参数中获取数据
        if (options.orderNo) {
          orderNo.value = options.orderNo;
          
          // 如果有ID参数
          if (options.id) {
            itemCode.value = options.id;
          }
          
          // 不再从本地存储获取数据，避免数据混乱
          
          // 如果当前是序列号详情Tab，立即获取序列号数据
          if (currentTab.value === 1) {
            fetchSerialDetail();
          }
        } else {
          uni.showToast({
            title: '获取参数失败',
            icon: 'none'
          });
        }
      } catch (optionsError) {
        uni.showToast({
          title: '获取参数失败',
          icon: 'none'
        });
      }
    }
    // #endif
    
    // #ifdef H5
    // H5环境通过路由参数获取数据
    const pages = getCurrentPages();
    const currentPage = pages[pages.length - 1];
    const query = currentPage.$page?.options || {};
    
    if (query && query.orderData) {
      // 从URL参数中解析订单数据
      try {
        const decodedData = decodeURIComponent(query.orderData);
        orderDetail.value = JSON.parse(decodedData);
        orderNo.value = orderDetail.value.DNo || query.orderNo || '';
        
        // 使用传入的物料编码或ID参数
        itemCode.value = orderDetail.value.ItemCode || query.id || '';
        
        // 如果当前是序列号详情Tab，立即获取序列号数据
        if (currentTab.value === 1) {
          fetchSerialDetail();
        }
      } catch (error) {
        // 如果解析失败，尝试直接使用URL参数
        if (query.orderNo) {
          orderNo.value = query.orderNo;
          itemCode.value = query.id || '';
          
          // 如果当前是序列号详情Tab，立即获取序列号数据
          if (currentTab.value === 1) {
            fetchSerialDetail();
          }
        } else {
          uni.showToast({
            title: '获取订单详情失败',
            icon: 'none'
          });
        }
      }
    } else if (query.orderNo) {
      // 如果没有完整数据但有orderNo参数
      orderNo.value = query.orderNo;
      itemCode.value = query.id || '';
      
      // 如果当前是序列号详情Tab，立即获取序列号数据
      if (currentTab.value === 1) {
        fetchSerialDetail();
      }
    } else {
      uni.showToast({
        title: '参数不完整',
        icon: 'none'
      });
    }
    // #endif
  } catch (error) {
    uni.showToast({
      title: '获取详情数据失败',
      icon: 'none'
    });
  }
});
</script>

<style scoped>
/* 导航栏容器 */
.nav-bar-container {
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 100;
}

/* 主体内容区域 */
.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  width: 100%;
  overflow: hidden;
  position: relative;
  margin-top: 10px; /* 减小与标题栏的距离 */
}

/* 容器样式 */
.container {
  height: 100vh;
  width: 100%;
  display: flex;
  flex-direction: column;
  background-color: #f7f9fc;
  position: relative;
  overflow: hidden;
  margin: 0;
  border: none;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, sans-serif;
}

/* 订单状态显示 */
.order-status {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 16px;
  background-color: #fff;
  border-bottom: 1px solid #f0f0f0;
}

.order-number-container {
  display: flex;
  align-items: center;
}

.order-number {
  font-size: 14px;
  color: #333;
  font-weight: 500;
}

.bill-type {
  font-size: 11px;
  color: #64748b;
  margin-left: 8px;
  font-weight: 600;
}

/* Tab栏样式 */
.tabs {
  display: flex;
  background-color: #fff;
  border-bottom: 1px solid #f0f0f0;
  position: relative;
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 12px 0;
  font-size: 14px;
  color: #666;
  position: relative;
}

.tab-item.active {
  color: #0c873d;
  font-weight: 500;
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 20px;
  height: 3px;
  background-color: #0c873d;
  border-radius: 3px;
}

/* Tab内容区 */
.tab-content {
  flex: 1;
  height: 0;
  padding: 12px;
  box-sizing: border-box;
}

.tab-pane {
  height: 100%;
}

/* 详情卡片 */
.detail-card {
  background-color: #ffffff;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

/* 卡片标题 */
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  padding: 12px 16px 8px;
  border-bottom: 1px solid #f1f5f9;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
}

.item-count {
  font-size: 12px;
  color: #64748b;
  margin-right: 4px;
}

/* 信息网格 */
.info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

.info-item {
  display: flex;
  flex-direction: column;
}

.info-label {
  font-size: 12px;
  color: #64748b;
  margin-bottom: 4px;
}

.info-value {
  font-size: 14px;
  color: #334155;
  font-weight: 500;
}

.bill-type-fh {
  color: #67C23A; /* 发货单绿色 */
}

.bill-type-th {
  color: #F56C6C; /* 退货单红色 */
}

/* 信息列表 */
.info-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.info-row {
  display: flex;
  flex-direction: column;
}

/* 序列号卡片样式 */
.serial-card {
  background-color: #ffffff;
  border-radius: 12px;
  margin-bottom: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

.card-body {
  padding: 0px 0px;
}

/* 序列号列表样式 */
.serial-list {
  margin-top: 4px;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.serial-item {
  background-color: #ffffff;
  border-radius: 8px;
  padding: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;
  position: relative;
  margin: 0 4px;
}

.serial-item-header {
  margin-bottom: 8px;
  padding-bottom: 6px;
  border-bottom: 1px solid #e2e8f0;
}

.serial-title {
  font-size: 14px;
  font-weight: 600;
  color: #0f172a;
  display: block;
  white-space: normal; /* 允许换行 */
  overflow: visible; /* 允许内容超出显示 */
  text-overflow: clip; /* 不使用省略号 */
  line-height: 1.3; /* 增加行高 */
  margin-bottom: 4px; /* 增加与下方编码的间距 */
}

.serial-code {
  font-size: 12px;
  color: #64748b;
  display: block;
}

.serial-body {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px 12px; /* 增加行列间距 */
}

.serial-info {
  display: flex;
  flex-direction: column;
}

.serial-label {
  font-size: 10px;
  color: #64748b;
  margin-bottom: 1px;
}

.serial-value {
  font-size: 12px;
  color: #334155;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 空列表提示 */
.empty-list {
  padding: 24px;
  text-align: center;
  color: #64748b;
  font-size: 14px;
}

/* 加载更多 */
.load-more {
  text-align: center;
  padding: 12px;
  color: #0c873d;
  font-size: 14px;
}

/* 加载中和空数据提示 */
.loading-box, .empty-box {
  padding: 24px;
  text-align: center;
  color: #64748b;
  font-size: 14px;
}

/* 状态标签 */
.status-tag {
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  color: white;
}

.status-warning {
  background-color: #E6A23C;
}

.status-success {
  background-color: #67C23A;
}

.status-info {
  background-color: #909399;
}

.status-return {
  background-color: #F56C6C; /* 红色退货状态 */
}

.status-default {
  background-color: #606266; /* 默认灰色 */
}

/* 发货信息列表 */
.shipping-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.shipping-item-card {
  padding: 10px;
  margin-bottom: 0px;
}

.shipping-info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

.shipping-info {
  display: flex;
  flex-direction: column;
  margin-bottom: 2px;
}

.shipping-label {
  font-size: 12px;
  color: #64748b;
  margin-bottom: 3px;
}

.shipping-value {
  font-size: 14px;
  color: #334155;
  font-weight: 500;
}
</style> 
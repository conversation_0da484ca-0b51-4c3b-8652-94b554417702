<template>
  <view class="container">
    <!-- 使用uni-nav-bar组件 -->
    <view class="nav-bar-container">
      <uni-nav-bar
        :fixed="true"
        :status-bar="true"
        title="仓库管理"
        left-icon="left"
        @clickLeft="goBack"
        :border="false"
        :right-icon="null"
      >
        <template #right>
          <view class="nav-right-icon" @click="showOperationLog">
            <image class="add-icon" src="/static/icons/add.png" mode="aspectFit"></image>
          </view>
        </template>
      </uni-nav-bar>
    </view>
    
    <!-- 使用操作记录组件 -->
    <operation-log
      v-model:showLogModal="showLogModal"
      :operationLogs="operationLogs"
      @close="closeOperationLog"
      @clear="clearOperationLogs"
    />
    
    <!-- 主体内容区 -->
    <view class="main-content">
      <!-- 扫码和输入区域 -->
      <view class="scan-container">
        <view class="scan-icon-btn" @click="handleScan">
          <image class="scan-icon-left" src="/static/icons/scan.png" mode="aspectFit"></image>
        </view>
        <view class="input-wrapper" :class="{ 'input-focus': isInputFocused }">
          <input 
            class="code-input" 
            type="text" 
            v-model="codeInput" 
            placeholder="扫描或输入条码" 
            @confirm="handleCodeSubmit"
            @focus="handleInputFocus"
            @blur="handleInputBlur"
          />
          <view v-if="codeInput" class="clear-icon" @click.stop="clearCodeInput">×</view>
        </view>
      </view>
      
      <!-- 功能区域 -->
      <view class="function-area">
        <view class="function-title">仓库功能</view>
        <view class="function-grid">
          <view class="function-card" @click="navigateToInbound">
            <image class="function-icon" src="/static/icons/inbound-active.png" mode="aspectFit"></image>
            <text class="function-name">入库管理</text>
          </view>
          <view class="function-card" @click="navigateToOutbound">
            <image class="function-icon" src="/static/icons/outbound-active.png" mode="aspectFit"></image>
            <text class="function-name">出库管理</text>
          </view>
          <view class="function-card" @click="navigateToInventory">
            <image class="function-icon" src="/static/icons/inventory-active.png" mode="aspectFit"></image>
            <text class="function-name">库存查询</text>
          </view>
          <view class="function-card" @click="navigateToTransfer">
            <image class="function-icon" src="/static/icons/transfer-active.png" mode="aspectFit"></image>
            <text class="function-name">库存调拨</text>
          </view>
        </view>
      </view>
      
      <!-- 最近扫描记录 -->
      <view class="recent-scans">
        <view class="recent-title">最近扫描</view>
        <view v-if="recentScans.length === 0" class="empty-scans">
          <text>暂无扫描记录</text>
        </view>
        <view v-else class="scan-list">
          <view 
            v-for="(item, index) in recentScans" 
            :key="index" 
            class="scan-item"
            @click="handleScanItemClick(item)"
          >
            <view class="scan-info">
              <text class="scan-code">{{ item.code }}</text>
              <text class="scan-time">{{ item.time }}</text>
            </view>
            <view class="scan-action">
              <uni-icons type="right" size="16" color="#999"></uni-icons>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { useUserStore } from '@/store/user';
import scanCodeUtil from '@/utils/scanCode';

// 导入uni-nav-bar组件
import uniNavBar from '@dcloudio/uni-ui/lib/uni-nav-bar/uni-nav-bar.vue'
import uniIcons from '@dcloudio/uni-ui/lib/uni-icons/uni-icons.vue'

// 导入操作记录组件
import OperationLog from '@/components/OperationLog.vue'

// 用户状态管理
const userStore = useUserStore();

// 输入框内容
const codeInput = ref('');

// 输入框焦点状态
const isInputFocused = ref(false);

// 操作记录相关
const showLogModal = ref(false);
const operationLogs = ref([]);

// 最近扫描记录
const recentScans = ref([]);

// 处理输入框获得焦点
const handleInputFocus = () => {
  isInputFocused.value = true;
};

// 处理输入框失去焦点
const handleInputBlur = () => {
  isInputFocused.value = false;
};

// 处理输入提交
const handleCodeSubmit = () => {
  if (!codeInput.value.trim()) {
    uni.showToast({
      title: '请输入条码',
      icon: 'none'
    });
    return;
  }
  
  // 处理条码
  processCode(codeInput.value.trim());
  
  // 记录操作
  recordOperation(`提交条码: ${codeInput.value.trim()}`);
  
  // 清空输入框
  codeInput.value = '';
};

// 清除输入框
const clearCodeInput = () => {
  codeInput.value = '';
};

// 扫码功能
const handleScan = async () => {
  try {
    // 使用scanCode.js中的扫码功能
    const result = await scanCodeUtil.start({
      preserveGS1Separators: true
    });
    
    // 处理扫描结果
    processCode(result);
    
    // 记录操作
    recordOperation(`扫码成功: ${result}`);
  } catch (error) {
    uni.showToast({
      title: '扫码失败',
      icon: 'none'
    });
    console.error('扫码失败:', error);
    
    // 记录操作
    recordOperation(`扫码失败: ${error.message || '未知错误'}`);
  }
};

// 处理条码
const processCode = (code) => {
  // 添加到最近扫描记录
  const now = new Date();
  const timeString = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}-${String(now.getDate()).padStart(2, '0')} ${String(now.getHours()).padStart(2, '0')}:${String(now.getMinutes()).padStart(2, '0')}`;
  
  recentScans.value.unshift({
    code: code,
    time: timeString
  });
  
  // 限制记录数量
  if (recentScans.value.length > 5) {
    recentScans.value = recentScans.value.slice(0, 5);
  }
  
  // 保存到本地存储
  uni.setStorageSync('warehouseRecentScans', JSON.stringify(recentScans.value));
  
  // TODO: 根据条码类型跳转到不同页面
  uni.showModal({
    title: '条码识别',
    content: `识别到条码: ${code}，请选择要执行的操作`,
    showCancel: true,
    cancelText: '取消',
    confirmText: '查询',
    success: (res) => {
      if (res.confirm) {
        // 跳转到库存查询页面
        navigateToInventory(code);
      }
    }
  });
};

// 处理扫描记录点击
const handleScanItemClick = (item) => {
  // 跳转到库存查询页面
  navigateToInventory(item.code);
};

// 显示操作记录弹框
const showOperationLog = () => {
  showLogModal.value = true;
};

// 关闭操作记录弹框
const closeOperationLog = () => {
  showLogModal.value = false;
};

// 清空操作记录
const clearOperationLogs = () => {
  operationLogs.value = [];
};

// 记录操作
const recordOperation = (message) => {
  const now = new Date();
  const timeString = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}-${String(now.getDate()).padStart(2, '0')} ${String(now.getHours()).padStart(2, '0')}:${String(now.getMinutes()).padStart(2, '0')}:${String(now.getSeconds()).padStart(2, '0')}`;
  
  // 添加新记录到开头
  operationLogs.value.unshift({
    time: timeString,
    message: message
  });
  
  // 限制记录数量，最多保留50条
  if (operationLogs.value.length > 50) {
    operationLogs.value = operationLogs.value.slice(0, 50);
  }
};

// 返回上一页
const goBack = () => {
  uni.navigateBack({
    delta: 1,
    fail: () => {
      // 如果返回失败，则跳转到首页
      uni.switchTab({
        url: '/pages/index/index'
      });
    }
  });
};

// 导航到入库管理
const navigateToInbound = () => {
  uni.navigateTo({
    url: '/pages/warehouse/inbound'
  });
};

// 导航到出库管理
const navigateToOutbound = () => {
  uni.navigateTo({
    url: '/pages/warehouse/outbound'
  });
};

// 导航到库存查询
const navigateToInventory = (code = '') => {
  uni.navigateTo({
    url: `/pages/warehouse/inventory${code ? '?code=' + encodeURIComponent(code) : ''}`
  });
};

// 导航到库存调拨
const navigateToTransfer = () => {
  uni.navigateTo({
    url: '/pages/warehouse/transfer'
  });
};

// 生命周期
onMounted(() => {
  // 检查是否已登录
  checkLogin();
  
  // 从本地存储加载最近扫描记录
  try {
    const savedScans = uni.getStorageSync('warehouseRecentScans');
    if (savedScans) {
      recentScans.value = JSON.parse(savedScans);
    }
  } catch (error) {
    console.error('加载最近扫描记录失败:', error);
  }
});

// 检查登录状态
const checkLogin = () => {
  // 使用userStore的isLoggedIn getter
  const isLoggedIn = userStore.isLoggedIn;
  
  if (!isLoggedIn) {
    setTimeout(() => {
      // 再次检查，防止状态更新延迟
      if (!userStore.isLoggedIn) {
        uni.navigateTo({
          url: '/pages/login/login'
        });
      }
    }, 100);
  }
};
</script>

<style scoped>
/* 容器样式 */
.container {
  height: 100vh;
  width: 100%;
  display: flex;
  flex-direction: column;
  background-color: #f7f9fc;
  position: relative;
  overflow: hidden;
  margin: 0;
  border: none;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, sans-serif;
}

/* 导航栏容器 */
.nav-bar-container {
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 100;
}

/* 导航栏右侧图标 */
.nav-right-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 44px;
  height: 44px;
}

.add-icon {
  width: 20px;
  height: 20px;
}

/* 主体内容区域 */
.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  width: 100%;
  overflow-y: auto;
  position: relative;
  padding: 0 15px;
  margin-top: 10px; /* 减小与标题栏的距离 */
}

/* 扫码和输入区域 */
.scan-container {
  display: flex;
  align-items: center;
  margin: 10px 0;
  width: 100%;
}

.scan-icon-btn {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 10px;
}

.scan-icon-left {
  width: 24px;
  height: 24px;
}

.input-wrapper {
  flex: 1;
  display: flex;
  align-items: center;
  height: 36px;
  border-radius: 8px;
  border: 1px solid #d1d5db;
  background-color: #fff;
  padding: 0 10px;
  position: relative;
  transition: all 0.3s ease;
}

.input-focus {
  border-color: #4CAF50;
  box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.1);
}

.code-input {
  flex: 1;
  height: 100%;
  border: none;
  outline: none;
  font-size: 14px;
  color: #333;
}

.clear-icon {
  font-size: 16px;
  color: #999;
  padding: 4px;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
}

/* 功能区域样式 */
.function-area {
  margin: 15px 0;
  background-color: #fff;
  border-radius: 12px;
  padding: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.function-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 15px;
}

.function-grid {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  gap: 15px;
}

.function-card {
  width: calc(50% - 8px);
  aspect-ratio: 2/1;
  background-color: #f9f9f9;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 15px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.function-icon {
  width: 32px;
  height: 32px;
  margin-bottom: 8px;
}

.function-name {
  font-size: 14px;
  color: #333;
}

/* 最近扫描记录样式 */
.recent-scans {
  margin: 15px 0;
  background-color: #fff;
  border-radius: 12px;
  padding: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.recent-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 15px;
}

.empty-scans {
  padding: 20px 0;
  text-align: center;
  color: #999;
  font-size: 14px;
}

.scan-list {
  display: flex;
  flex-direction: column;
}

.scan-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.scan-item:last-child {
  border-bottom: none;
}

.scan-info {
  display: flex;
  flex-direction: column;
}

.scan-code {
  font-size: 14px;
  color: #333;
  margin-bottom: 4px;
}

.scan-time {
  font-size: 12px;
  color: #999;
}

.scan-action {
  display: flex;
  align-items: center;
}
</style> 
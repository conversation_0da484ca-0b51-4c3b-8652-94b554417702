import CONFIG from '@/config/index.js'

// 接口共用地址
let BASE_URL = CONFIG.serverUrl

// 请求超时时间
const TIMEOUT = 10000

// 创建请求对象
const http = {
  config: {
    baseURL: BASE_URL,
    header: {
      'accept': 'application/json',
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    timeout: TIMEOUT
  },
  
  setConfig(customConfig) {
    this.config = {
      ...this.config,
      ...customConfig
    }
  },
  
  request(options = {}) {
    if (!options.url) return Promise.reject(new Error('请求地址不能为空'))
    
    options.baseURL = options.baseURL || this.config.baseURL
    options.timeout = options.timeout || this.config.timeout
    options.header = {
      ...this.config.header,
      ...options.header
    }
    
    // 添加基础URL
    if (!options.url.startsWith('http')) {
      // 确保URL格式正确
      if (options.url.startsWith('/')) {
        // 如果baseURL已经是完整URL，则需要去掉URL前面的斜杠
        if (options.baseURL.startsWith('http')) {
          options.url = options.url.substring(1)
        }
        options.url = options.baseURL + options.url
      } else {
        options.url = options.baseURL + options.url
      }
    }
    
    // 调试日志
    // console.log('请求URL:', options.url)
    
    return new Promise((resolve, reject) => {
      options.success = (response) => {
        if (response.statusCode === 200) {
          const data = response.data
          
          // 处理登录错误或响应为空的情况
          if (data && data.Msg === "LoginError") {
            uni.showToast({
              title: "您未登录系统，请登录！",
              icon: 'none',
              duration: 2000
            })
            
            // 跳转到登录页
            setTimeout(() => {
              uni.reLaunch({
                url: '/pages/login/login'
              })
            }, 1500)
            
            return reject({ message: '登录已过期，请重新登录' })
          }
          
          // 添加对Error类型响应的处理
          if (data && data.Msg === "Error") {
            // 显示错误信息
            uni.showToast({
              title: data.Message || '请求失败，请确认无误后重试',
              icon: 'none',
              duration: 2000
            })
            
            // 返回错误对象，但不拦截，让业务层处理
            return resolve(data)
          }
          
          resolve(data === undefined ? {} : data)
        } else {
          // 处理错误响应
          let errorMsg = '请求失败'
          
          if (response.statusCode === 401) {
            errorMsg = '登录已过期，请重新登录'
            // 跳转到登录页
            setTimeout(() => {
              uni.reLaunch({
                url: '/pages/login/login'
              })
            }, 1500)
          } else if (response.statusCode === 400) {
            errorMsg = response.data?.message || '请求参数错误'
          } else if (response.statusCode === 403) {
            errorMsg = response.data?.message || '没有权限进行此操作'
          } else if (response.statusCode === 404) {
            errorMsg = '请求失败，接口不存在'
          } else if (response.statusCode === 500) {
            errorMsg = response.data?.message || '服务器内部错误'
          }
          
          uni.showToast({
            title: errorMsg,
            icon: 'none',
            duration: 2000
          })
          
          reject({ message: errorMsg })
        }
      }
      
      options.fail = (error) => {
        // 处理请求失败
        let errorMsg = '请求失败，请检查网络连接'
        console.error('请求失败:', error)
        
        if (error.errMsg && error.errMsg.includes('timeout')) {
          errorMsg = '网络请求超时'
        } else if (error.errMsg && error.errMsg.includes('fail')) {
          errorMsg = '请求失败，服务器无法连接'
        }
        
        uni.showToast({
          title: errorMsg,
          icon: 'none',
          duration: 2000
        })
        
        reject(error)
      }
      
      // 发起请求
      uni.request(options)
    })
  },
  
  get(url, data, options = {}) {
    return this.request({
      url,
      data,
      method: 'GET',
      ...options
    })
  },
  
  post(url, data, options = {}) {
    return this.request({
      url,
      data,
      method: 'POST',
      ...options
    })
  },
  
  put(url, data, options = {}) {
    return this.request({
      url,
      data,
      method: 'PUT',
      ...options
    })
  },
  
  delete(url, data, options = {}) {
    return this.request({
      url,
      data,
      method: 'DELETE',
      ...options
    })
  }
}

export default http
export { BASE_URL as baseURL } 
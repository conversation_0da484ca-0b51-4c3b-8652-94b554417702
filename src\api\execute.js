import http from '@/utils/request'

// 扫描作业单元
export const scanUnit = (params) => {
  return http.post("Service/OrderAjax.ashx?OP=GetWorkCenter", params);
};

// 扫描序列号
export const scanSerial = (params) => {
  return http.post("Service/OrderAjax.ashx?OP=GetSerialInfoByNo", params);
};

// 加载工序 -- 版本
export const getProcedureList = (params) => {
  return http.get("Service/OrderAjax.ashx?OP=GetProcedureList", { params });
};

// 产生批量生产批次方法
export const getOPPRDUpbatchInfo = (params) => {
  return http.get("Service/OrderAjax.ashx?OP=OPPRDUpbatchInfo", { params });
};

// 第三方获取测试记录
export const getThirdTestItem = (params) => {
  return http.get("Service/OrderAjax.ashx?OP=GetThirdTestItem", { params });
};

// 工序行为是送检抽检合并
export const createCheckBatchNo = (params) => {
  return http.get("Service/OrderAjax.ashx?OP=OPPrdSamplingInfo", { params });
};

// 追溯物料，包装送检
export const getPRDInfoTwo = (params) => {
  return http.get("Service/OrderAjax.ashx?OP=GetPRDInfoTwo", { params });
};

// 获取测试项
export const getPRDTestInfo = (params) => {
  return http.get("Service/OrderAjax.ashx?OP=GetPRDTestInfo", { params });
};

// 获取包装批次及对应数量
export const getUpBatchInfo = (params) => {
  return http.get("Service/OrderAjax.ashx?OP=GetUpBatchInfo", { params });
};

// 扫描设备信息
export const getOPPRDDeviceInfo = (params) => {
  return http.get("Service/OrderAjax.ashx?OP=OPPRDDeviceInfo", { params });
};

// 获取设备信息
export const getOrderInfo = (params) => {
  return http.get("Service/OrderAjax.ashx?OP=GetOrderInfo", { params });
};

// 抽检信息
export const getWXInfo = (params) => {
  return http.get("Service/OrderAjax.ashx?OP=GetWXInfo", { params });
};

// 抽样方案列表
export const getSamplingPlanList = (params) => {
  return http.get("Service/OrderAjax.ashx?OP=GetSamplingPlanList", { params });
};

// 不良代码
export const getDefectsCauseByNo = (params) => {
  return http.get("Service/OrderAjax.ashx?OP=GetDefectsCauseByNo", { params });
};

// 提交不良代码信息
export const submitOPPRDYCCodeInfo = (params) => {
  return http.post("Service/OrderAjax.ashx?OP=OPPRDYCCodeInfo", { Data: params.Data });
};

// 扫描追溯/装箱物料，获取详情
export const getOPPRDMaterInfo = (params) => {
  return http.get("Service/OrderAjax.ashx?OP=OPPRDMaterInfo", { params });
};

// 切换抽检方案
export const getOPInspectInfo = (params) => {
  return http.get("Service/OrderAjax.ashx?OP=OPInspectInfo", { params });
};

// 完工
export const getOPPRDOverInfo = (params) => {
  return http.get("Service/OrderAjax.ashx?OP=OPPRDOverInfo", { params });
};

// 抽检完工
export const getOPPrdSamplingInfo = (params) => {
  return http.get("Service/OrderAjax.ashx?OP=OPPrdSamplingInfo", { params });
};

// 生成DHR
export const getExportConditions = (params) => {
  return http.get("Service/DHRAjax.ashx?OP=GetExportConditions", { params });
};

// 获取要生成的DHR
export const getExportData = (params) => {
  return http.get("Service/DHRAjax.ashx?OP=GetExportData", { params });
};

// 提交测试
export const submitOPPRDTestInfo = (params) => {
  return http.get("Service/OrderAjax.ashx?OP=OPPRDTestInfo", { params });
};

// 切换工序版本
export const changeOPPRDChangeVerInfo = (params) => {
  return http.post("Service/OrderAjax.ashx?OP=OPPRDChangeVerInfo", params);
};

// 获取最新版本
export const getSoftwareVersionInfoList = (params) => {
  return http.post("Service/BaseModuleAjax.ashx?OP=GetSoftwareVersionInfoList", params);
};

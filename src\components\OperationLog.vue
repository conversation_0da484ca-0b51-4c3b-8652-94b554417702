<template>
  <view class="operation-log-modal" v-if="showLogModal">
    <view class="modal-mask" @click="handleClose"></view>
    <view class="modal-content" @click.stop>
      <view class="modal-header">
        <text class="modal-title">操作记录</text>
        <view class="header-close-btn" @click="handleClose">×</view>
      </view>
      <scroll-view scroll-y class="modal-body">
        <view v-if="operationLogs.length === 0" class="empty-log">
          <text>暂无操作记录</text>
        </view>
        <view v-else class="log-list">
          <view v-for="(log, index) in operationLogs" :key="index" class="log-item">
            <view class="log-time">{{ log.time }}</view>
            <view class="log-message">{{ log.message }}</view>
          </view>
        </view>
      </scroll-view>
      <view class="modal-footer">
        <button class="clear-btn" @click="handleClear">清空记录</button>
        <button class="footer-close-btn" @click="handleClose">关闭</button>
      </view>
    </view>
  </view>
</template>

<script setup>
import { defineProps, defineEmits } from 'vue';

// 定义props
const props = defineProps({
  showLogModal: {
    type: Boolean,
    default: false
  },
  operationLogs: {
    type: Array,
    default: () => []
  }
});

// 定义事件
const emit = defineEmits(['update:showLogModal', 'clear', 'close']);

// 处理关闭事件
const handleClose = () => {
  emit('close');
  emit('update:showLogModal', false);
};

// 处理清空事件
const handleClear = () => {
  // 只触发清空事件，不显示提示
  emit('clear');
};
</script>

<style scoped>
/* 操作记录弹框样式 */
.operation-log-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
  pointer-events: none; /* 默认不拦截点击事件 */
}

.modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  pointer-events: auto; /* 蒙层可以接收点击事件 */
}

.modal-content {
  position: relative;
  width: 85vw;
  max-width: 500px;
  height: 85vh;
  background-color: #fff;
  border-radius: 12px;
  overflow: hidden;
  z-index: 10000;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  display: flex;
  flex-direction: column;
  pointer-events: auto; /* 内容区域可以接收点击事件 */
}

.modal-header {
  padding: 15px;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-shrink: 0;
  position: relative;
}

.modal-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  flex: 1;
  text-align: center;
}

/* 标题栏中的关闭按钮 */
.header-close-btn {
  font-size: 14px;
  color: #999;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  position: absolute;
  right: 15px;
  top: 15px;
  background-color: #f5f5f5;
}

.modal-body {
  padding: 15px;
  flex: 1;
  overflow-y: auto;
}

.empty-log {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100px;
  color: #999;
  font-size: 14px;
}

.log-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.log-item {
  padding: 10px;
  border-radius: 8px;
  background-color: #f9f9f9;
  border-left: 3px solid #0c873d;
}

.log-time {
  font-size: 12px;
  color: #666;
  margin-bottom: 5px;
}

.log-message {
  font-size: 14px;
  color: #333;
  word-break: break-all;
}

.modal-footer {
  padding: 15px;
  border-top: 1px solid #f0f0f0;
  display: flex;
  justify-content: space-between;
  gap: 10px;
  flex-shrink: 0;
}

.clear-btn, .footer-close-btn {
  flex: 1;
  height: 40px;
  line-height: 40px;
  text-align: center;
  border-radius: 8px;
  font-size: 14px;
}

.clear-btn {
  background-color: #f5f5f5;
  color: #333;
}

.footer-close-btn {
  background-color: #0c873d;
  color: #fff;
}
</style> 
<template>
  <view class="container">
    <!-- 使用uni-nav-bar组件 -->
    <view class="nav-bar-container">
      <uni-nav-bar
        :fixed="true"
        :status-bar="true"
        title="生产执行"
        left-icon="left"
        @clickLeft="goBack"
        :border="false"
        :right-icon="null"
      >
        <template #right>
          <view class="nav-right-icon" @click="showOperationLog">
            <image class="add-icon" src="/static/icons/add.png" mode="aspectFit"></image>
          </view>
        </template>
      </uni-nav-bar>
    </view>
    
    <!-- 使用操作记录组件 -->
    <operation-log
      v-model:showLogModal="showLogModal"
      :operationLogs="operationLogs"
      @close="closeOperationLog"
      @clear="clearOperationLogs"
    />
    
    <!-- 主体内容区 -->
    <view class="main-content">
      <!-- 扫码和输入区域 -->
      <view class="scan-container">
        <view class="scan-icon-btn" @click="handleScan">
          <image class="scan-icon-left" src="/static/icons/scan.png" mode="aspectFit"></image>
        </view>
        <view class="input-wrapper" :class="{ 'input-focus': isInputFocused }">
          <input 
            class="code-input" 
            type="text" 
            v-model="codeInput" 
            placeholder="扫描或输入条码" 
            @confirm="handleCodeSubmit"
            @focus="handleInputFocus"
            @blur="handleInputBlur"
          />
          <view v-if="codeInput" class="clear-icon" @click.stop="clearCodeInput">×</view>
        </view>
      </view>
      
      <!-- Tab栏 -->
      <view class="tabs">
        <view class="tabs-container">
          <view 
            v-for="(tab, index) in tabs" 
            :key="index" 
            class="tab-item" 
            :class="{ active: currentTab === index }"
            @click="switchTab(index)"
          >
            <text>{{ tab.name }}</text>
          </view>
        </view>
      </view>
      
      <!-- Tab内容区 -->
      <view class="tab-content">
        <!-- 使用组件化的Tab内容 -->
        <basic-info-tab 
          v-if="currentTab === 0" 
          :productionInfo="productionInfo"
        />
        
        <batch-info-tab 
          v-if="currentTab === 1" 
          :batchInfo="batchInfo"
        />
        
        <equipment-info-tab 
          v-if="currentTab === 2" 
          :equipmentInfo="equipmentInfo"
        />
        
        <material-info-tab 
          v-if="currentTab === 3" 
          :materialInfo="materialInfo"
        />
        
        <inspection-info-tab 
          v-if="currentTab === 4" 
          :inspectionInfo="inspectionInfo"
        />
        
        <test-info-tab 
          v-if="currentTab === 5" 
          :testInfo="testInfo"
        />
        
        <exception-info-tab 
          v-if="currentTab === 6" 
          :exceptionInfo="exceptionInfo"
        />
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { useUserStore } from '@/store/user';
import scanCodeUtil from '@/utils/scanCode';

// 导入uni-nav-bar组件
import uniNavBar from '@dcloudio/uni-ui/lib/uni-nav-bar/uni-nav-bar.vue'

// 导入操作记录组件
import OperationLog from '@/components/OperationLog.vue'

// 导入Tab组件
import BasicInfoTab from './tabs/BasicInfoTab.vue'
import BatchInfoTab from './tabs/BatchInfoTab.vue'
import EquipmentInfoTab from './tabs/EquipmentInfoTab.vue'
import MaterialInfoTab from './tabs/MaterialInfoTab.vue'
import InspectionInfoTab from './tabs/InspectionInfoTab.vue'
import TestInfoTab from './tabs/TestInfoTab.vue'
import ExceptionInfoTab from './tabs/ExceptionInfoTab.vue'

// 用户状态管理
const userStore = useUserStore();

// 输入框内容
const codeInput = ref('');

// 输入框焦点状态
const isInputFocused = ref(false);

// 操作记录相关
const showLogModal = ref(false);
const operationLogs = ref([]);

// Tab栏配置
const tabs = [
  { name: '基本信息' },
  { name: '上层批次' },
  { name: '设备信息' },
  { name: '追溯物料' },
  { name: '抽检信息' },
  { name: '测试信息' },
  { name: '异常信息' }
];
const currentTab = ref(0);

// 各Tab数据
const productionInfo = ref({});
const batchInfo = ref([]);
const equipmentInfo = ref([]);
const materialInfo = ref([]);
const inspectionInfo = ref([]);
const testInfo = ref([]);
const exceptionInfo = ref([]);

// 处理输入框获得焦点
const handleInputFocus = () => {
  isInputFocused.value = true;
};

// 处理输入框失去焦点
const handleInputBlur = () => {
  isInputFocused.value = false;
};

// 处理输入提交
const handleCodeSubmit = () => {
  if (!codeInput.value.trim()) {
    uni.showToast({
      title: '请输入条码',
      icon: 'none'
    });
    return;
  }
  
  // 执行工单查询
  const code = codeInput.value.trim();
  processProductionCode(code);
  
  // 记录操作
  recordOperation(`提交条码: ${code}`);
  
  // 清空输入框
  codeInput.value = '';
};

// 清除输入框
const clearCodeInput = () => {
  codeInput.value = '';
};

// 扫码功能
const handleScan = async () => {
  try {
    // 使用scanCode.js中的扫码功能
    const result = await scanCodeUtil.start({
      preserveGS1Separators: true
    });
    
    // 直接处理扫描结果，不显示在输入框
    processProductionCode(result);
    
    // 记录操作
    recordOperation(`扫码成功: ${result}`);
    
    // 确保输入框为空
    codeInput.value = '';
  } catch (error) {
    uni.showToast({
      title: '扫码失败',
      icon: 'none'
    });
    console.error('扫码失败:', error);
    
    // 记录操作
    recordOperation(`扫码失败: ${error.message || '未知错误'}`);
  }
};

// 处理生产工单号
const processProductionCode = (code) => {
  // TODO: 调用API获取工单信息
  fetchProductionInfo(code);
};

// 获取生产工单信息
const fetchProductionInfo = async (code) => {
  try {
    uni.showLoading({
      title: '加载中...',
      mask: true
    });
    
    // 记录操作
    recordOperation(`开始获取工单信息: ${code}`);
    
    // TODO: 这里应该调用实际的API获取数据
    // 模拟API调用延迟
    setTimeout(() => {
      // 模拟数据
      productionInfo.value = {
        orderNo: code,
        productName: '测试产品',
        spec: 'XC-001',
        planQty: 100,
        planStartTime: '2023-05-01 08:00',
        planEndTime: '2023-05-01 17:00',
        status: '进行中',
        remark: '测试生产工单'
      };
      
      // 模拟其他Tab数据
      batchInfo.value = [
        {
          batchNo: 'B20230501001',
          materialName: '原材料A',
          quantity: 50,
          productionDate: '2023-04-28'
        }
      ];
      
      equipmentInfo.value = [
        {
          equipmentName: '生产设备1',
          equipmentNo: 'EQ001',
          status: '正常',
          useTime: '2023-05-01 08:00-17:00'
        }
      ];
      
      materialInfo.value = [
        {
          materialName: '组件A',
          materialNo: 'M001',
          batchNo: 'MB001',
          quantity: 20
        }
      ];
      
      inspectionInfo.value = [
        {
          inspectionNo: 'I001',
          inspectionItem: '外观检查',
          result: '合格',
          inspectionTime: '2023-05-01 10:00'
        }
      ];
      
      testInfo.value = [
        {
          testNo: 'T001',
          testItem: '功能测试',
          result: '通过',
          testTime: '2023-05-01 15:00'
        }
      ];
      
      exceptionInfo.value = [];
      
      uni.hideLoading();
      
      // 显示成功提示
      uni.showToast({
        title: '加载成功',
        icon: 'success'
      });
      
      // 记录操作
      recordOperation(`获取工单信息成功: ${code}`);
    }, 1000);
  } catch (error) {
    uni.hideLoading();
    uni.showToast({
      title: '获取数据失败',
      icon: 'none'
    });
    console.error('获取生产工单信息失败:', error);
    
    // 记录操作
    recordOperation(`获取工单信息失败: ${error.message || '未知错误'}`);
  }
};

// 切换Tab
const switchTab = (index) => {
  currentTab.value = index;
  
  // 移除记录操作
  // recordOperation(`切换到标签: ${tabs[index].name}`);
};

// 返回上一页
const goBack = () => {
  // 记录操作
  recordOperation('返回上一页');
  
  uni.navigateBack({
    delta: 1,
    fail: () => {
      // 如果返回失败，则跳转到生产执行首页
      uni.redirectTo({
        url: './productionIndex'
      });
    }
  });
};

// 显示操作记录弹框
const showOperationLog = () => {
  // 移除记录操作
  // recordOperation('查看操作记录');
  
  // 设置显示状态
  showLogModal.value = true;
  
  // 移除提示
  // uni.showToast({
  //   title: '已打开操作记录',
  //   icon: 'none',
  //   duration: 500
  // });
};

// 关闭操作记录弹框
const closeOperationLog = () => {
  showLogModal.value = false;
  
  // 移除提示
  // uni.showToast({
  //   title: '已关闭操作记录',
  //   icon: 'none',
  //   duration: 500
  // });
};

// 清空操作记录
const clearOperationLogs = () => {
  // 完全清空操作记录，不保留任何记录
  operationLogs.value = [];
  
  // 移除记录清空操作
  // recordOperation('清空操作记录');
};

// 记录操作
const recordOperation = (message) => {
  const now = new Date();
  const timeString = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}-${String(now.getDate()).padStart(2, '0')} ${String(now.getHours()).padStart(2, '0')}:${String(now.getMinutes()).padStart(2, '0')}:${String(now.getSeconds()).padStart(2, '0')}`;
  
  // 添加新记录到开头
  operationLogs.value.unshift({
    time: timeString,
    message: message
  });
  
  // 限制记录数量，最多保留50条
  if (operationLogs.value.length > 50) {
    operationLogs.value = operationLogs.value.slice(0, 50);
  }
  
  // 控制台输出，便于调试
  console.log(`操作记录: ${timeString} - ${message}`);
};

// 生命周期
onMounted(() => {
  // 检查是否已登录
  checkLogin();
  
  // 获取URL参数
  const pages = getCurrentPages();
  const currentPage = pages[pages.length - 1];
  const options = currentPage.$page?.options || {};
  
  // 如果有code参数，自动处理，但不设置到输入框
  if (options.code) {
    const code = decodeURIComponent(options.code);
    processProductionCode(code);
    
    // 记录操作
    recordOperation(`页面加载，自动处理条码: ${code}`);
    
    // 确保输入框为空
    codeInput.value = '';
  } else {
    // 记录操作
    recordOperation('页面加载完成');
  }
  
  // APP端设置状态栏样式
  // #ifdef APP-PLUS
  plus.navigator.setStatusBarStyle('dark');
  // #endif
});

// 检查登录状态
const checkLogin = () => {
  // 使用userStore的isLoggedIn getter
  const isLoggedIn = userStore.isLoggedIn;
  
  if (!isLoggedIn) {
    setTimeout(() => {
      // 再次检查，防止状态更新延迟
      if (!userStore.isLoggedIn) {
        uni.navigateTo({
          url: '/pages/login/login'
        });
      }
    }, 100);
  }
};
</script>

<style scoped>
/* 容器样式 */
.container {
  height: 100vh;
  width: 100%;
  display: flex;
  flex-direction: column;
  background-color: #f7f9fc;
  position: relative;
  overflow: hidden;
  margin: 0;
  border: none;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, sans-serif;
}

/* 导航栏容器 */
.nav-bar-container {
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 100;
}

/* 导航栏右侧图标 */
.nav-right-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 44px;
  height: 44px;
}

.add-icon {
  width: 20px;
  height: 20px;
}

/* 主体内容区域 */
.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  width: 100%;
  overflow: hidden;
  position: relative;
  padding: 0 15px;
  margin-top: 10px; /* 减小与标题栏的距离 */
}

/* 扫码和输入区域 */
.scan-container {
  display: flex;
  align-items: center;
  margin: 10px 0;
  width: 100%;
}

.scan-icon-btn {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 10px;
}

.scan-icon-left {
  width: 24px;
  height: 24px;
}

.input-wrapper {
  flex: 1;
  display: flex;
  align-items: center;
  height: 36px;
  border-radius: 8px;
  border: 1px solid #d1d5db;
  background-color: #fff;
  padding: 0 10px;
  position: relative;
  transition: all 0.3s ease;
}

.input-focus {
  border-color: #4CAF50;
  box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.1);
}

.code-input {
  flex: 1;
  height: 100%;
  border: none;
  outline: none;
  font-size: 14px;
  color: #333;
}

.clear-icon {
  font-size: 16px;
  color: #999;
  padding: 4px;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
}

/* Tab栏样式 */
.tabs {
  position: relative;
  margin: 5px 0;
  border-bottom: 1px solid #f0f0f0;
  background-color: #fff;
  overflow: hidden;
}

.tabs-container {
  display: flex;
  align-items: center;
  width: 100%;
  justify-content: flex-start;
  flex-wrap: nowrap;
  overflow-x: auto;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
  padding: 0 5px;
}

.tabs-container::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Opera */
}

.tab-item {
  flex: 0 0 auto;
  text-align: center;
  padding: 12px 15px;
  font-size: 14px;
  color: #666;
  position: relative;
  white-space: nowrap;
}

.tab-item.active {
  color: #0c873d;
  font-weight: 500;
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 20px;
  height: 3px;
  background-color: #0c873d;
  border-radius: 3px;
}

/* Tab内容区 */
.tab-content {
  flex: 1;
  overflow-y: auto;
  padding: 10px 0;
  box-sizing: border-box;
}
</style> 
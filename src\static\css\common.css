/* 底部导航图标 */
.tab-icon {
  font-size: 24px;
  color: #999;
}

.tab-item.active .tab-icon {
  color: #0c873d;
}

/* 自定义图标替代 */
.icon-home:before {
  content: "\e900";  /* 使用Unicode字符 */
}

/* 激活状态的图标 */
.tab-item.active .icon-home,
.tab-item.active .icon-profile {
  color: #0c873d;
}

/* Tab bar common styles */
.tab-bar {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 50px;
  background-color: #fff;
  display: flex;
  border-top: 1px solid #f1f1f1;
  z-index: 100;
}

.tab-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 5px 0;
}

.tab-text {
  font-size: 12px;
  color: #333;
}

.tab-item.active .tab-text {
  color: #0c873d;
}

/* 页面基础样式 */
page {
  height: 100vh;
  overflow: hidden;
  position: fixed;
  width: 100%;
  touch-action: none;
}

/* 容器基础样式 */
.container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  position: relative;
  box-sizing: border-box;
  padding-bottom: 50px;
  touch-action: none;
}

/* 顶部标题栏基础样式 */
.header {
  padding: 10px 0;
  text-align: center;
  position: relative;
  z-index: 10;
  background-color: #f8f8f8 !important;
  border-bottom: 1px solid #eaeaea;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

/* 标题文本样式 */
.header text {
  font-size: 18px;
  font-weight: 500;
  color: #333 !important;
}

/* 返回按钮通用样式 */
.back-btn {
  position: absolute;
  left: 15px;
  top: 50%;
  transform: translateY(-50%);
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon {
  font-size: 20px;
  color: #333;
  font-weight: bold;
}

/* 安全区域适配 */
.safe-area-inset-top {
  padding-top: constant(safe-area-inset-top);
  padding-top: env(safe-area-inset-top);
}

.safe-area-inset-bottom {
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}

.safe-area-inset-left {
  padding-left: constant(safe-area-inset-left);
  padding-left: env(safe-area-inset-left);
}

.safe-area-inset-right {
  padding-right: constant(safe-area-inset-right);
  padding-right: env(safe-area-inset-right);
}

/* 通用样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* 解决APP空白问题 */
/* #ifdef APP-PLUS */
html, body {
  height: 100%;
  width: 100%;
  overflow-x: hidden;
}

/* 状态栏高度CSS变量 */
page {
  --status-bar-height: 0px;
}

/* 检测状态栏高度并设置变量 */
:root {
  --status-bar-height: 0px;
}

uni-page-head {
  display: block !important;
}

uni-page-wrapper {
  height: 100% !important;
  display: flex !important;
  flex-direction: column !important;
}

uni-page-body {
  flex: 1 !important;
}

uni-tabbar {
  height: 50px !important;
}

/* 修复页面顶部状态栏 */
.status-bar {
  width: 100%;
  position: sticky;
  top: 0;
  z-index: 100;
}

/* 自定义导航栏容器 */
.nav-container {
  width: 100%;
  padding-top: var(--status-bar-height);
  display: flex;
  flex-direction: column;
}

/* 覆盖原生导航栏样式 */
uni-app uni-page-head {
  display: none !important;
}

.uni-app--showtabbar uni-page-wrapper::after {
  height: calc(50px + constant(safe-area-inset-bottom)) !important;
  height: calc(50px + env(safe-area-inset-bottom)) !important;
}
/* #endif */

/* 页面容器 */
.page-container {
  min-height: 100vh;
  width: 100%;
  background-color: #f8f8f8;
}

/* 弹性盒布局 */
.flex {
  display: flex;
}

.flex-col {
  display: flex;
  flex-direction: column;
}

.flex-1 {
  flex: 1;
}

.justify-center {
  justify-content: center;
}

.align-center {
  align-items: center;
}

/* 间距 */
.m-10 {
  margin: 10px;
}

.p-10 {
  padding: 10px;
}

/* 颜色相关 */
.primary-color {
  color: #0c873d;
}

.primary-bg {
  background-color: #0c873d;
}

/* 卡片样式 */
.card {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  padding: 15px;
  margin-bottom: 15px;
} 
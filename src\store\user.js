import { defineStore } from 'pinia'
import { login as queryLogin } from '@/api/login'

// 获取本地存储的用户信息
const getStoredUserInfo = () => {
  try {
    const userInfo = uni.getStorageSync('userInfo')
    // 检查是否为字符串，如果是则尝试解析，否则直接返回
    if (typeof userInfo === 'string') {
      return userInfo ? JSON.parse(userInfo) : null
    }
    
    return userInfo || null
  } catch (e) {
    console.error('获取本地用户信息失败:', e)
    // 出错时清除可能损坏的数据
    uni.removeStorageSync('userInfo')
    return null
  }
}

// 获取存储的过期时间
const getStoredExpireTime = () => {
  try {
    const expireTime = uni.getStorageSync('tokenExpireTime')
    return expireTime ? Number(expireTime) : null
  } catch (e) {
    console.error('获取token过期时间失败:', e)
    return null
  }
}

export const useUserStore = defineStore('user', {
  state: () => ({
    userInfo: getStoredUserInfo() || null,
    tokenExpireTime: getStoredExpireTime() || null
  }),
  
  getters: {
    isLoggedIn: (state) => {
      // 首先检查token是否过期
      if (state.tokenExpireTime) {
        const now = Date.now()
        if (now > state.tokenExpireTime) {
          console.log('token已过期，需要重新登录')
          return false
        }
      }
      
      // 检查userInfo是否存在
      if (state.userInfo) return true;
      
      // 如果state中没有，检查本地存储
      try {
        const storedUserInfo = uni.getStorageSync('userInfo');
        const tokenExpireTime = uni.getStorageSync('tokenExpireTime');
        
        // 检查token是否过期
        if (tokenExpireTime) {
          const now = Date.now()
          if (now > Number(tokenExpireTime)) {
            console.log('本地存储的token已过期，需要重新登录')
            return false
          }
        }
        
        // 如果有用户信息，且token未过期，认为已登录
        return !!storedUserInfo;
      } catch (e) {
        console.error('获取本地存储用户信息失败:', e);
        return false;
      }
    },
    isTokenExpired: (state) => {
      if (!state.tokenExpireTime) return true;
      
      const now = Date.now();
      return now > state.tokenExpireTime;
    },
    getUserInfo: (state) => state.userInfo,
    getCustNo: (state) => state.userInfo?.custNo
  },
  
  actions: {
    // 设置用户信息
    setUserInfo(userInfo) {
      if (!userInfo) {
        console.error('setUserInfo: 用户信息不能为空');
        return;
      }
      
      this.userInfo = {
        ...(this.userInfo || {}),
        ...userInfo
      }
      
      console.log('设置用户信息:', this.userInfo);
      
      // 存储到本地，确保序列化为JSON字符串
      try {
        uni.setStorageSync('userInfo', JSON.stringify(this.userInfo));
        console.log('用户信息已保存到本地存储');
      } catch (e) {
        console.error('保存用户信息失败:', e)
      }
    },
    
    // 设置token过期时间（12小时后过期）
    setTokenExpireTime() {
      const TWELVE_HOURS = 12 * 60 * 60 * 1000; // 12小时的毫秒数
      this.tokenExpireTime = Date.now() + TWELVE_HOURS;
      
      try {
        uni.setStorageSync('tokenExpireTime', this.tokenExpireTime.toString());
        console.log('Token过期时间已设置:', new Date(this.tokenExpireTime).toLocaleString());
      } catch (e) {
        console.error('保存Token过期时间失败:', e)
      }
    },
    
    // 检查token是否过期
    checkTokenExpiration() {
      if (!this.tokenExpireTime) {
        console.log('没有设置token过期时间');
        return true; // 如果没有设置过期时间，视为已过期
      }
      
      const now = Date.now();
      const isExpired = now > this.tokenExpireTime;
      
      if (isExpired) {
        console.log('Token已过期，过期时间:', new Date(this.tokenExpireTime).toLocaleString());
        // 清除过期的登录信息
        this.clearUserInfo();
        
        // 跳转到登录页面
        uni.reLaunch({
          url: '/pages/login/login'
        });
      } else {
        const remainingTime = Math.round((this.tokenExpireTime - now) / (60 * 1000)); // 剩余分钟
        // console.log(`Token有效，剩余约${remainingTime}分钟过期`);
      }
      
      return isExpired;
    },
    
    // 清除用户信息
    clearUserInfo() {
      this.userInfo = null
      this.tokenExpireTime = null
      
      // 清除所有相关的本地存储
      try {
        uni.removeStorageSync('userInfo')
        uni.removeStorageSync('tokenExpireTime')
        console.log('已清除用户信息和token过期时间')
      } catch (e) {
        console.error('清除本地存储失败:', e)
      }
    },
    
    // 登录
    async login(loginParams) {
      try {
        // 调用登录API
        const res = await queryLogin(loginParams)
        
        // 检查登录结果
        if (res && res.Msg === 'Success') {
          console.log('登录API返回成功:', res)
          // 登录成功，保存用户信息
          const userInfo = {
            userId: res.UserID,
            userName: res.UserName || loginParams.User,
            // 根据实际接口返回补充其他字段
          }
          
          // 设置用户信息前先清除旧数据
          this.clearUserInfo()
          
          // 设置新的用户信息
          this.setUserInfo(userInfo)
          
          // 设置token过期时间（12小时后过期）
          this.setTokenExpireTime()
          
          return { success: true }
        } else {
          // 登录失败
          return { 
            success: false, 
            message: res?.Msg === 'NoUser' ? '用户名或密码错误' : (res?.Msg || '登录失败') 
          }
        }
      } catch (error) {
        console.error('登录失败:', error)
        return { 
          success: false, 
          message: '登录失败，请检查网络连接'
        }
      }
    },
    
    // 登出
    async logout() {
      try {
        const res = await uni.showModal({
          title: "安全提示",
          content: "确定要退出登录吗？",
          confirmText: "退出登录",
          cancelText: "取消",
          confirmColor: "#e74c3c"
        })
        
        if (res.confirm) {
          // 清除用户数据
          this.clearUserInfo()
          return true
        }
        return false
      } catch (error) {
        console.error('登出失败:', error)
        return false
      }
    }
  }
}) 
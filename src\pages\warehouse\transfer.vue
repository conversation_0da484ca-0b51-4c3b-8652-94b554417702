<template>
  <view class="container">
    <!-- 使用uni-nav-bar组件 -->
    <view class="nav-bar-container">
      <uni-nav-bar
        :fixed="true"
        :status-bar="true"
        title="库存调拨"
        left-icon="left"
        @clickLeft="goBack"
        :border="false"
      />
    </view>
    
    <!-- 主体内容区 -->
    <view class="main-content">
      <!-- 扫码和输入区域 -->
      <view class="scan-container">
        <view class="scan-icon-btn" @click="handleScan">
          <image class="scan-icon-left" src="/static/icons/scan.png" mode="aspectFit"></image>
        </view>
        <view class="input-wrapper" :class="{ 'input-focus': isInputFocused }">
          <input 
            class="code-input" 
            type="text" 
            v-model="codeInput" 
            placeholder="扫描或输入产品条码" 
            @confirm="handleCodeSubmit"
            @focus="handleInputFocus"
            @blur="handleInputBlur"
          />
          <view v-if="codeInput" class="clear-icon" @click.stop="clearCodeInput">×</view>
        </view>
      </view>
      
      <!-- 调拨表单 -->
      <view class="transfer-form">
        <view class="form-title">调拨信息</view>
        
        <view class="form-item">
          <text class="form-label">产品编号</text>
          <view class="form-input-wrapper">
            <input 
              class="form-input" 
              type="text" 
              v-model="transferForm.productCode" 
              placeholder="产品编号"
              disabled
            />
          </view>
        </view>
        
        <view class="form-item">
          <text class="form-label">产品名称</text>
          <view class="form-input-wrapper">
            <input 
              class="form-input" 
              type="text" 
              v-model="transferForm.productName" 
              placeholder="产品名称"
              disabled
            />
          </view>
        </view>
        
        <view class="form-item">
          <text class="form-label">规格型号</text>
          <view class="form-input-wrapper">
            <input 
              class="form-input" 
              type="text" 
              v-model="transferForm.specification" 
              placeholder="规格型号"
              disabled
            />
          </view>
        </view>
        
        <view class="form-item">
          <text class="form-label">当前库存</text>
          <view class="form-input-wrapper">
            <input 
              class="form-input" 
              type="text" 
              v-model="transferForm.currentStock" 
              placeholder="当前库存"
              disabled
            />
          </view>
        </view>
        
        <view class="form-item">
          <text class="form-label">当前位置</text>
          <view class="form-input-wrapper">
            <input 
              class="form-input" 
              type="text" 
              v-model="transferForm.currentLocation" 
              placeholder="当前位置"
              disabled
            />
          </view>
        </view>
        
        <view class="form-item">
          <text class="form-label">调拨数量</text>
          <view class="form-input-wrapper">
            <input 
              class="form-input" 
              type="number" 
              v-model="transferForm.quantity" 
              placeholder="请输入调拨数量"
            />
          </view>
        </view>
        
        <view class="form-item">
          <text class="form-label">目标位置</text>
          <view class="form-input-wrapper">
            <picker 
              class="form-picker" 
              :value="targetLocationIndex" 
              :range="locationOptions" 
              @change="handleTargetLocationChange"
            >
              <view class="picker-text">{{ locationOptions[targetLocationIndex] || '请选择目标位置' }}</view>
            </picker>
          </view>
        </view>
        
        <view class="form-item">
          <text class="form-label">调拨原因</text>
          <view class="form-input-wrapper">
            <picker 
              class="form-picker" 
              :value="transferReasonIndex" 
              :range="transferReasons" 
              @change="handleTransferReasonChange"
            >
              <view class="picker-text">{{ transferReasons[transferReasonIndex] || '请选择调拨原因' }}</view>
            </picker>
          </view>
        </view>
        
        <view class="form-item">
          <text class="form-label">备注</text>
          <view class="form-input-wrapper">
            <textarea 
              class="form-textarea" 
              v-model="transferForm.remark" 
              placeholder="请输入备注信息"
              maxlength="200"
            />
          </view>
        </view>
      </view>
      
      <!-- 提交按钮 -->
      <view class="submit-container">
        <button class="submit-btn" @click="submitTransfer">确认调拨</button>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, reactive } from 'vue';
import { onLoad } from '@dcloudio/uni-app';
import { useUserStore } from '@/store/user';
import scanCodeUtil from '@/utils/scanCode';

// 导入uni-nav-bar组件
import uniNavBar from '@dcloudio/uni-ui/lib/uni-nav-bar/uni-nav-bar.vue'

// 用户状态管理
const userStore = useUserStore();

// 输入框内容
const codeInput = ref('');

// 输入框焦点状态
const isInputFocused = ref(false);

// 调拨表单数据
const transferForm = reactive({
  productCode: '',
  productName: '',
  specification: '',
  currentStock: '',
  currentLocation: '',
  quantity: '',
  targetLocation: '',
  transferReason: '',
  remark: ''
});

// 仓库位置选项
const locationOptions = ref(['A区-01', 'A区-02', 'B区-01', 'B区-02', 'C区-01', 'C区-02']);
const targetLocationIndex = ref(0);

// 调拨原因选项
const transferReasons = ref(['库位整理', '库存集中', '临时存放', '其他原因']);
const transferReasonIndex = ref(0);

// 处理页面加载
onLoad((options) => {
  // 如果有传入的条码参数，直接处理
  if (options.code) {
    processCode(decodeURIComponent(options.code));
  }
});

// 处理输入框获得焦点
const handleInputFocus = () => {
  isInputFocused.value = true;
};

// 处理输入框失去焦点
const handleInputBlur = () => {
  isInputFocused.value = false;
};

// 处理输入提交
const handleCodeSubmit = () => {
  if (!codeInput.value.trim()) {
    uni.showToast({
      title: '请输入条码',
      icon: 'none'
    });
    return;
  }
  
  // 处理条码
  processCode(codeInput.value.trim());
  
  // 清空输入框
  codeInput.value = '';
};

// 清除输入框
const clearCodeInput = () => {
  codeInput.value = '';
};

// 扫码功能
const handleScan = async () => {
  try {
    // 使用scanCode.js中的扫码功能
    const result = await scanCodeUtil.start({
      preserveGS1Separators: true
    });
    
    // 处理扫描结果
    processCode(result);
  } catch (error) {
    uni.showToast({
      title: '扫码失败',
      icon: 'none'
    });
    console.error('扫码失败:', error);
  }
};

// 处理条码
const processCode = (code) => {
  // 模拟获取产品信息
  // TODO: 实际项目中应该调用API获取产品信息
  setTimeout(() => {
    // 模拟产品数据
    const productInfo = {
      productCode: code,
      productName: '测试产品',
      specification: '规格型号123',
      currentStock: '100个',
      currentLocation: 'A区-01'
    };
    
    // 更新表单数据
    transferForm.productCode = productInfo.productCode;
    transferForm.productName = productInfo.productName;
    transferForm.specification = productInfo.specification;
    transferForm.currentStock = productInfo.currentStock;
    transferForm.currentLocation = productInfo.currentLocation;
    
    // 设置默认目标位置（不能与当前位置相同）
    const currentLocationIndex = locationOptions.value.findIndex(loc => loc === productInfo.currentLocation);
    if (currentLocationIndex !== -1) {
      // 选择下一个位置，如果是最后一个则选择第一个
      targetLocationIndex.value = (currentLocationIndex + 1) % locationOptions.value.length;
      transferForm.targetLocation = locationOptions.value[targetLocationIndex.value];
    }
    
    uni.showToast({
      title: '已获取产品信息',
      icon: 'success'
    });
  }, 500);
};

// 处理目标位置变更
const handleTargetLocationChange = (e) => {
  targetLocationIndex.value = e.detail.value;
  transferForm.targetLocation = locationOptions.value[targetLocationIndex.value];
  
  // 检查是否与当前位置相同
  if (transferForm.targetLocation === transferForm.currentLocation) {
    uni.showToast({
      title: '目标位置不能与当前位置相同',
      icon: 'none'
    });
  }
};

// 处理调拨原因变更
const handleTransferReasonChange = (e) => {
  transferReasonIndex.value = e.detail.value;
  transferForm.transferReason = transferReasons.value[transferReasonIndex.value];
};

// 提交调拨
const submitTransfer = () => {
  // 表单验证
  if (!transferForm.productCode) {
    uni.showToast({
      title: '请先扫描产品条码',
      icon: 'none'
    });
    return;
  }
  
  if (!transferForm.quantity || transferForm.quantity <= 0) {
    uni.showToast({
      title: '请输入有效的调拨数量',
      icon: 'none'
    });
    return;
  }
  
  if (transferForm.targetLocation === transferForm.currentLocation) {
    uni.showToast({
      title: '目标位置不能与当前位置相同',
      icon: 'none'
    });
    return;
  }
  
  // 确保调拨原因已选择
  transferForm.transferReason = transferReasons.value[transferReasonIndex.value];
  
  // 显示确认对话框
  uni.showModal({
    title: '确认调拨',
    content: `确定要将 ${transferForm.quantity} 个 ${transferForm.productName} 从 ${transferForm.currentLocation} 调拨到 ${transferForm.targetLocation} 吗？`,
    success: (res) => {
      if (res.confirm) {
        // 执行调拨操作
        // TODO: 实际项目中应该调用API执行调拨操作
        setTimeout(() => {
          uni.showToast({
            title: '调拨成功',
            icon: 'success'
          });
          
          // 重置表单
          resetForm();
        }, 1000);
      }
    }
  });
};

// 重置表单
const resetForm = () => {
  transferForm.productCode = '';
  transferForm.productName = '';
  transferForm.specification = '';
  transferForm.currentStock = '';
  transferForm.currentLocation = '';
  transferForm.quantity = '';
  transferForm.targetLocation = '';
  transferForm.remark = '';
  targetLocationIndex.value = 0;
  transferReasonIndex.value = 0;
};

// 返回上一页
const goBack = () => {
  uni.navigateBack({
    delta: 1
  });
};
</script>

<style scoped>
/* 容器样式 */
.container {
  height: 100vh;
  width: 100%;
  display: flex;
  flex-direction: column;
  background-color: #f7f9fc;
  position: relative;
  overflow: hidden;
  margin: 0;
  border: none;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, sans-serif;
}

/* 导航栏容器 */
.nav-bar-container {
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 100;
}

/* 主体内容区域 */
.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  width: 100%;
  overflow-y: auto;
  position: relative;
  padding: 0 15px;
  margin-top: 10px;
}

/* 扫码和输入区域 */
.scan-container {
  display: flex;
  align-items: center;
  margin: 10px 0;
  width: 100%;
}

.scan-icon-btn {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 10px;
}

.scan-icon-left {
  width: 24px;
  height: 24px;
}

.input-wrapper {
  flex: 1;
  display: flex;
  align-items: center;
  height: 36px;
  border-radius: 8px;
  border: 1px solid #d1d5db;
  background-color: #fff;
  padding: 0 10px;
  position: relative;
  transition: all 0.3s ease;
}

.input-focus {
  border-color: #2196F3;
  box-shadow: 0 0 0 2px rgba(33, 150, 243, 0.1);
}

.code-input {
  flex: 1;
  height: 100%;
  border: none;
  outline: none;
  font-size: 14px;
  color: #333;
}

.clear-icon {
  font-size: 16px;
  color: #999;
  padding: 4px;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
}

/* 调拨表单样式 */
.transfer-form {
  margin: 15px 0;
  background-color: #fff;
  border-radius: 12px;
  padding: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.form-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 15px;
}

.form-item {
  margin-bottom: 15px;
}

.form-label {
  display: block;
  font-size: 14px;
  color: #666;
  margin-bottom: 5px;
}

.form-input-wrapper {
  border: 1px solid #d1d5db;
  border-radius: 8px;
  background-color: #fff;
  overflow: hidden;
}

.form-input {
  width: 100%;
  height: 40px;
  padding: 0 10px;
  font-size: 14px;
  color: #333;
}

.form-input[disabled] {
  background-color: #f9f9f9;
  color: #666;
}

.form-textarea {
  width: 100%;
  height: 80px;
  padding: 10px;
  font-size: 14px;
  color: #333;
}

.form-picker {
  width: 100%;
  height: 40px;
}

.picker-text {
  height: 40px;
  line-height: 40px;
  padding: 0 10px;
  font-size: 14px;
  color: #333;
}

/* 提交按钮样式 */
.submit-container {
  margin: 20px 0;
  padding: 0 15px;
}

.submit-btn {
  width: 100%;
  height: 44px;
  line-height: 44px;
  background-color: #2196F3;
  color: #fff;
  font-size: 16px;
  border-radius: 8px;
  text-align: center;
}
</style> 
/**
 * 通用扫码工具类
 * 兼容原生安卓My-ScanCode插件和uni.scanCode API
 * 支持保留GS1二维码中的分隔符
 */
export default {
  /**
   * 开始扫码
   * @param {Object} options 扫码选项
   * @param {Boolean} options.preserveGS1Separators 是否保留GS1分隔符，默认为true
   * @returns {Promise<string>} 返回扫码结果
   */
  start: function(options = { preserveGS1Separators: true }) {
    return new Promise((resolve, reject) => {
      // #ifdef APP-PLUS
      // 检查是否为Android平台且My-ScanCode插件可用
      try {
        if (uni.getSystemInfoSync().platform === 'android') {
          try {
            const myScanCode = uni.requireNativePlugin('My-ScanCode');
            if (myScanCode && typeof myScanCode.scanCode === 'function') {
              const scanOptions = {
                scanType: ['CODE_128', 'QR_CODE', 'DATA_MATRIX'], // 添加DATA_MATRIX支持GS1 DataMatrix
                prompt: '提示：将条形码/二维码图片对准扫描框即可自动扫描',
                locked: true,
                // 添加GS1分隔符保留选项
                preserveGS1Separators: options.preserveGS1Separators
              };
              
              myScanCode.scanCode(scanOptions, res => {
                if (res.success === 'true') {
                  // 如果需要保留GS1分隔符，处理扫码结果
                  if (options.preserveGS1Separators) {
                    // 将ASCII 29 (GS分组分隔符)替换为可见的分隔符表示
                    // 在结果中使用^表示GS分隔符
                    let result = res.result;
                    // 检查是否包含ASCII 29 (GS分组分隔符)
                    if (result.indexOf(String.fromCharCode(29)) !== -1) {
                      result = result.replace(/\u001d/g, '^');
                    }
                    resolve(result);
                  } else {
                    resolve(res.result);
                  }
                } else {
                  reject(new Error('扫码失败: ' + (res.message || '未知错误')));
                }
              });
              return; // 成功调用插件后返回
            }
          } catch (pluginError) {
            console.error('插件加载失败，回退到uni.scanCode:', pluginError);
            // 插件加载失败，回退到uni.scanCode
          }
        }
      } catch (platformError) {
        console.error('平台检测失败，回退到uni.scanCode:', platformError);
        // 平台检测失败，回退到uni.scanCode
      }
      
      // 如果插件不可用或非Android平台，使用uni.scanCode
      uni.scanCode({
        onlyFromCamera: true,
        scanType: ['barCode', 'qrCode', 'datamatrix'], // 添加datamatrix支持GS1 DataMatrix
        success(res) {
          // 如果需要保留GS1分隔符，处理扫码结果
          if (options.preserveGS1Separators) {
            // 将ASCII 29 (GS分组分隔符)替换为可见的分隔符表示
            // 在结果中使用^表示GS分隔符
            let result = res.result;
            // 检查是否包含ASCII 29 (GS分组分隔符)
            if (result.indexOf(String.fromCharCode(29)) !== -1) {
              result = result.replace(/\u001d/g, '^');
            }
            resolve(result);
          } else {
            resolve(res.result);
          }
        },
        fail(err) {
          reject(err);
        }
      });
      // #endif
      
      // #ifndef APP-PLUS
      // 非APP环境，直接使用uni.scanCode
      uni.scanCode({
        onlyFromCamera: true,
        scanType: ['barCode', 'qrCode', 'datamatrix'], // 添加datamatrix支持GS1 DataMatrix
        success(res) {
          // 如果需要保留GS1分隔符，处理扫码结果
          if (options.preserveGS1Separators) {
            // 将ASCII 29 (GS分组分隔符)替换为可见的分隔符表示
            // 在结果中使用^表示GS分隔符
            let result = res.result;
            // 检查是否包含ASCII 29 (GS分组分隔符)
            if (result.indexOf(String.fromCharCode(29)) !== -1) {
              result = result.replace(/\u001d/g, '^');
            }
            resolve(result);
          } else {
            resolve(res.result);
          }
        },
        fail(err) {
          reject(err);
        }
      });
      // #endif
    });
  },
  
  /**
   * 解析特殊GS1 UDI字符串
   * @param {String} udiString 包含[GS]和[CR]标记的UDI字符串
   * @returns {Object} 解析后的UDI数据对象，包含各个应用标识符对应的值
   */
  parseSpecialGS1UDI: function(udiString) {
    // 将分组分隔符和回车符替换为特殊字符以便于解析
    const normalizedString = udiString.replace(/\[GS\]/g, '\u001D').replace(/\[CR\]/g, '\u000D');
    
    // 定义已知的应用标识符(AI)及其格式
    const aiDefinitions = {
      '01': { name: 'UDI', length: 14, type: 'fixed' },
      '10': { name: 'BatchNo', length: null, type: 'variable', maxLength: 20 },
      '11': { name: 'PrdDate', length: 6, type: 'fixed' },
      '17': { name: 'ExpDate', length: 6, type: 'fixed' },
      '21': { name: 'SerialNo', length: null, type: 'variable', maxLength: 20 },
      '240': { name: 'AdditionalId', length: null, type: 'variable', maxLength: 30 },
      '241': { name: 'CustomerPartNo', length: null, type: 'variable', maxLength: 30 }
    };
    
    const result = {};
    let currentPos = 0;
    
    // 持续解析直到到达字符串末尾
    while (currentPos < normalizedString.length) {
      // 检查是否已到达控制字符
      if (normalizedString[currentPos] === '\u001D' || normalizedString[currentPos] === '\u000D') {
        currentPos++;
        continue;
      }
      
      // 尝试在当前位置识别AI
      let aiFound = false;
      
      // 首先尝试3位数AI（如240, 241）
      let potentialAI = normalizedString.substring(currentPos, currentPos + 3);
      if (aiDefinitions[potentialAI]) {
        aiFound = true;
        const aiDef = aiDefinitions[potentialAI];
        currentPos += 3; // 跳过AI部分
        
        if (aiDef.type === 'fixed') {
          // 对于固定长度字段，提取指定长度
          result[aiDef.name] = normalizedString.substring(currentPos, currentPos + aiDef.length);
          currentPos += aiDef.length;
        } else {
          // 对于可变长度字段，读取直到下一个分隔符或字符串结束
          let endPos = normalizedString.indexOf('\u001D', currentPos);
          if (endPos === -1) {
            endPos = normalizedString.indexOf('\u000D', currentPos);
          }
          if (endPos === -1) {
            endPos = normalizedString.length;
          }
          result[aiDef.name] = normalizedString.substring(currentPos, endPos);
          currentPos = endPos;
        }
      }
      
      // 如果没有找到3位数AI，尝试2位数AI（如01, 10, 11等）
      if (!aiFound) {
        potentialAI = normalizedString.substring(currentPos, currentPos + 2);
        if (aiDefinitions[potentialAI]) {
          const aiDef = aiDefinitions[potentialAI];
          currentPos += 2; // 跳过AI部分
          
          if (aiDef.type === 'fixed') {
            // 对于固定长度字段，提取指定长度
            result[aiDef.name] = normalizedString.substring(currentPos, currentPos + aiDef.length);
            currentPos += aiDef.length;
          } else {
            // 对于可变长度字段，读取直到下一个分隔符或字符串结束
            let endPos = normalizedString.indexOf('\u001D', currentPos);
            if (endPos === -1) {
              endPos = normalizedString.indexOf('\u000D', currentPos);
            }
            if (endPos === -1) {
              endPos = normalizedString.length;
            }
            result[aiDef.name] = normalizedString.substring(currentPos, endPos);
            currentPos = endPos;
          }
        } else {
          // 如果在当前位置找不到有效的AI，移动到下一个字符
          currentPos++;
        }
      }
    }  
    return result;
  }
}; 
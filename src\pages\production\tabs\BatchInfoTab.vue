<template>
  <view class="tab-pane">
    <view class="info-card">
      <view class="card-body">
        <view v-if="batchInfo.length === 0" class="empty-tip">暂无上层批次信息</view>
        <view v-else class="batch-list">
          <view v-for="(item, index) in batchInfo" :key="index" class="batch-item">
            <view class="batch-header">
              <text class="batch-title">{{ item.batchNo || '未知批次' }}</text>
            </view>
            <view class="batch-body">
              <view class="batch-info">
                <text class="batch-label">物料名称</text>
                <text class="batch-value">{{ item.materialName || '-' }}</text>
              </view>
              <view class="batch-info">
                <text class="batch-label">批次数量</text>
                <text class="batch-value">{{ item.quantity || '-' }}</text>
              </view>
              <view class="batch-info">
                <text class="batch-label">生产日期</text>
                <text class="batch-value">{{ item.productionDate || '-' }}</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
// 定义props接收父组件传递的数据
const props = defineProps({
  batchInfo: {
    type: Array,
    default: () => []
  }
});
</script>

<style scoped>
/* 卡片样式 */
.info-card {
  background-color: #ffffff;
  border-radius: 10px;
  margin-bottom: 10px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.card-body {
  padding: 15px;
}

/* 空数据提示 */
.empty-tip {
  text-align: center;
  padding: 30px 0;
  color: #9ca3af;
  font-size: 14px;
}

/* 批次信息样式 */
.batch-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.batch-item {
  background-color: #f8fafc;
  border-radius: 8px;
  padding: 12px;
  border: 1px solid #e2e8f0;
}

.batch-header {
  margin-bottom: 8px;
  padding-bottom: 6px;
  border-bottom: 1px solid #e2e8f0;
}

.batch-title {
  font-size: 14px;
  font-weight: 600;
  color: #0f172a;
}

.batch-body {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
}

.batch-info {
  display: flex;
  flex-direction: column;
}

.batch-label {
  font-size: 11px;
  color: #64748b;
  margin-bottom: 2px;
}

.batch-value {
  font-size: 13px;
  color: #334155;
}

.tab-pane {
  height: 100%;
}
</style> 
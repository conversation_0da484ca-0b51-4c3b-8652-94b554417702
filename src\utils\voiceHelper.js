/**
 * 语音播报辅助工具
 * 封装android-utils的TTS功能，提供语音播报服务
 */

// #ifdef APP-PLUS
import { AndroidTTSVoice } from '@/uni_modules/android-utils';
// #endif

// 当前TTS实例
let currentTTS = null;

/**
 * 判断是否在真实APP环境下运行（非模拟器）
 * @returns {boolean} 是否为真实设备环境
 */
const isRealAppEnvironment = () => {
  // #ifdef APP-PLUS
  try {
    // 检查是否为真实设备环境
    return plus && plus.device && plus.device.model && !plus.device.model.toLowerCase().includes('simulator');
  } catch (error) {
    console.error('检查设备环境出错:', error);
    return false;
  }
  // #endif
  
  // #ifndef APP-PLUS
  return false;
  // #endif
};

/**
 * 停止当前语音播报
 */
export const stopSpeaking = () => {
  if (currentTTS) {
    try {
      currentTTS.stop();
      currentTTS = null;
    } catch (error) {
      console.error('停止语音播报出错:', error);
    }
  }
};

/**
 * 播报文本内容
 * @param {string} text 要播报的文本内容
 * @param {Function} callback 播报完成后的回调函数（可选）
 * @returns {Promise<boolean>} 是否成功启动播报
 */
export const speakText = (text, callback) => {
  return new Promise((resolve) => {
    // #ifdef APP-PLUS
    try {
      // 先停止之前的播报
      stopSpeaking();
      
      // 检查是否为真实APP环境
      if (!isRealAppEnvironment()) {
        console.log('模拟器环境，跳过AndroidTTSVoice实例化');
        uni.showToast({
          title: '模拟器环境不支持此功能',
          icon: 'none'
        });
        resolve(false);
        return;
      }
      
      // 创建新的TTS实例
      const tts = new AndroidTTSVoice(state => {
        if (state) {
          try {
            tts.listenerVoiceState((b) => {
              if (b != 0) {
                try {
                  tts.stop();
                  if (typeof callback === 'function') {
                    callback();
                  }
                } catch (e) {
                  console.error('停止语音出错:', e);
                }
              }
            });
            tts.speak(text);
            currentTTS = tts; // 保存实例引用
            resolve(true);
          } catch (error) {
            console.error('AndroidTTSVoice执行出错:', error);
            uni.showToast({
              title: '语音播报失败',
              icon: 'none'
            });
            resolve(false);
          }
        } else {
          uni.showToast({
            title: '语音引擎初始化失败',
            icon: 'none'
          });
          resolve(false);
        }
      });
    } catch (error) {
      console.error('AndroidTTSVoice创建出错:', error);
      uni.showToast({
        title: '语音播报初始化失败',
        icon: 'none'
      });
      resolve(false);
    }
    // #endif
    
    // #ifndef APP-PLUS
    uni.showToast({
      title: '仅支持APP环境',
      icon: 'none'
    });
    resolve(false);
    // #endif
  });
};

/**
 * 设置语音模式
 * @param {number} mode 模式：0-暂停后输出，1-播放结束后播放
 */
export const setVoiceMode = (mode) => {
  if (currentTTS) {
    try {
      currentTTS.setMode(mode);
    } catch (error) {
      console.error('设置语音模式出错:', error);
    }
  }
};

/**
 * 获取可用语音名称列表
 * @returns {string[]} 语音名称列表
 */
export const getVoiceNames = () => {
  if (currentTTS) {
    try {
      return currentTTS.getVoiceNames();
    } catch (error) {
      console.error('获取语音名称列表出错:', error);
      return [];
    }
  }
  return [];
};

/**
 * 设置语音名称
 * @param {string} name 语音名称
 */
export const setVoiceName = (name) => {
  if (currentTTS && name) {
    try {
      currentTTS.setVoiceName(name);
    } catch (error) {
      console.error('设置语音名称出错:', error);
    }
  }
};

/**
 * 设置语音速度
 * @param {number} speed 语速(0-1)
 */
export const setVoiceSpeed = (speed) => {
  if (currentTTS && typeof speed === 'number') {
    try {
      currentTTS.setSpeed(speed);
    } catch (error) {
      console.error('设置语音速度出错:', error);
    }
  }
};

/**
 * 检查是否正在播放语音
 * @returns {boolean} 是否正在播放
 */
export const isSpeaking = () => {
  if (currentTTS) {
    try {
      return currentTTS.isSpeaking();
    } catch (error) {
      console.error('检查语音播放状态出错:', error);
      return false;
    }
  }
  return false;
};

export default {
  speakText,
  stopSpeaking,
  setVoiceMode,
  getVoiceNames,
  setVoiceName,
  setVoiceSpeed,
  isSpeaking
}; 
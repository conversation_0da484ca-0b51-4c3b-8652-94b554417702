/**
 * 文件存储辅助工具
 * 封装APP环境下文件存储相关功能，提供日志记录、文件保存等服务
 * 
 * 文件保存说明：
 * 1. 文件默认保存在应用沙盒的documents目录下：_doc/logs/[模块名]/[日期].log
 * 2. Android设备对应路径: /storage/emulated/0/Android/data/[应用包名]/files/documents/logs/[模块名]/[日期].log
 * 3. iOS设备对应路径: /var/mobile/Containers/Data/Application/[应用ID]/Documents/logs/[模块名]/[日期].log
 */

// 默认日志目录
const DEFAULT_LOG_DIR = '_doc/logs/';
// 存储权限状态
let hasStoragePermission = false;
// 标记是否已初始化
let isInitialized = false;
// 标记是否为Web环境
const isWebEnvironment = typeof window !== 'undefined' && !window.plus;

/**
 * 初始化文件系统
 * 确保必要的目录结构存在
 * @returns {Promise<boolean>} 初始化是否成功
 */
export const initFileSystem = async () => {
  // 如果已初始化，直接返回
  if (isInitialized) {
    return true;
  }
  
  // Web环境下，不执行文件系统初始化
  // #ifdef H5
  return false;
  // #endif
  
  // 检查权限
  if (!hasStoragePermission) {
    const permissionGranted = await requestStoragePermission();
    if (!permissionGranted) {
      console.error('没有文件存储权限，无法初始化文件系统');
      return false;
    }
  }
  
  // #ifdef APP-PLUS
  try {
    // 确保logs目录存在
    const result = await ensureDirectoryExists(DEFAULT_LOG_DIR);
    
    if (result) {
      console.log('文件系统初始化成功，日志目录已创建');
      isInitialized = true;
      
      // 获取日志目录的绝对路径
      const absolutePath = await getAbsolutePath(DEFAULT_LOG_DIR);
      if (absolutePath) {
        console.log('日志目录绝对路径:', absolutePath);
      }
      
      return true;
    } else {
      console.error('创建日志目录失败');
      return false;
    }
  } catch (error) {
    console.error('初始化文件系统时出错:', error);
    return false;
  }
  // #endif
  
  // #ifndef APP-PLUS
  console.log('非APP环境不支持文件系统操作');
  return false;
  // #endif
};

/**
 * 检查文件存储权限
 * @returns {Promise<boolean>} 是否拥有权限
 */
export const checkStoragePermission = () => {
  return new Promise((resolve) => {
    // Web环境下，直接返回false
    // #ifdef H5
    hasStoragePermission = false;
    resolve(false);
    return;
    // #endif
    
    // #ifdef APP-PLUS
    try {
      // Android平台需要检查权限
      if (plus.os.name.toLowerCase() === 'android') {
        plus.android.requestPermissions(
          ['android.permission.WRITE_EXTERNAL_STORAGE', 'android.permission.READ_EXTERNAL_STORAGE'],
          function(result) {
            hasStoragePermission = result.granted.length === 2;
            resolve(hasStoragePermission);
          },
          function(error) {
            console.error('请求权限出错:', error);
            hasStoragePermission = false;
            resolve(false);
          }
        );
      } else {
        // iOS平台默认有权限访问应用沙盒
        hasStoragePermission = true;
        resolve(true);
      }
    } catch (error) {
      console.error('检查存储权限时出错:', error);
      hasStoragePermission = false;
      resolve(false);
    }
    // #endif
    
    // #ifndef APP-PLUS
    console.log('非APP环境不支持文件系统操作');
    hasStoragePermission = false;
    resolve(false);
    // #endif
  });
};

/**
 * 请求文件存储权限
 * @returns {Promise<boolean>} 是否获取到权限
 */
export const requestStoragePermission = async () => {
  // 如果已经有权限，直接返回true
  if (hasStoragePermission) {
    return true;
  }
  
  // 请求权限
  return await checkStoragePermission();
};

/**
 * 确保目录存在
 * @param {string} dirPath 目录路径
 * @returns {Promise<boolean>} 目录是否存在或创建成功
 */
const ensureDirectoryExists = (dirPath) => {
  return new Promise((resolve) => {
    // Web环境下，直接返回false
    // #ifdef H5
    resolve(false);
    return;
    // #endif
    
    // #ifdef APP-PLUS
    try {
      plus.io.resolveLocalFileSystemURL(dirPath, (entry) => {
        // 目录已存在
        resolve(true);
      }, () => {
        // 目录不存在，创建目录
        plus.io.resolveLocalFileSystemURL('_doc/', (root) => {
          // 创建子目录结构
          const dirs = dirPath.replace('_doc/', '').split('/').filter(Boolean);
          let currentPath = '_doc/';
          
          // 递归创建目录
          const createNextDir = (index) => {
            if (index >= dirs.length) {
              resolve(true);
              return;
            }
            
            currentPath += dirs[index] + '/';
            plus.io.resolveLocalFileSystemURL(currentPath, () => {
              // 当前层级目录已存在，继续创建下一层
              createNextDir(index + 1);
            }, () => {
              // 当前层级目录不存在，创建它
              plus.io.resolveLocalFileSystemURL(currentPath.substring(0, currentPath.lastIndexOf(dirs[index] + '/')), (entry) => {
                entry.getDirectory(dirs[index], { create: true }, () => {
                  createNextDir(index + 1);
                }, (error) => {
                  console.error('创建目录失败:', error);
                  resolve(false);
                });
              }, (error) => {
                console.error('解析父目录失败:', error);
                resolve(false);
              });
            });
          };
          
          createNextDir(0);
        }, (error) => {
          console.error('解析根目录失败:', error);
          resolve(false);
        });
      });
    } catch (error) {
      console.error('确保目录存在时出错:', error);
      resolve(false);
    }
    // #endif
    
    // #ifndef APP-PLUS
    // 非APP环境不支持文件系统操作
    console.log('非APP环境不支持文件系统操作');
    resolve(false);
    // #endif
  });
};

/**
 * 获取文件的绝对路径
 * @param {string} relativePath 相对路径，如 _doc/logs/production/2024-10-30.log
 * @returns {Promise<string|null>} 绝对路径或null
 */
export const getAbsolutePath = (relativePath) => {
  return new Promise((resolve) => {
    // Web环境下，直接返回null
    // #ifdef H5
    resolve(null);
    return;
    // #endif
    
    // #ifdef APP-PLUS
    try {
      plus.io.resolveLocalFileSystemURL(relativePath, (entry) => {
        // 获取文件的完整URL
        resolve(entry.fullPath || entry.toURL() || entry.toLocalURL());
      }, (error) => {
        console.error('解析文件路径失败:', error);
        resolve(null);
      });
    } catch (error) {
      console.error('获取绝对路径时出错:', error);
      resolve(null);
    }
    // #endif
    
    // #ifndef APP-PLUS
    console.log('非APP环境不支持文件系统操作');
    resolve(null);
    // #endif
  });
};

/**
 * 保存文本到文件
 * @param {string} content 要保存的内容
 * @param {string} filePath 文件路径，包括文件名
 * @returns {Promise<boolean>} 是否保存成功
 */
export const saveTextToFile = async (content, filePath) => {
  // Web环境下，直接返回false
  // #ifdef H5
  return false;
  // #endif
  
  // 检查权限
  if (!hasStoragePermission) {
    const permissionGranted = await requestStoragePermission();
    if (!permissionGranted) {
      console.error('没有文件存储权限，无法保存文件');
      // 在APP环境下才显示提示
      // #ifdef APP-PLUS
      uni.showToast({
        title: '没有文件存储权限，无法保存记录',
        icon: 'none',
        duration: 2000
      });
      // #endif
      return false;
    }
  }
  
  return new Promise((resolve) => {
    // #ifdef APP-PLUS
    try {
      // 确保目录存在
      const dirPath = filePath.substring(0, filePath.lastIndexOf('/') + 1);
      ensureDirectoryExists(dirPath).then((dirExists) => {
        if (!dirExists) {
          console.error('目录创建失败，无法保存文件');
          resolve(false);
          return;
        }
        
        // 写入文件
        plus.io.resolveLocalFileSystemURL(dirPath, (entry) => {
          entry.getFile(filePath.substring(filePath.lastIndexOf('/') + 1), { create: true }, (fileEntry) => {
            fileEntry.createWriter((writer) => {
              writer.onwrite = () => {
                console.log('文件写入成功:', filePath);
                
                // 获取并打印文件的绝对路径，方便用户查找
                getAbsolutePath(filePath).then(absolutePath => {
                  if (absolutePath) {
                    console.log('文件保存在:', absolutePath);
                  }
                });
                
                resolve(true);
              };
              writer.onerror = (error) => {
                console.error('文件写入失败:', error);
                resolve(false);
              };
              writer.write(content);
            }, (error) => {
              console.error('创建文件写入器失败:', error);
              resolve(false);
            });
          }, (error) => {
            console.error('获取文件失败:', error);
            resolve(false);
          });
        }, (error) => {
          console.error('解析目录失败:', error);
          resolve(false);
        });
      });
    } catch (error) {
      console.error('保存文件时出错:', error);
      resolve(false);
    }
    // #endif
    
    // #ifndef APP-PLUS
    console.log('非APP环境不支持文件系统操作');
    resolve(false);
    // #endif
  });
};

/**
 * 读取文本文件
 * @param {string} filePath 文件路径
 * @returns {Promise<string|null>} 文件内容或null（如果读取失败）
 */
export const readTextFile = (filePath) => {
  // Web环境下，直接返回null
  // #ifdef H5
  return Promise.resolve(null);
  // #endif
  
  // 检查权限
  if (!hasStoragePermission) {
    return Promise.resolve(null);
  }
  
  return new Promise((resolve) => {
    // #ifdef APP-PLUS
    try {
      plus.io.resolveLocalFileSystemURL(filePath, (entry) => {
        entry.file((file) => {
          const reader = new plus.io.FileReader();
          reader.onloadend = (e) => {
            resolve(e.target.result);
          };
          reader.onerror = (error) => {
            console.error('读取文件失败:', error);
            resolve(null);
          };
          reader.readAsText(file);
        }, (error) => {
          console.error('获取文件对象失败:', error);
          resolve(null);
        });
      }, (error) => {
        console.error('解析文件路径失败:', error);
        resolve(null);
      });
    } catch (error) {
      console.error('读取文件时出错:', error);
      resolve(null);
    }
    // #endif
    
    // #ifndef APP-PLUS
    console.log('非APP环境不支持文件系统操作');
    resolve(null);
    // #endif
  });
};

/**
 * 保存操作日志到文件
 * @param {Array} logs 日志数组，每项包含time和message
 * @param {string} [moduleName='production'] 模块名称
 * @returns {Promise<boolean>} 是否保存成功
 */
export const saveOperationLogs = async (logs, moduleName = 'production') => {
  if (!logs || !Array.isArray(logs) || logs.length === 0) {
    return false;
  }
  
  // Web环境下，只在控制台输出日志，不尝试保存文件
  // #ifdef H5
  console.log('Web环境下，操作日志：', logs);
  return true;
  // #endif
  
  // 确保文件系统已初始化
  if (!isInitialized) {
    const initResult = await initFileSystem();
    if (!initResult) {
      console.error('文件系统初始化失败，无法保存操作记录');
      // 在APP环境下才显示提示
      // #ifdef APP-PLUS
      uni.showToast({
        title: '文件系统初始化失败，无法保存记录',
        icon: 'none',
        duration: 2000
      });
      // #endif
      return false;
    }
  }
  
  // 检查并请求权限
  if (!hasStoragePermission) {
    const permissionGranted = await requestStoragePermission();
    if (!permissionGranted) {
      console.error('没有文件存储权限，无法保存操作记录');
      // 在APP环境下才显示提示
      // #ifdef APP-PLUS
      uni.showToast({
        title: '没有文件存储权限，无法保存操作记录',
        icon: 'none',
        duration: 2000
      });
      // #endif
      return false;
    }
  }
  
  // 处理模块名，确保符合格式要求
  // 检查模块名是否已包含中文日期格式
  if (!moduleName.includes('年') && !moduleName.includes('月') && !moduleName.includes('日')) {
    // 生成中文格式的日期
    const now = new Date();
    const chineseDate = `${now.getFullYear()}年${String(now.getMonth() + 1).padStart(2, '0')}月${String(now.getDate()).padStart(2, '0')}日`;
    
    // 如果是默认模块名或不包含日期格式，统一使用中文日期格式
    if (moduleName === 'production' || !moduleName || moduleName.startsWith('production_')) {
      moduleName = `production_${chineseDate}`;
    }
  }
  
  // 创建日志目录
  const logDir = DEFAULT_LOG_DIR + moduleName + '/';
  
  // 确保模块日志目录存在
  const dirExists = await ensureDirectoryExists(logDir);
  if (!dirExists) {
    console.error('创建模块日志目录失败:', logDir);
    // 在APP环境下才显示提示
    // #ifdef APP-PLUS
    uni.showToast({
      title: '创建日志目录失败',
      icon: 'none',
      duration: 2000
    });
    // #endif
    return false;
  }
  
  // 生成文件名，使用单一文件名，方便同一天的日志合并
  const fileName = `操作日志.log`;
  const filePath = logDir + fileName;
  
  // 读取现有日志（如果存在）
  let existingContent = await readTextFile(filePath);
  let content = '';
  
  if (existingContent) {
    // 如果文件已存在，添加新日志到现有内容后
    content = existingContent + '\n';
  }
  
  // 格式化日志内容，确保按时间倒序排列
  const formattedLogs = logs
    .sort((a, b) => {
      // 提取时间部分进行比较（格式：YYYY-MM-DD HH:MM:SS [用户]）
      const timeA = a.time.split(' [')[0];
      const timeB = b.time.split(' [')[0];
      return new Date(timeB) - new Date(timeA); // 降序排列
    })
    .map(log => {
      return `[${log.time}] ${log.message}`;
    })
    .join('\n');
  
  content += formattedLogs;
  
  // 保存到文件
  const saveResult = await saveTextToFile(content, filePath);
  
  // 如果保存成功，显示文件路径
  if (saveResult) {
    const absolutePath = await getAbsolutePath(filePath);
  }
  
  return saveResult;
};

/**
 * 获取指定日期的操作日志
 * @param {string} date 日期，格式：YYYY-MM-DD
 * @param {string} [moduleName='production'] 模块名称
 * @returns {Promise<Array|null>} 日志数组或null（如果读取失败）
 */
export const getOperationLogsByDate = async (date, moduleName = 'production') => {
  // Web环境下，返回空数组
  // #ifdef H5
  return [];
  // #endif
  
  const logDir = DEFAULT_LOG_DIR + moduleName + '/';
  const filePath = logDir + date + '.log';
  
  const content = await readTextFile(filePath);
  if (!content) {
    return null;
  }
  
  // 解析日志内容为数组
  const logs = [];
  const lines = content.split('\n').filter(line => line.trim());
  
  lines.forEach(line => {
    const match = line.match(/\[(.*?)\] (.*)/);
    if (match && match.length >= 3) {
      logs.push({
        time: match[1],
        message: match[2]
      });
    }
  });
  
  return logs;
};

/**
 * 获取所有可用的日志日期列表
 * @param {string} [moduleName='production'] 模块名称
 * @returns {Promise<Array>} 日期列表，格式：['2024-10-29', '2024-10-28', ...]
 */
export const getAvailableLogDates = async (moduleName = 'production') => {
  // Web环境下，返回空数组
  // #ifdef H5
  return [];
  // #endif
  
  // 确保文件系统已初始化
  if (!isInitialized) {
    await initFileSystem();
  }
  
  return new Promise((resolve) => {
    // #ifdef APP-PLUS
    try {
      const logDir = DEFAULT_LOG_DIR + moduleName + '/';
      
      // 先检查目录是否存在
      plus.io.resolveLocalFileSystemURL(logDir, (entry) => {
        const reader = entry.createReader();
        reader.readEntries((entries) => {
          // 过滤出.log文件并提取日期
          const dates = entries
            .filter(entry => entry.isFile && entry.name.endsWith('.log'))
            .map(entry => entry.name.replace('.log', ''))
            .sort((a, b) => b.localeCompare(a)); // 按日期降序排序
          
          resolve(dates);
        }, (error) => {
          console.error('读取目录失败:', error);
          resolve([]);
        });
      }, (error) => {
        console.error('解析日志目录失败，可能目录不存在:', error);
        // 尝试创建目录
        ensureDirectoryExists(logDir).then(() => {
          resolve([]);
        });
      });
    } catch (error) {
      console.error('获取日志日期列表时出错:', error);
      resolve([]);
    }
    // #endif
    
    // #ifndef APP-PLUS
    console.log('非APP环境不支持文件系统操作');
    resolve([]);
    // #endif
  });
};

/**
 * 打开日志文件
 * @param {string} date 日期，格式：YYYY-MM-DD
 * @param {string} [moduleName='production'] 模块名称
 */
export const openLogFile = async (date, moduleName = 'production') => {
  // Web环境下，显示不支持的提示
  // #ifdef H5
  uni.showToast({
    title: 'Web环境不支持打开文件',
    icon: 'none',
    duration: 2000
  });
  return;
  // #endif
  
  // #ifdef APP-PLUS
  try {
    const logDir = DEFAULT_LOG_DIR + moduleName + '/';
    const filePath = logDir + date + '.log';
    
    // 获取文件绝对路径
    const absolutePath = await getAbsolutePath(filePath);
    if (!absolutePath) {
      uni.showToast({
        title: '找不到日志文件',
        icon: 'none'
      });
      return;
    }
    
    // 尝试打开文件
    if (plus.os.name.toLowerCase() === 'android') {
      // Android平台
      const Intent = plus.android.importClass("android.content.Intent");
      const File = plus.android.importClass("java.io.File");
      const FileProvider = plus.android.importClass("androidx.core.content.FileProvider");
      const Uri = plus.android.importClass("android.net.Uri");
      
      const mainActivity = plus.android.runtimeMainActivity();
      const intent = new Intent(Intent.ACTION_VIEW);
      
      // 使用FileProvider获取文件URI
      const file = new File(absolutePath);
      const uri = FileProvider.getUriForFile(
        mainActivity,
        plus.runtime.appid + ".fileprovider",
        file
      );
      
      intent.setDataAndType(uri, "text/plain");
      intent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION);
      
      mainActivity.startActivity(intent);
    } else {
      // iOS平台
      plus.runtime.openFile(absolutePath);
    }
  } catch (error) {
    console.error('打开日志文件出错:', error);
    uni.showToast({
      title: '无法打开日志文件',
      icon: 'none'
    });
  }
  // #endif
  
  // #ifndef APP-PLUS
  console.log('非APP环境不支持打开文件');
  // #endif
};

/**
 * 查看最近的操作日志
 */
export const viewRecentLogs = async () => {
  // Web环境下，显示不支持的提示
  // #ifdef H5
  uni.showToast({
    title: 'Web环境不支持查看日志文件',
    icon: 'none',
    duration: 2000
  });
  return;
  // #endif
  
  // 获取可用的日志日期
  const dates = await getAvailableLogDates();
  
  if (dates.length === 0) {
    uni.showToast({
      title: '没有找到日志文件',
      icon: 'none'
    });
    return;
  }
  
  // 获取最近的日志文件路径
  const mostRecentDate = dates[0]; // 日期是降序排列的
  const logDir = DEFAULT_LOG_DIR + 'production/';
  const filePath = logDir + mostRecentDate + '.log';
  
  // 获取并显示文件绝对路径
  const absolutePath = await getAbsolutePath(filePath);
  if (absolutePath) {
    uni.showModal({
      title: '最近的日志文件',
      content: `日期: ${mostRecentDate}\n路径: ${absolutePath}`,
      confirmText: '打开',
      success: (res) => {
        if (res.confirm) {
          openLogFile(mostRecentDate);
        }
      }
    });
  } else {
    uni.showToast({
      title: '找不到日志文件',
      icon: 'none'
    });
  }
};

/**
 * 获取所有模块的日志文件列表
 * @returns {Promise<Array>} 日志文件列表，格式：[{module: '模块名', date: '日期', path: '文件路径'}]
 */
export const getAllLogFiles = async () => {
  // Web环境下，返回空数组
  // #ifdef H5
  return [];
  // #endif
  
  // 确保文件系统已初始化
  if (!isInitialized) {
    await initFileSystem();
  }
  
  return new Promise((resolve) => {
    // #ifdef APP-PLUS
    try {
      // 先获取logs目录下的所有模块文件夹
      plus.io.resolveLocalFileSystemURL(DEFAULT_LOG_DIR, async (entry) => {
        const reader = entry.createReader();
        reader.readEntries(async (entries) => {
          // 过滤出文件夹
          const moduleFolders = entries.filter(entry => entry.isDirectory);
          
          if (moduleFolders.length === 0) {
            resolve([]);
            return;
          }
          
          // 存储所有日志文件信息
          const allLogFiles = [];
          
          // 遍历每个模块文件夹
          for (const moduleEntry of moduleFolders) {
            const moduleName = moduleEntry.name;
            
            try {
              // 获取该模块下的所有日志文件
              const moduleReader = moduleEntry.createReader();
              const logFiles = await new Promise((resolveFiles) => {
                moduleReader.readEntries((fileEntries) => {
                  resolveFiles(fileEntries);
                }, (error) => {
                  console.error(`读取${moduleName}目录失败:`, error);
                  resolveFiles([]);
                });
              });
              
              // 过滤出.log文件并添加到结果中
              logFiles
                .filter(file => file.isFile && file.name.endsWith('.log'))
                .forEach(file => {
                  allLogFiles.push({
                    module: moduleName,
                    date: file.name.replace('.log', ''),
                    path: `${DEFAULT_LOG_DIR}${moduleName}/${file.name}`
                  });
                });
            } catch (error) {
              console.error(`处理${moduleName}模块日志时出错:`, error);
            }
          }
          
          // 按日期降序排序
          allLogFiles.sort((a, b) => b.date.localeCompare(a.date));
          
          resolve(allLogFiles);
        }, (error) => {
          console.error('读取日志根目录失败:', error);
          resolve([]);
        });
      }, (error) => {
        console.error('解析日志根目录失败，可能目录不存在:', error);
        // 尝试创建日志根目录
        ensureDirectoryExists(DEFAULT_LOG_DIR).then(() => {
          resolve([]);
        });
      });
    } catch (error) {
      console.error('获取所有日志文件列表时出错:', error);
      resolve([]);
    }
    // #endif
    
    // #ifndef APP-PLUS
    console.log('非APP环境不支持文件系统操作');
    resolve([]);
    // #endif
  });
};

export default {
  saveTextToFile,
  readTextFile,
  saveOperationLogs,
  getOperationLogsByDate,
  getAvailableLogDates,
  getAbsolutePath,
  openLogFile,
  viewRecentLogs,
  getAllLogFiles,
  initFileSystem
}; 
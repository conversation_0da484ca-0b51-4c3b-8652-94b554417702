<template>
  <view class="container" @touchmove.prevent>
    <!-- H5 环境下的标题栏 -->
    <!-- #ifdef H5 -->
    <view class="custom-header">
      <view class="header-title">个人中心</view>
    </view>
    <!-- #endif -->

    <!-- 内容区域 -->
    <view :class="['content', { 'h5-container': isH5 }]" @touchmove.prevent>
      <!-- 用户信息卡片 -->
      <view class="user-card">
        <view class="avatar">
          <image :src="baseURL.serverUrl + 'img/user2-160x160.jpg' || '' " mode="aspectFill" class="avatar-image"></image>
        </view>
        <view class="user-details">
          <text class="user-name">{{ userInfo && userInfo.userId || '' }}</text>
          <text class="user-account">{{ userInfo && userInfo.userId || '' }}</text>
        </view>
      </view>

      <!-- 服务卡片 -->
      <view class="section">
        <view class="section-title">
          <text>服务中心</text>
        </view>

        <!-- 服务网格 -->
        <view class="service-grid">
          <view class="service-item" @click="navigateToProfileSetting">
            <view class="service-icon">
              <!-- 统一使用图片图标 -->
              <image src="@/static/icons/profile-active.png" mode="aspectFit" class="icon-image"></image>
            </view>
            <text class="service-name">个人资料</text>
          </view>

          <view class="service-item" @click="navigateToPasswordSetting">
            <view class="service-icon">
              <!-- 统一使用图片图标 -->
              <image src="@/static/icons/password.png" mode="aspectFit" class="icon-image"></image>
            </view>
            <text class="service-name">修改密码</text>
          </view>

          <view class="service-item">
            <view class="service-icon">
              <!-- 统一使用图片图标 -->
              <image src="@/static/icons/view.png" mode="aspectFit" class="icon-image"></image>
            </view>
            <text class="service-name">关于我们</text>
          </view>
        </view>
      </view>

      <!-- 设置卡片 -->
      <view class="section">
        <view class="section-title">
          <text>设置</text>
        </view>

        <view class="settings-list">
          <view class="settings-item">
            <view class="settings-left">
              <view class="settings-icon">
                <!-- 统一使用图片图标 -->
                <image src="@/static/icons/manage-active.png" mode="aspectFit" class="icon-image-small"></image>
              </view>
              <text class="settings-text">系统设置</text>
            </view>
            <view class="settings-arrow">
              <!-- 统一使用图片图标 -->
              <image src="@/static/icons/arrow-right.png" mode="aspectFit" style="width: 16px; height: 16px;"></image>
            </view>
          </view>

          <view class="settings-item">
            <view class="settings-left">
              <view class="settings-icon">
                <!-- 统一使用图片图标 -->
                <image src="@/static/icons/password.png" mode="aspectFit" class="icon-image-small"></image>
              </view>
              <text class="settings-text">隐私政策</text>
            </view>
            <view class="settings-arrow">
              <!-- 统一使用图片图标 -->
              <image src="@/static/icons/arrow-right.png" mode="aspectFit" style="width: 16px; height: 16px;"></image>
            </view>
          </view>
          
        </view>
      </view>

      <!-- 版本信息 -->
      <view class="version-info">
        <text>版本 {{ appVersion }}</text>
      </view>

      <!-- 退出登录按钮 -->
      <view class="logout-btn" @click="handleLogout">
        <text class="logout-text">退出登录</text>
      </view>
    </view>
  </view>
</template>

<script setup>
import { onMounted, computed, ref, watch } from 'vue';
import baseURL from '@/config/index';
import { useUserStore } from '@/store/user';

// 初始化用户store
const userStore = useUserStore();

// 用户信息
const userInfo = ref(userStore.userInfo);

// 添加对userStore.userInfo的监听
watch(() => userStore.userInfo, (newUserInfo) => {
  userInfo.value = newUserInfo;
}, { immediate: true });

// APP版本号
const appVersion = ref('1.0.0');

// 判断是否为H5环境
const isH5 = computed(() => {
  // #ifdef H5
  return true;
  // #endif

  // #ifndef H5
  return false;
  // #endif
});

// 页面导航
const navigateTo = (url) => {
  uni.switchTab({
    url: url
  });
};

// 获取APP版本信息
const getAppVersion = () => {
  // #ifdef APP-PLUS
  // APP环境下，使用plus获取应用版本号
  plus.runtime.getProperty(plus.runtime.appid, (widgetInfo) => {
    appVersion.value = widgetInfo.version;
  });
  // #endif

  // #ifdef H5
  // H5环境下，可以使用一个默认版本或从配置中获取
  appVersion.value = '1.0.0 (Web)';
  // #endif

  // #ifdef MP
  // 小程序环境
  const accountInfo = uni.getAccountInfoSync();
  if (accountInfo && accountInfo.miniProgram) {
    appVersion.value = accountInfo.miniProgram.version || '1.0.0';
  }
  // #endif
};

// 生命周期
onMounted(() => {
  // 禁用页面滚动
  uni.setPageMeta({
    pageStyle: {
      disableScroll: true
    }
  });

  // 检查登录状态
  checkLogin();

  // 获取APP版本信息
  getAppVersion();
});

// 检查登录状态
const checkLogin = () => {
  // 使用userStore的isLoggedIn getter
  const isLoggedIn = userStore.isLoggedIn;
  
  // console.log('个人中心页面检查登录状态:', isLoggedIn);
  
  if (!isLoggedIn) {
    console.log('未检测到登录状态，准备跳转到登录页');
    
    // 添加延迟，确保store状态已更新
    setTimeout(() => {
      // 再次检查，防止状态更新延迟
      if (!userStore.isLoggedIn) {
        uni.navigateTo({
          url: '/pages/login/login'
        });
      }
    }, 100);
  }
};

// 处理退出登录
const handleLogout = () => {
  // 跳过store中的确认对话框，直接在这里显示确认框
  uni.showModal({
    title: '提示',
    content: '确定要退出登录吗？',
    success: (res) => {
      if (res.confirm) {
        // 直接清除用户信息和token
        userStore.clearUserInfo();

        // 显示提示
        uni.showToast({
          title: '已退出登录',
          icon: 'success',
          duration: 2000
        });

        // 跳转到登录页
        setTimeout(() => {
          uni.reLaunch({
            url: '/pages/login/login'
          });
        }, 1000);
      }
    }
  });
};

// 处理个人资料设置
const navigateToProfileSetting = () => {
  uni.navigateTo({
    url: '/pages/profile/settingProfile'
  });
};

// 处理密码设置
const navigateToPasswordSetting = () => {
  uni.navigateTo({
    url: '/pages/profile/settingPwd'
  });
};
</script>

<style scoped>
page {
  height: 100vh;
  overflow: hidden;
  position: fixed;
  width: 100%;
  touch-action: none;
}

/* 容器样式 */
.container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f7f7f7;
  position: relative;
  box-sizing: border-box;
  padding-bottom: 13%;
  touch-action: none;
}

/* 仅在非H5环境下添加顶部内边距 */
/* #ifndef H5 */
.container {
  padding-top: var(--window-top);
}

/* #endif */

/* 标题栏样式 */
.custom-header {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 44px;
  background-color: #FFFFFF;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 999;
  box-shadow: 0 1px 5px rgba(0, 0, 0, 0.1);
}

/* #ifdef APP-PLUS */
.header,
.custom-header {
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.12) !important;
}

/* #endif */

.header-title {
  color: #333;
  font-size: 16px;
  font-weight: 500;
}

/* H5环境下的容器样式 */
.h5-container {
  padding-top: 44px;
}

/* 内容区域 */
.content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: auto;
  position: relative;
  box-sizing: border-box;
  justify-content: flex-start;
  align-items: stretch;
  padding: 3%;
  background-color: #f7f7f7;
  -webkit-overflow-scrolling: touch;
  padding-bottom: 10%;
}

/* 用户卡片 */
.user-card {
  background: linear-gradient(135deg, #0c873d, #0db055);
  border-radius: 12px;
  padding: 5%;
  margin-bottom: 4%;
  box-shadow: 0 4px 12px rgba(12, 135, 61, 0.2);
  display: flex;
  align-items: center;
}

/* 头像样式 */
.avatar {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.2);
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 16px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  flex-shrink: 0;
  overflow: hidden;
}

.avatar-image {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  object-fit: cover;
}

.user-details {
  flex: 1;
  min-width: 0;
  /* 防止子元素溢出 */
}

.user-name {
  font-size: 18px;
  font-weight: 500;
  color: white;
  margin-bottom: 4px;
  display: block;
  word-break: break-word;
}

.user-account {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.85);
  display: block;
}

/* 区域公共样式 */
.section {
  margin-bottom: 1.5%;
  width: 100%;
}

.section-title {
  margin: 3% 0 2%;
  padding-left: 8px;
  border-left: 3px solid #0c873d;
}

.section-title text {
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

/* 服务网格 */
.service-grid {
  display: flex;
  justify-content: space-between;
  width: 100%;
}

.service-item {
  flex: 1;
  padding: 4% 2%;
  background-color: #fff;
  border-radius: 10px;
  margin: 0 1%;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
  display: flex;
  flex-direction: column;
  align-items: center;
  transition: all 0.3s ease;
}

.service-item:first-child {
  margin-left: 0;
}

.service-item:last-child {
  margin-right: 0;
}

.service-item:active {
  transform: scale(0.98);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.service-icon {
  margin-bottom: 3%;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 48px;
  height: 48px;
  background-color: rgba(12, 135, 61, 0.08);
  border-radius: 24px;
}

.icon-image {
  width: 24px;
  height: 24px;
}

.icon-image-small {
  width: 18px;
  height: 18px;
}

.service-name {
  font-size: 14px;
  color: #333;
  font-weight: 500;
  text-align: center;
}

/* 设置列表 */
.settings-list {
  background-color: #fff;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
  width: 100%;
  margin-bottom: 2%;
}

.settings-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 3.5% 4%;
  border-bottom: 1px solid #f5f5f5;
}

.settings-item:last-child {
  border-bottom: none;
}

.settings-left {
  display: flex;
  align-items: center;
}

.settings-icon {
  margin-right: 12px;
}

.settings-text {
  font-size: 15px;
  color: #333;
}

.settings-arrow {
  color: #bbb;
}

/* 版本信息 */
.version-info {
  text-align: center;
  margin: 2% 0;
  color: #999;
  font-size: 12px;
  width: 100%;
}

/* 退出登录按钮 */
.logout-btn {
  background-color: #fff;
  border-radius: 10px;
  padding: 3.5%;
  text-align: center;
  margin: 2% 0 8% 0;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
  width: 100%;
  box-sizing: border-box;
}

.logout-text {
  font-size: 16px;
  color: #f03838;
  font-weight: 500;
}

/* 小屏幕设备适配 (iPhone SE等) */
@media screen and (max-height: 667px) {
  .content-container {
    padding-bottom: 15%;
  }

  .avatar {
    width: 50px;
    height: 50px;
    border-radius: 25px;
  }

  .avatar-text {
    font-size: 20px;
  }

  .service-icon {
    width: 40px;
    height: 40px;
    border-radius: 20px;
    margin-bottom: 2%;
  }

  .service-item {
    padding: 3% 1.5%;
  }

  .service-name {
    font-size: 12px;
  }

  .settings-item {
    padding: 3% 4%;
  }

  .version-info {
    margin: 3% 0;
  }

  .logout-btn {
    margin-bottom: 7%;
  }
}
</style>